[{"reportMerchantNo": "561254278", "tradeAuthDirList": [], "appIdList": [{"appId": "wx9610b6be9f4dd0ae", "appSecret": null, "appIdType": "MINI_PROGRAM", "subscribeAppId": null, "status": "SUCCESS", "failReason": null}, {"appId": "wx9a95331605fc5eb5", "appSecret": null, "appIdType": "MINI_PROGRAM", "subscribeAppId": null, "status": "SUCCESS", "failReason": null}, {"appId": "wxbb48bac536053072", "appSecret": null, "appIdType": "MINI_PROGRAM", "subscribeAppId": null, "status": "SUCCESS", "failReason": null}, {"appId": "wx379b62a3cc54f35e", "appSecret": null, "appIdType": "MINI_PROGRAM", "subscribeAppId": null, "status": "SUCCESS", "failReason": null}, {"appId": "wx1efe8d202b154ebf", "appSecret": null, "appIdType": "MINI_PROGRAM", "subscribeAppId": null, "status": "SUCCESS", "failReason": null}, {"appId": "wxd53b51a91b79f546", "appSecret": null, "appIdType": "MINI_PROGRAM", "subscribeAppId": null, "status": "SUCCESS", "failReason": null}, {"appId": "wx3fec602eb6c6e435", "appSecret": null, "appIdType": "MINI_PROGRAM", "subscribeAppId": null, "status": "FAILED", "failReason": "该APPID认证主体与特约商户、所属渠道商或服务商主体不一致，请检查后再试"}, {"appId": "wx4742f3ec5eb75746", "appSecret": null, "appIdType": "MINI_PROGRAM", "subscribeAppId": null, "status": "FAILED", "failReason": "该APPID认证主体与特约商户、所属渠道商或服务商主体不一致，请检查后再试"}, {"appId": "wx72a9f83beb183f87", "appSecret": null, "appIdType": "MINI_PROGRAM", "subscribeAppId": null, "status": "FAILED", "failReason": "当前已配置subappid个数已达上限，如需额外配置，请联系客服处理"}]}, {"reportMerchantNo": "588868829", "tradeAuthDirList": [], "appIdList": [{"appId": "wx9610b6be9f4dd0ae", "appSecret": null, "appIdType": "MINI_PROGRAM", "subscribeAppId": null, "status": "SUCCESS", "failReason": null}, {"appId": "wx9a95331605fc5eb5", "appSecret": null, "appIdType": "MINI_PROGRAM", "subscribeAppId": null, "status": "SUCCESS", "failReason": null}, {"appId": "wxbb48bac536053072", "appSecret": null, "appIdType": "MINI_PROGRAM", "subscribeAppId": null, "status": "SUCCESS", "failReason": null}, {"appId": "wx379b62a3cc54f35e", "appSecret": null, "appIdType": "MINI_PROGRAM", "subscribeAppId": null, "status": "SUCCESS", "failReason": null}, {"appId": "wx3fec602eb6c6e435", "appSecret": null, "appIdType": "MINI_PROGRAM", "subscribeAppId": null, "status": "FAILED", "failReason": "该APPID认证主体与特约商户、所属渠道商或服务商主体不一致，请检查后再试"}, {"appId": "wx4742f3ec5eb75746", "appSecret": null, "appIdType": "MINI_PROGRAM", "subscribeAppId": null, "status": "FAILED", "failReason": "该APPID认证主体与特约商户、所属渠道商或服务商主体不一致，请检查后再试"}, {"appId": "wxd53b51a91b79f546", "appSecret": null, "appIdType": "MINI_PROGRAM", "subscribeAppId": null, "status": "FAILED", "failReason": "当前已配置subappid个数已达上限，如需额外配置，请联系客服处理"}, {"appId": "wx72a9f83beb183f87", "appSecret": null, "appIdType": "MINI_PROGRAM", "subscribeAppId": null, "status": "FAILED", "failReason": "当前已配置subappid个数已达上限，如需额外配置，请联系客服处理"}]}, {"reportMerchantNo": "558053121", "tradeAuthDirList": [], "appIdList": [{"appId": "wx9610b6be9f4dd0ae", "appSecret": null, "appIdType": "MINI_PROGRAM", "subscribeAppId": null, "status": "SUCCESS", "failReason": null}, {"appId": "wx9a95331605fc5eb5", "appSecret": null, "appIdType": "MINI_PROGRAM", "subscribeAppId": null, "status": "SUCCESS", "failReason": null}, {"appId": "wxbb48bac536053072", "appSecret": null, "appIdType": "MINI_PROGRAM", "subscribeAppId": null, "status": "SUCCESS", "failReason": null}, {"appId": "wx379b62a3cc54f35e", "appSecret": null, "appIdType": "MINI_PROGRAM", "subscribeAppId": null, "status": "SUCCESS", "failReason": null}, {"appId": "wx3fec602eb6c6e435", "appSecret": null, "appIdType": "MINI_PROGRAM", "subscribeAppId": null, "status": "FAILED", "failReason": "该APPID认证主体与特约商户、所属渠道商或服务商主体不一致，请检查后再试"}, {"appId": "wx4742f3ec5eb75746", "appSecret": null, "appIdType": "MINI_PROGRAM", "subscribeAppId": null, "status": "FAILED", "failReason": "该APPID认证主体与特约商户、所属渠道商或服务商主体不一致，请检查后再试"}, {"appId": "wxd53b51a91b79f546", "appSecret": null, "appIdType": "MINI_PROGRAM", "subscribeAppId": null, "status": "FAILED", "failReason": "当前已配置subappid个数已达上限，如需额外配置，请联系客服处理"}, {"appId": "wx72a9f83beb183f87", "appSecret": null, "appIdType": "MINI_PROGRAM", "subscribeAppId": null, "status": "FAILED", "failReason": "当前已配置subappid个数已达上限，如需额外配置，请联系客服处理"}]}]