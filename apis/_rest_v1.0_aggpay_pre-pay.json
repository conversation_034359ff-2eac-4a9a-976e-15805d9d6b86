{"openapi": "3.0.1", "info": {"title": "聚合支付", "version": "1.0"}, "tags": [{"name": "aggpay", "description": "<p>提供聚合支付相关服务（支付下单、公众号配置等功能</p>"}], "servers": [{"url": "https://openapi.yeepay.com/yop-center", "description": "生产环境"}, {"url": "https://sandbox.yeepay.com/yop-center", "description": "沙箱环境"}], "security": [{"YOP-SM2-SM3_aggpay": []}, {"YOP-RSA2048-SHA256_aggpay": []}], "paths": {"/rest/v1.0/aggpay/pre-pay": {"post": {"tags": ["aggpay"], "summary": "聚合支付统一下单", "description": "\n", "operationId": "aggpay_prePay", "security": [{"YOP-SM2-SM3_aggpay": []}, {"YOP-RSA2048-SHA256_aggpay": []}], "parameters": [{"name": "x-yop-request-id", "in": "header", "description": "<p>请求唯一标识，每笔请求不同，推荐使用 UUID</p>", "required": true, "schema": {"title": "请求标识", "maxLength": 64, "type": "string"}}, {"name": "x-yop-appkey", "in": "header", "description": "", "required": true, "schema": {"title": "应用标识", "maxLength": 32, "type": "string"}}, {"name": "x-yop-sdk-langs", "in": "header", "description": "sdk 语言(如：java/python/go)", "required": false, "schema": {"title": "SDK语言", "maxLength": 64, "type": "string", "example": "java"}}, {"name": "x-yop-sdk-version", "in": "header", "description": "sdk 版本号，自实现目前统一使用 4.0.0", "required": true, "schema": {"title": "SDK版本号", "maxLength": 64, "type": "string"}}], "requestBody": {"content": {"application/x-www-form-urlencoded": {"schema": {"required": ["channel", "payWay", "userIp"], "type": "object", "properties": {"parentMerchantNo": {"title": "发起方商户编号", "maxLength": 11, "type": "string", "description": "<p>发起方商户编号。<br />*标准商户收付款方案中此参数与收款商户编号一致；<br />*平台商户收付款方案中此参数为平台商商户编号；<br />*服务商解决方案中，①标准商户收款时，该参数为标准商户商编 ②平台商收款或平台商入驻商户收款时，该参数为平台商商编。 <br />如果商户使用先下单再支付模式，已传入token，则本字段可以不传值，否则必须传值</p>", "example": "10012426765", "x-yop-api-param-condition": {"conditionRequired": false, "type": "PLAIN", "value": ""}}, "merchantNo": {"title": "商户编号", "maxLength": 11, "type": "string", "description": "<p>商户编号 收单主体商编，平台商或服务商下的子商户，普通特约商户 如果商户使用先下单再支付模式，已传入token，则本字段可以不传值，否则必须传值</p>", "example": "10012426765", "x-yop-api-param-condition": {"conditionRequired": true, "type": "PLAIN", "value": " 先下单后支付时无需传入"}}, "orderId": {"title": "商户收款请求号", "maxLength": 64, "type": "string", "description": "<p>商户收款请求号<br />商户系统内部生成的订单号，需要保持在同一个商户下唯一<br />未传入token时必填</p>", "example": "ORDER202401010515217305372872", "x-yop-api-param-condition": {"conditionRequired": true, "type": "PLAIN", "value": " 先下单后支付时无需传入"}}, "orderAmount": {"title": "订单金额", "maximum": 999999999999, "minimum": 0.01, "type": "number", "description": "<p>订单金额<br />业务上是必须参数，单位： 元， 两位小数， 最低 0.01<br />未传入token时必填</p>", "example": 0.01, "x-yop-api-param-condition": {"conditionRequired": true, "type": "PLAIN", "value": " 先下单后支付时无需传入"}}, "expiredTime": {"title": "订单截止时间", "maxLength": 32, "type": "string", "description": "<p>订单截止时间<br />格式\"yyyy-MM-dd HH:mm:ss\"不传默认一天</p>", "format": "date-time", "x-yop-api-param-condition": {"conditionRequired": false, "type": "PLAIN", "value": "先下单后支付时无需传入"}}, "notifyUrl": {"title": "支付结果通知地址", "maxLength": 200, "type": "string", "description": "<p>接收支付结果的通知地址<br />请参考<a href=\"#anchor7\">结果通知详情</a></p>", "format": "notify-url", "example": "https://notify.merchant.com/xxx", "x-yop-api-param-condition": {"conditionRequired": false, "type": "PLAIN", "value": ""}}, "redirectUrl": {"title": "页面回调地址", "maxLength": 200, "type": "string", "description": "<p>页面回调地址。如使用易宝收银台需要上送前端页面回调地址</p>", "example": "https://notify.merchant.com/xxx", "x-yop-api-param-condition": {"conditionRequired": true, "type": "PLAIN", "value": "使用给易宝收银台时必填"}}, "memo": {"title": "对账备注", "maxLength": 256, "type": "string", "description": "<p>对账备注<br />商户自定义参数，会展示在交易对账单中,支持85个字符（中文或者英文字母）</p>", "x-yop-api-param-condition": {"conditionRequired": false, "type": "PLAIN", "value": ""}}, "goodsName": {"title": "商品名称", "maxLength": 85, "type": "string", "description": "<p>商品名称，简单描述订单信息或商品简介，用于展示在收银台页面或者支付明细中<br />未传入token时必填<br />特殊说明：<br />1、当商品名称超过85个字符时，易宝会默认保留前85个字符<br />2、由于微信渠道侧限制，如微信支付时该字段将自动截取前42个字符送渠道</p>", "example": "旺仔牛奶", "x-yop-api-param-condition": {"conditionRequired": false, "type": "PLAIN", "value": ""}}, "fundProcessType": {"title": "分账订单标记", "maxLength": 32, "type": "string", "description": "<p>分账订单标记<br />可选项如下:<br />DELAY_SETTLE:需要分账<br />REAL_TIME:不需要分账<br />REAL_TIME_DIVIDE:实时分账;需同时传入divideDetail</p>", "example": "REAL_TIME", "x-yop-api-param-condition": {"conditionRequired": false, "type": "PLAIN", "value": ""}}, "payWay": {"title": "支付方式", "maxLength": 32, "type": "string", "description": "<p>可选项如下:<br />USER_SCAN:用户扫码<br />MINI_PROGRAM:小程序支付<br />WECHAT_OFFIACCOUNT:微信公众号<br />ALIPAY_LIFE:支付宝生活号<br />JS_PAY:JS支付<br />SDK_PAY:SDK支付<br />H5_PAY:H5支付<br />UNCONSCIOUS_PAY:云微无感支付<br />DIRECT_PAY:云微直接支付</p>", "example": "USER_SCAN", "x-yop-api-param-condition": {"conditionRequired": false, "type": "PLAIN", "value": ""}}, "channel": {"title": "渠道类型", "maxLength": 32, "type": "string", "description": "<p>渠道类型<br />可选项如下:<br />WECHAT:微信<br />ALIPAY:支付宝<br />UNIONPAY:银联云闪付<br />DCEP:数字人民币</p>", "example": "ALIPAY", "x-yop-api-param-condition": {"conditionRequired": false, "type": "PLAIN", "value": ""}}, "scene": {"title": "场景", "maxLength": 32, "type": "string", "description": "<p>场景channel为WECHAT时，可选值ONLINE/OFFLINE/BAOXIAN/GONGYI/DC_SEPARATION/DIGITAL/REGISTRATION/PRIVATE_EDUCATION/DIRECT/STORE_ASST；<br />channel为ALIPAY时，可选值OFFLINE/LARGE/REGISTRATION；<br />channel为UNIONPAY时非必填；<br />可选项如下:<br />ONLINE:线上<br />OFFLINE:线下<br />BAOXIAN:保险<br />GONGYI:公益<br />DC_SEPARATION:借贷分离<br />DIGITAL:数娱<br />REGISTRATION:报名(需要先优惠费率报名成功，否则会阻断交易)<br />PRIVATE_EDUCATION:民办教育<br />DIRECT:直连<br />LARGE:特殊<br />STORE_ASST:门店助手</p>", "example": "OFFLINE", "x-yop-api-param-condition": {"conditionRequired": true, "type": "PLAIN", "value": "渠道类型不是银联"}}, "appId": {"title": "微信公众号ID", "maxLength": 64, "type": "string", "description": "<p>微信公众号ID/微信小程序ID/支付宝小程序ID商家的公众号id，需保证传入的openId是基于该appId获取，微信公众号、微信小程序和支付宝小程序支付需要</p>", "example": "wx012574bf7bc1836d", "x-yop-api-param-condition": {"conditionRequired": true, "type": "PLAIN", "value": "微信小程序和支付宝小程序支付必填，微信公众号支付且已完成商户appid配置的必填"}}, "userId": {"title": "用户ID", "maxLength": 128, "type": "string", "description": "<p>用户ID用户标识，微信公众号/微信小程序为用户的openId，支付宝生活号/支付宝小程序/银联JS支付为用户的userId 微信公众号/小程序、支付宝生活号/小程序支付时必填；银联JS支付时，与userAuthCode二选一必填<br />详见<a href=\"https://open.yeepay.com/docs/products/bzshsfk/others/65f26eb04cf2f6004e026731\">聚合统一下单各userid获取方法</a></p>", "example": "**************** 、olkcn7obuausx40U8TjVj-5QEdT7", "x-yop-api-param-condition": {"conditionRequired": true, "type": "PLAIN", "value": "微信公众号/小程序、支付宝生活号/小程序支付时必填；银联JS支付时，与userAuthCode二选一"}}, "userIp": {"title": "用户IP", "maxLength": 128, "type": "string", "description": "<p>用户真实IP地址<br />已获批开通账户通的平台商，若accountLinkInfo不为空，此userIp必须与accountLinkInfo中的token所包含的IP一致</p>", "example": "***********", "x-yop-api-param-condition": {"conditionRequired": false, "type": "PLAIN", "value": ""}}, "channelSpecifiedInfo": {"title": "渠道指定支付信息", "maxLength": 1024, "type": "string", "description": "<p>渠道指定支付信息，json格式<br />参考<a href=\"https://open.yeepay.com/docs/products/fwssfk/others/5f59fc1720289f001ba82528/67d94d72a9a59b000119fb71\">渠道指定支付信息</a></p>", "example": "{\"hbFqNum\":\"3\",\"hbFqSellerPercent\":\"0\",\"sysServiceProviderId\":\"\"}", "x-yop-api-param-condition": {"conditionRequired": true, "type": "PLAIN", "value": "信用分支付和微信B2B必填"}}, "channelPromotionInfo": {"title": "渠道优惠信息", "maxLength": 1024, "type": "string", "description": "<p>渠道优惠信息<br />订单需参与渠道指定优惠时传入，如微信单品券<br /><a href=\"https://open.yeepay.com/docs/v2/products/fwssfk/others/5f59fc1720289f001ba82528/5fed4bc56d313b001b79abfd/index.html\">渠道优惠信息JSON说明&lt;/a &gt;</a></p>", "x-yop-api-param-condition": {"conditionRequired": false, "type": "PLAIN", "value": ""}}, "identityInfo": {"title": "限制付款人信息", "maxLength": 512, "type": "string", "description": "<p>限制付款人信息<br />特殊行业需要支付实名场景下使用，如保险的特殊险种需要付款人和投保人实名。支持微信、支付宝，详见：<br /><a href=\"https://open.yeepay.com/docs/products/bzshsfk/others/5f3cefa0a92810001be76426/63a1b711f9a122006f1b5379\">聚合限制付款人信息json说明</a></p>", "example": "{\"identityVerifyType\":\"Y\",\"payerIdType\":\"IDENTITY_CARD\",\"payerNumber\":\"234512198006252456\",\"payerName\":\"名字\"}", "x-yop-api-param-condition": {"conditionRequired": false, "type": "PLAIN", "value": ""}}, "limitCredit": {"title": "是否限制贷记卡", "maxLength": 16, "type": "string", "description": "<p>是否限制贷记卡<br />Y:仅借记卡可以支付<br />N:借贷记卡均可支付</p>", "example": "N", "x-yop-api-param-condition": {"conditionRequired": false, "type": "PLAIN", "value": ""}}, "uniqueOrderNo": {"title": "易宝订单号", "maxLength": 64, "type": "string", "description": "<p>易宝订单号<br />如果商户使用先下单再支付模式，请传入下单接口返回的易宝订单号<br />同时传入易宝订单号和token，将以易宝订单号为准</p>", "example": "1012202101070000001989946571", "x-yop-api-param-condition": {"conditionRequired": true, "type": "PLAIN", "value": "商户先下单后支付时，与token二选一必填"}}, "token": {"title": "token", "maxLength": 64, "type": "string", "description": "<p>对应参数通过易宝接口&ldquo;交易下单&rdquo;的响应获取。如果商户使用先下单再支付模式，请传入下单接口返回的token。</p>", "example": "83BCDF29CFACB4411533080B67864EF8C907CCDC5E10A707C285FEA10CDB8221", "x-yop-api-param-condition": {"conditionRequired": true, "type": "PLAIN", "value": "商户先下单后支付时，与uniqueOrderNo二选一必填"}}, "csUrl": {"title": "清算回调地址", "maxLength": 200, "type": "string", "description": "<p>清算成功服务器回调地址，不传则不通知。详见<a href=\"https://open.yeepay.com/docs/products/ptssfk/spis/5fc9ae6896818f001b300bce\">清算结果通知</a></p>", "x-yop-api-param-condition": {"conditionRequired": false, "type": "PLAIN", "value": ""}}, "accountLinkInfo": {"title": "合作银行信息", "maxLength": 512, "type": "string", "description": "<p>合作银行信息：<br />JSON字符串；已获批开通账户通的平台商可传入此字段，否则请勿传值<br />accountProvider:合作银行<br />token: 商户与银行约定令牌</p>", "example": "{\"accountProvider\":\"BOL\",\"token\":\"xxx\"}", "x-yop-api-param-condition": {"conditionRequired": false, "type": "PLAIN", "value": ""}}, "ypPromotionInfo": {"title": "易宝营销信息", "maxLength": 1024, "type": "string", "description": "<p>易宝营销信息json格式；需要参加易宝营销活动的可传入此值；如需参加营销活动，请先联系易宝运营进行配置，否则传入不生效，按无营销活动支付<br /><br />amount:营销金额（自定义补贴商户时，不需要传参）<br />type:营销类型<br /><br />营销类型枚举值：<br />CUSTOM_REDUCTION:自定义用户支付立减<br />用户支付金额=订单金额-自定义立减金额<br />订单入账金额=订单金额<br />CUSTOM_ALLOWANCE:自定义补贴商户（自定义补贴商户需要在支付清算完成后，发起调用补贴申请）<br />用户支付金额=订单金额<br />订单入账金额=订单金额+自定义补贴金额</p>", "example": "自定义支付立减：[{\"amount\":\"0.01\",\"type\":\"CUSTOM_REDUCTION\"}],自定义补贴商户[{\"type\":\"CUSTOM_ALLOWANCE\"}]", "x-yop-api-param-condition": {"conditionRequired": false, "type": "PLAIN", "value": ""}}, "bankCode": {"title": "银行编码", "maxLength": 32, "type": "string", "description": "<p>银行编码<br />渠道类型（channel）为数字人民币（DCEP）时必填<br />BOC:中国银行<br />ICBC:工商银行<br /><a href=\"https://open.yeepay.com/docs/products/bzshsfk/others/5ffe9c3e46ceb3001bc286c6\">银行编码与名称</a></p>", "example": "BOC", "x-yop-api-param-condition": {"conditionRequired": true, "type": "PLAIN", "value": "渠道类型为数字人民币时必填"}}, "businessInfo": {"title": "自定义参数信息", "maxLength": 1024, "type": "string", "description": "<p>自定义参数信息<br /><a href=\"https://open.yeepay.com/docs/products/ptssfk/others/5f3cef4420289f001ba82523/64d5e1de964ee6006f0bfdd0\">业务自定义信息json说明</a></p>", "x-yop-api-param-condition": {"conditionRequired": false, "type": "PLAIN", "value": ""}}, "userAuthCode": {"title": "userAuthCode", "maxLength": 64, "type": "string", "description": "<p>用户授权码，银联JS支付时，与userId二选一必填</p>", "x-yop-api-param-condition": {"conditionRequired": false, "type": "PLAIN", "value": ""}}, "channelActivityInfo": {"title": "渠道活动信息", "maxLength": 512, "type": "string", "description": "<p>渠道活动信息，可供支付宝扫码点餐场景使用<br />food_order_type：扫码点餐场景码<br />枚举值： <br />SELF_PICK:门店自提<br />TAKE_OUT:餐饮外卖<br />QR_FOOD_ORDER:扫码点餐<br />P_QR_FOOD_ORDER:点餐后付<br />LINE_UP:排队订单<br />FOOD_ORDER:餐厅预订</p>", "example": "{\"food_order_type\":\"QR_FOOD_ORDER\"}", "x-yop-api-param-condition": {"conditionRequired": false, "type": "PLAIN", "value": ""}}, "terminalId": {"title": "terminalId", "maxLength": 32, "type": "string", "description": "<p>若交易为线下面对面场景，通过终端发起，请传入终端ID（ID由商户自定义，与终端一一对应）</p>", "x-yop-api-param-condition": {"conditionRequired": true, "type": "PLAIN", "value": "POS终端发起交易时必填"}}, "terminalSceneInfo": {"title": "商家终端场景信息", "maxLength": 512, "type": "string", "description": "<p>商家终端场景信息，非必传，可不传。具体请参考示例值</p>", "example": "{\"storeId\":\"门店id\",\"storeName\":\"门店名称\",\"operatorId\":\"商户操作员编号\",\"alipayStoreId\":\"支付宝的店铺编号\",\"areaCode\":\"门店行政区划码\",\"address\":\"门店详细地址\"}", "x-yop-api-param-condition": {"conditionRequired": false, "type": "PLAIN", "value": ""}}, "ypAccountBookNo": {"title": "记账簿编号", "maxLength": 32, "type": "string", "description": "<p>记账簿编号<br />支持收款至预收账户<br />记账簿编号不为空时，fundProcessType必须上送REAL_TIME</p>", "x-yop-api-param-condition": {"conditionRequired": false, "type": "PLAIN", "value": ""}}, "terminalInfo": {"title": "终端信息", "maxLength": 128, "type": "string", "description": "<p>终端信息<br />需要在商户后台查询网点信息和交易账单上需要展示网点信息的商户，需要传入此值，否则请勿传值！！！<br />shopName：网点名称<br />shopCustomerNumber：网点编号</p>", "example": "{\"shopName\":\"网点名称\",\"shopCustomerNumber\":\"网点编号\"}", "x-yop-api-param-condition": {"conditionRequired": false, "type": "PLAIN", "value": ""}}, "productInfo": {"title": "易宝营销产品信息", "maxLength": 512, "type": "string", "description": "<p>易宝营销产品信息<br />本次订单可用的单品券id列表，最大长度512</p>", "example": "[{\"id\":\"random_reduction_pro\"}]", "x-yop-api-param-condition": {"conditionRequired": false, "type": "PLAIN", "value": ""}}, "divideDetail": {"title": "分账明细", "maxLength": 1000, "type": "string", "description": "<p>分账明细<br />fundProcessType为实时分账时，必传。JSON格式：<br />ledgerNo 分账接收方<br />amount 分账金额<br />实时分账的情况下，分账商编无需传入收单商编，除去分给他人的金额，订单剩余可分账金额均会分账给收单商户。</p>", "example": "[{\"amount\":\"金额\",\"ledgerNo\":\"分账商编\",\"divideDetailDesc\":\"分账说明\"}]", "x-yop-api-param-condition": {"conditionRequired": true, "type": "PLAIN", "value": "分账订单标记为实时分账时必填"}}, "divideNotifyUrl": {"title": "分账通知地址", "maxLength": 256, "type": "string", "description": "<p>分账通知地址<br />分账成功服务器回调地址，不传则不通知</p>", "format": "notify-url", "x-yop-api-param-condition": {"conditionRequired": false, "type": "PLAIN", "value": ""}}, "feeSubsidyInfo": {"title": "手续费补贴信息", "maxLength": 256, "type": "string", "description": "<p>手续费补贴信息<br />JSON ARRAY格式，只支持上传1条<br /><a href=\"https://open.yeepay.com/docs/products/ptssfk/others/5f3cef4420289f001ba82523/656ed87c9a76e60072dd728b\">手续费补贴信息json说明</a></p>", "example": "[{\"subsidyMerchantNo\":\"***********\",\"subsidyAccountType\":\"FEE_ACCOUNT\",\"subsidyType\":\"ABSOLUTE\",\"subsidyProportion\":\"\",\"subsidyCalculateType\":\"SINGLE_PERCENT\",\"subsidyPercentFee\":\"0.6\",\"subsidyFixedFee\":\"\",\"subsidySingleMaxFee\":\"\"}]", "x-yop-api-param-condition": {"conditionRequired": false, "type": "PLAIN", "value": ""}}, "agreementId": {"title": "协议ID", "maxLength": 64, "type": "string", "description": "<p>易宝商户自己生成的协议号<br />微信代扣、云微无感支付需传入此字段</p>", "x-yop-api-param-condition": {"conditionRequired": true, "type": "PLAIN", "value": "微信代扣、云微无感支付必填"}}, "creditOrderId": {"title": "信用分请求号", "maxLength": 64, "type": "string", "description": "<p>信用分请求号payWay：CREDIT_PAY时，必传</p>", "x-yop-api-param-condition": {"conditionRequired": true, "type": "PLAIN", "value": "信用分支付必填"}}, "payMedium": {"title": "支付媒介", "maxLength": 32, "type": "string", "description": "<p>PRECONSUME:预消费</p>", "x-yop-api-param-condition": {"conditionRequired": true, "type": "PLAIN", "value": "预消费交易必填"}}}}}}}, "responses": {"200": {"description": "正常响应", "content": {"application/json": {"schema": {"type": "object", "properties": {"result": {"$ref": "#/components/schemas/aggpay.OrderResponseDTO"}}}}}}, "500": {"description": "异常响应", "content": {"application/json": {"schema": {"type": "object", "properties": {"requestId": {"type": "string", "description": "请求标识"}, "code": {"type": "string", "description": "错误码"}, "message": {"type": "string", "description": "错误描述"}, "subCode": {"type": "string", "description": "子错误码"}, "subMessage": {"type": "string", "description": "子错误描述"}, "docUrl": {"type": "string", "description": "错误解决方案(点击查看明细)"}}}}}}}, "x-yop-apigateway-api-parameter-handling": "MAPPING", "x-yop-apigateway-api-type": "COMMON", "x-yop-apigateway-api-option-rule": [{"name": "IDEMPOTENT", "config": {"useAllParams": true, "hasBizError": false, "params": [], "supported": false, "bizErrorCode": ""}}, {"name": "SANDBOX", "config": {"supported": false}}, {"name": "UNIQUE_PARAMS", "config": {"supported": false}}, {"name": "SYNC_ASYNC", "config": {"invokeType": "SYNC"}}]}}}, "components": {"schemas": {"aggpay.OrderResponseDTO": {"title": "响应结果", "required": ["code", "message"], "type": "object", "properties": {"code": {"title": "返回码", "maxLength": 16, "type": "string"}, "message": {"title": "返回信息", "maxLength": 1024, "type": "string"}, "orderId": {"title": "商户收款请求号", "maxLength": 64, "type": "string"}, "uniqueOrderNo": {"title": "易宝收款订单号", "maxLength": 64, "type": "string"}, "bankOrderId": {"title": "渠道侧商户请求号", "maxLength": 64, "type": "string", "description": "<p>支付机构在微信侧的外部商户订单号，用于服务商用于点金计划商户小票功能</p>"}, "prePayTn": {"title": "预支付标识信息", "maxLength": 2048, "type": "string", "description": "<p>预支付标识信息 <a href=\"https://open.yeepay.com/docs/v2/products/ptssfk/others/5feb010d6d313b001b79abf8/index.html\">prePayTn使用方式</a></p>"}}, "description": "请求成功返回00000", "x-yop-api-param-conditions": {}}}, "securitySchemes": {"YOP-SM2-SM3_aggpay": {"type": "<PERSON><PERSON><PERSON><PERSON>", "name": "Authorization", "in": "header", "x-yop-apigateway-auth-type": "YOP-SM2-SM3"}, "YOP-RSA2048-SHA256_aggpay": {"type": "<PERSON><PERSON><PERSON><PERSON>", "name": "Authorization", "in": "header", "x-yop-apigateway-auth-type": "YOP-RSA2048-SHA256"}}}, "x-yop-apigateway-group": "aggpay"}