```mermaid
sequenceDiagram
participant 用户
participant DocMgrFacadeImpl
participant ApiDocBizImpl
participant ApiDefinitionGenerator
participant ApiMdUtils
participant remoteStorage

    用户->>DocMgrFacadeImpl: publishChangedApis / publishDocMd / 触发API变更事件
    DocMgrFacadeImpl->>ApiDocBizImpl: publishChangedApis / publishDocMd
    ApiDocBizImpl->>ApiDefinitionGenerator: DocGeneratorFactory.get("api.definition").generate(api, lang)
    ApiDefinitionGenerator->>ApiMdUtils: new ApiMdUtils(apiDefinition, modelMap, errorCodes, callbacks)
    ApiDefinitionGenerator->>ApiMdUtils: convertToMarkdown()
    ApiMdUtils-->>ApiDefinitionGenerator: 返回Markdown内容
    ApiDefinitionGenerator->>remoteStorage: remoteStorage.update(md路径, Markdown内容)
    remoteStorage-->>ApiDefinitionGenerator: 存储成功
    ApiDefinitionGenerator-->>ApiDocBizImpl: 生成完成
    ApiDocBizImpl-->>DocMgrFacadeImpl: 处理完成
    DocMgrFacadeImpl-->>用户: 返回处理结果

```