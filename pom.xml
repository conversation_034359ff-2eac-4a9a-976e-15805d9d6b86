<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/maven-v4_0_0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <groupId>com.yeepay.janus</groupId>
    <artifactId>janus-parent</artifactId>
    <version>1.1-SNAPSHOT</version>
    <packaging>pom</packaging>
    <name>janus-parent</name>

    <parent>
        <groupId>com.yeepay.boot</groupId>
        <artifactId>yeepay-boot-parent</artifactId>
        <version>2.6.3-SNAPSHOT</version>
    </parent>

    <properties>
        <maven.compiler.target>1.8</maven.compiler.target>
        <maven.compiler.source>1.8</maven.compiler.source>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
        <project.reporting.outputEncoding>UTF-8</project.reporting.outputEncoding>

        <yeepay-boot.version>2.6.3-SNAPSHOT</yeepay-boot.version>
        <yeepay-lock.version>1.4.2-ssl-SNAPSHOT</yeepay-lock.version>
        <object-storage-sdk.version>2.0.4-SNAPSHOT</object-storage-sdk.version>
        <yeepay-persistence.version>1.0</yeepay-persistence.version>
        <yeepay-utils.version>2.3.5</yeepay-utils.version>
        <yeepay-boot-utils.version>2.6.4-SNAPSHOT</yeepay-boot-utils.version>
        <yeepay-boot-config.version>1.0-SNAPSHOT</yeepay-boot-config.version>
        <yeepay-infra-threadpool.version>2.4</yeepay-infra-threadpool.version>
        <rocketmq-spring-boot-starter.version>2.2.3</rocketmq-spring-boot-starter.version>
        <infra-log4j2.version>2.1.3</infra-log4j2.version>
        <yeepay-log4j2-pattern.verison>2.1.0</yeepay-log4j2-pattern.verison>
        <accesslog-valve.version>2.1.3</accesslog-valve.version>
        <fluent-log4j2-appender.version>2.1.1</fluent-log4j2-appender.version>
        <yeepay-scheduler-client-starter.version>1.4</yeepay-scheduler-client-starter.version>
        <yeepay-boot-event.version>2.6.3-SNAPSHOT</yeepay-boot-event.version>

        <druid.version>1.2.22</druid.version>
        <mybatis-plus-boot-starter.version>3.4.3.4</mybatis-plus-boot-starter.version>
        <mybatis-plus-generator.version>3.5.1</mybatis-plus-generator.version>
        <dynamic-datasource-spring-boot-starter.version>3.5.0</dynamic-datasource-spring-boot-starter.version>
        <jmh.version>1.33</jmh.version>
        <mapstruct.version>1.5.0.Beta2</mapstruct.version>
        <mapstruct-lombok.version>0.2.0</mapstruct-lombok.version>

        <thymeleaf.version>3.0.4.RELEASE</thymeleaf.version>
        <thymeleaf-layout-dialect.version>2.4.1</thymeleaf-layout-dialect.version>
        <lucene.version>7.6.0</lucene.version>
        <hibernate-validator.version>5.3.6.Final</hibernate-validator.version>
        <flexmark-all.version>0.62.2</flexmark-all.version>

        <!--swagger 3-->
        <swagger-core.version>2.0.10</swagger-core.version>
        <swagger-codegen-generators.version>1.0.13</swagger-codegen-generators.version>
        <swagger-codegen-v3.version>3.0.13</swagger-codegen-v3.version>

        <!-- yeepay-smartcache 基于jedis 2.XX.-->
        <yeepay-smartcache.version>1.5.2-ssl-SNAPSHOT</yeepay-smartcache.version>
        <jedis.version>2.9.0</jedis.version>
        <spring-data-redis.version>1.8.23.RELEASE</spring-data-redis.version>
        <spring-data-commons.version>1.13.23.RELEASE</spring-data-commons.version>
        <spring-data-keyvalue.version>1.2.23.RELEASE</spring-data-keyvalue.version>

        <yop-java-sdk.version>4.1.4</yop-java-sdk.version>

        <!--    升级dubbo3   -->
        <yeepay-rmi.version>3.1</yeepay-rmi.version>
        <yeepay-soa.version>3.4</yeepay-soa.version>
        <yeepay-soa-starter.version>4.0.1.RELEASE</yeepay-soa-starter.version>
        <curator.version>4.2.0</curator.version>
        <javassist.version>3.28.0-GA</javassist.version>
        <fluent-sender-base.version>2.1.2</fluent-sender-base.version>
        <netty-all.version>4.1.56.Final</netty-all.version>
        <protobuf-java.version>3.9.1</protobuf-java.version>
        <concurrentlinkedhashmap-lru.version>1.4.2</concurrentlinkedhashmap-lru.version>
        <zookeeper.version>3.4.8</zookeeper.version>
        <yeepay-utils-common.version>4.2.0</yeepay-utils-common.version>

        <json-path.version>2.9.0</json-path.version>
        <gson.version>2.8.9</gson.version>
    </properties>

    <modules>
        <module>yop-doc/yop-doc-center</module>
        <module>yop-doc/yop-doc-domain</module>
        <module>yop-doc/yop-doc-facade</module>
        <module>yop-doc/yop-doc-infrastructure</module>
        <module>yop-doc/yop-doc-core</module>
        <module>yop-doc/yop-doc-hessian</module>
        <module>yop-tools/yop-codegen-generator</module>
        <module>yop-doc/yop-doc-codegen-facade</module>
        <module>yop-doc/yop-doc-codegen</module>
        <module>yop-doc/yop-doc-i18n-facade</module>
        <module>yop-doc/yop-doc-i18n</module>
        <module>yop-tools/yop-i18n-translator</module>
        <module>yop-tools/yop-swagger-utils</module>
        <module>yop-tools/yop-data-connector</module>
    </modules>

    <dependencyManagement>
        <dependencies>
            <!-- Yeepay boot -->
            <dependency>
                <groupId>com.yeepay.boot.starters</groupId>
                <artifactId>yeepay-boot-transaction-starter</artifactId>
                <version>${yeepay-boot.version}</version>
                <exclusions>
                    <exclusion>
                        <artifactId>logback-classic</artifactId>
                        <groupId>ch.qos.logback</groupId>
                    </exclusion>
                </exclusions>
            </dependency>

            <!-- Project modules -->
            <dependency>
                <groupId>com.yeepay.janus</groupId>
                <artifactId>yop-doc-hessian</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>com.yeepay.janus</groupId>
                <artifactId>yop-doc-center</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>com.yeepay.janus</groupId>
                <artifactId>yop-doc-domain</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>com.yeepay.janus</groupId>
                <artifactId>yop-doc-facade</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>com.yeepay.janus</groupId>
                <artifactId>yop-doc-infrastructure</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>com.yeepay.janus</groupId>
                <artifactId>yop-doc-core</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>com.yeepay.janus</groupId>
                <artifactId>yop-doc-codegen-facade</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>com.yeepay.janus</groupId>
                <artifactId>yop-doc-codegen</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>com.yeepay.janus</groupId>
                <artifactId>yop-codegen-generator</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>com.yeepay.janus</groupId>
                <artifactId>yop-doc-i18n-facade</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>com.yeepay.janus</groupId>
                <artifactId>yop-doc-i18n</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>com.yeepay.janus</groupId>
                <artifactId>yop-i18n-translator</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>com.yeepay.janus</groupId>
                <artifactId>yop-swagger-utils</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>com.yeepay.janus</groupId>
                <artifactId>yop-data-connector</artifactId>
                <version>${project.version}</version>
            </dependency>
            <!-- Project modules End -->

            <!--     yop子系统       -->
            <dependency>
                <groupId>com.yeepay.g3.yop</groupId>
                <artifactId>yop-sys-event</artifactId>
                <version>${yop-sys-event.version}</version>
            </dependency>
            <dependency>
                <groupId>com.yeepay.g3.yop</groupId>
                <artifactId>yop-sys-facade</artifactId>
                <version>${yop-sys-facade.version}</version>
            </dependency>
            <!--     yop子系统       -->

            <!--    升级dubbo3   开始     -->
            <dependency>
                <groupId>com.yeepay.g3.utils</groupId>
                <artifactId>yeepay-rmi</artifactId>
                <version>${yeepay-rmi.version}</version>
                <exclusions>
                    <exclusion>
                        <groupId>javax.servlet</groupId>
                        <artifactId>servlet-api</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>
            <dependency>
                <groupId>com.yeepay.g3.utils</groupId>
                <artifactId>yeepay-soa</artifactId>
                <version>${yeepay-soa.version}</version>
            </dependency>
            <dependency>
                <groupId>com.yeepay.g3.starter</groupId>
                <artifactId>yeepay-soa-starter</artifactId>
                <version>${yeepay-soa-starter.version}</version>
            </dependency>
            <dependency>
                <groupId>com.yeepay.infra</groupId>
                <artifactId>fluent-sender-base</artifactId>
                <version>${fluent-sender-base.version}</version>
            </dependency>
            <dependency>
                <groupId>org.apache.curator</groupId>
                <artifactId>curator-client</artifactId>
                <version>${curator.version}</version>
            </dependency>
            <dependency>
                <groupId>org.apache.curator</groupId>
                <artifactId>curator-framework</artifactId>
                <version>${curator.version}</version>
            </dependency>
            <dependency>
                <groupId>org.apache.curator</groupId>
                <artifactId>curator-recipes</artifactId>
                <version>${curator.version}</version>
            </dependency>
            <dependency>
                <groupId>org.apache.curator</groupId>
                <artifactId>curator-x-discovery</artifactId>
                <version>${curator.version}</version>
            </dependency>
            <dependency>
                <groupId>org.javassist</groupId>
                <artifactId>javassist</artifactId>
                <version>${javassist.version}</version>
            </dependency>
            <dependency>
                <groupId>io.netty</groupId>
                <artifactId>netty-all</artifactId>
                <version>${netty-all.version}</version>
            </dependency>
            <dependency>
                <groupId>com.google.protobuf</groupId>
                <artifactId>protobuf-java</artifactId>
                <version>${protobuf-java.version}</version>
            </dependency>
            <dependency>
                <groupId>com.googlecode.concurrentlinkedhashmap</groupId>
                <artifactId>concurrentlinkedhashmap-lru</artifactId>
                <version>${concurrentlinkedhashmap-lru.version}</version>
            </dependency>
            <dependency>
                <groupId>org.apache.zookeeper</groupId>
                <artifactId>zookeeper</artifactId>
                <version>${zookeeper.version}</version>
                <exclusions>
                    <exclusion>
                        <artifactId>slf4j-log4j12</artifactId>
                        <groupId>org.slf4j</groupId>
                    </exclusion>
                </exclusions>
            </dependency>
            <!--    升级dubbo3   结束     -->

            <!-- Yeepay Tools Begin -->
            <dependency>
                <groupId>com.yeepay.g3.utils</groupId>
                <artifactId>yeepay-utils-common</artifactId>
                <version>${yeepay-utils-common.version}</version>
                <exclusions>
                    <exclusion>
                        <groupId>commons-logging</groupId>
                        <artifactId>commons-logging</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>c3p0</groupId>
                        <artifactId>c3p0</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>log4j</groupId>
                        <artifactId>log4j</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>com.yeepay.infra</groupId>
                        <artifactId>yeepay-atlas-agent</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>
            <dependency>
                <groupId>object-storage</groupId>
                <artifactId>object-storage-sdk</artifactId>
                <version>${object-storage-sdk.version}</version>
                <exclusions>
                    <exclusion>
                        <groupId>com.alibaba</groupId>
                        <artifactId>fastjson</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>
            <dependency>
                <groupId>com.yeepay.g3.utils</groupId>
                <artifactId>yeepay-persistence</artifactId>
                <exclusions>
                    <exclusion>
                        <groupId>org.mybatis</groupId>
                        <artifactId>mybatis</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>org.mybatis</groupId>
                        <artifactId>mybatis-spring</artifactId>
                    </exclusion>
                </exclusions>
                <version>${yeepay-persistence.version}</version>
            </dependency>
            <dependency>
                <groupId>com.yeepay.g3.boot</groupId>
                <artifactId>yeepay-utils</artifactId>
                <version>${yeepay-utils.version}</version>
            </dependency>
            <dependency>
                <groupId>com.yeepay.boot.components</groupId>
                <artifactId>yeepay-boot-utils</artifactId>
                <version>${yeepay-boot-utils.version}</version>
            </dependency>
            <dependency>
                <groupId>com.yeepay.boot.components</groupId>
                <artifactId>yeepay-boot-config</artifactId>
                <version>${yeepay-boot-config.version}</version>
                <exclusions>
                    <exclusion>
                        <groupId>commons-logging</groupId>
                        <artifactId>commons-logging</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>
            <dependency>
                <groupId>com.yeepay.infra</groupId>
                <artifactId>yeepay-infra-threadpool</artifactId>
                <version>${yeepay-infra-threadpool.version}</version>
                <exclusions>
                    <exclusion>
                        <groupId>com.yeepay.infra</groupId>
                        <artifactId>*</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>
            <dependency>
                <groupId>com.yeepay</groupId>
                <artifactId>yeepay-scheduler-client-starter</artifactId>
                <version>${yeepay-scheduler-client-starter.version}</version>
                <exclusions>
                    <exclusion>
                        <groupId>org.springframework.boot</groupId>
                        <artifactId>spring-boot-starter</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>

            <!--log4j2 begin-->
            <dependency>
                <groupId>com.yeepay.infra</groupId>
                <artifactId>infra-log4j2</artifactId>
                <version>${infra-log4j2.version}</version>
            </dependency>
            <dependency>
                <groupId>com.yeepay.g3.utils</groupId>
                <artifactId>yeepay-log4j2-pattern</artifactId>
                <version>${yeepay-log4j2-pattern.verison}</version>
            </dependency>
            <dependency>
                <groupId>com.yeepay.infra</groupId>
                <artifactId>accesslog-valve</artifactId>
                <version>${accesslog-valve.version}</version>
            </dependency>
            <dependency>
                <groupId>com.yeepay.infra</groupId>
                <artifactId>fluent-log4j2-appender</artifactId>
                <version>${fluent-log4j2-appender.version}</version>
            </dependency>
            <!--log4j2 end-->
            <!-- Yeepay Tools End -->

            <!-- Mybatis Plus Begin-->
            <dependency>
                <groupId>com.baomidou</groupId>
                <artifactId>mybatis-plus-boot-starter</artifactId>
                <version>${mybatis-plus-boot-starter.version}</version>
            </dependency>
            <dependency>
                <groupId>com.baomidou</groupId>
                <artifactId>mybatis-plus-generator</artifactId>
                <version>${mybatis-plus-generator.version}</version>
                <scope>test</scope>
            </dependency>
            <dependency>
                <groupId>com.baomidou</groupId>
                <artifactId>dynamic-datasource-spring-boot-starter</artifactId>
                <version>${dynamic-datasource-spring-boot-starter.version}</version>
                <exclusions>
                    <exclusion>
                        <groupId>ch.qos.logback</groupId>
                        <artifactId>logback-classic</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>
            <dependency>
                <groupId>com.alibaba</groupId>
                <artifactId>druid</artifactId>
                <version>${druid.version}</version>
            </dependency>
            <!-- Mybatis Plus End-->

            <!-- Redis lock Start-->
            <dependency>
                <groupId>org.springframework.data</groupId>
                <artifactId>spring-data-commons</artifactId>
                <version>${spring-data-commons.version}</version>
            </dependency>
            <dependency>
                <groupId>org.springframework.data</groupId>
                <artifactId>spring-data-keyvalue</artifactId>
                <version>${spring-data-keyvalue.version}</version>
            </dependency>
            <dependency>
                <groupId>org.springframework.data</groupId>
                <artifactId>spring-data-redis</artifactId>
                <version>${spring-data-redis.version}</version>
            </dependency>
            <dependency>
                <groupId>com.yeepay.g3.utils</groupId>
                <artifactId>yeepay-lock</artifactId>
                <version>${yeepay-lock.version}</version>
                <exclusions>
                    <exclusion>
                        <groupId>org.springframework</groupId>
                        <artifactId>spring-core</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>
            <dependency>
                <groupId>redis.clients</groupId>
                <artifactId>jedis</artifactId>
                <version>${jedis.version}</version>
            </dependency>
            <dependency>
                <groupId>com.yeepay.g3.utils</groupId>
                <artifactId>yeepay-smartcache</artifactId>
                <version>${yeepay-smartcache.version}</version>
            </dependency>
            <!-- Redis lock End-->

            <!-- event Start-->

            <!-- 老事件 -->
            <dependency>
                <groupId>com.yeepay.g3.yop</groupId>
                <artifactId>yop-sys-event</artifactId>
                <version>${yop-sys-event.version}</version>
            </dependency>
            <dependency>
                <groupId>com.yeepay.boot.starters</groupId>
                <artifactId>yeepay-boot-event-starter</artifactId>
                <version>${yeepay-boot-event.version}</version>
            </dependency>
            <dependency>
                <groupId>com.yeepay.boot.components</groupId>
                <artifactId>yeepay-boot-event</artifactId>
                <version>${yeepay-boot-event.version}</version>
            </dependency>
            <!-- rocket -->
            <dependency>
                <groupId>org.apache.rocketmq</groupId>
                <artifactId>rocketmq-spring-boot-starter</artifactId>
                <version>${rocketmq-spring-boot-starter.version}</version>
                <exclusions>
                    <exclusion>
                        <groupId>org.springframework.boot</groupId>
                        <artifactId>spring-boot-starter</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>

            <!-- 新事件(可靠消息) -->
            <dependency>
                <groupId>com.yeepay.g3.event</groupId>
                <artifactId>yeepay-event</artifactId>
                <version>4.0.3-SNAPSHOT</version>
            </dependency>

            <dependency>
                <groupId>com.alibaba</groupId>
                <artifactId>fastjson</artifactId>
                <version>1.2.83</version>
            </dependency>
            <!-- event End-->

            <!-- lucene Start -->
            <dependency>
                <groupId>org.apache.lucene</groupId>
                <artifactId>lucene-core</artifactId>
                <version>${lucene.version}</version>
            </dependency>
            <dependency>
                <groupId>org.apache.lucene</groupId>
                <artifactId>lucene-queryparser</artifactId>
                <version>${lucene.version}</version>
            </dependency>
            <dependency>
                <groupId>org.apache.lucene</groupId>
                <artifactId>lucene-analyzers-common</artifactId>
                <version>${lucene.version}</version>
            </dependency>
            <!-- lucene End-->

            <!-- swagger Start-->
            <dependency>
                <groupId>io.swagger.core.v3</groupId>
                <artifactId>swagger-core</artifactId>
                <version>${swagger-core.version}</version>
            </dependency>
            <dependency>
                <groupId>io.swagger.codegen.v3</groupId>
                <artifactId>swagger-codegen-generators</artifactId>
                <version>${swagger-codegen-generators.version}</version>
            </dependency>
            <dependency>
                <groupId>io.swagger.codegen.v3</groupId>
                <artifactId>swagger-codegen</artifactId>
                <version>${swagger-codegen-v3.version}</version>
                <exclusions>
                    <exclusion>
                        <artifactId>logback-classic</artifactId>
                        <groupId>ch.qos.logback</groupId>
                    </exclusion>
                    <exclusion>
                        <artifactId>logback-core</artifactId>
                        <groupId>ch.qos.logback</groupId>
                    </exclusion>
                </exclusions>
            </dependency>
            <dependency>
                <groupId>com.jayway.jsonpath</groupId>
                <artifactId>json-path</artifactId>
                <version>${json-path.version}</version>
            </dependency>
            <!-- swagger End-->

            <!--html、markdown Start -->
            <dependency>
                <groupId>com.vladsch.flexmark</groupId>
                <artifactId>flexmark-all</artifactId>
                <version>${flexmark-all.version}</version>
            </dependency>
            <!--html、markdown End -->

            <!-- others Start-->
            <dependency>
                <groupId>org.hibernate</groupId>
                <artifactId>hibernate-validator</artifactId>
                <version>${hibernate-validator.version}</version>
            </dependency>
            <dependency>
                <groupId>commons-lang</groupId>
                <artifactId>commons-lang</artifactId>
                <version>2.6</version>
            </dependency>
            <dependency>
                <groupId>com.google.code.gson</groupId>
                <artifactId>gson</artifactId>
                <version>${gson.version}</version>
            </dependency>
            <!-- others End-->

            <!-- test Start-->
            <dependency>
                <groupId>org.mockito</groupId>
                <artifactId>mockito-all</artifactId>
                <version>1.10.19</version>
                <scope>test</scope>
            </dependency>
            <!-- test End-->

        </dependencies>
    </dependencyManagement>

    <build>
        <pluginManagement>
            <plugins>
                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-source-plugin</artifactId>
                    <version>3.0.1</version>
                </plugin>
                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-resources-plugin</artifactId>
                    <version>3.2.0</version>
                </plugin>
                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-compiler-plugin</artifactId>
                    <version>3.8.1</version>
                    <configuration>
                        <failOnError>true</failOnError>
                        <verbose>true</verbose>
                        <fork>true</fork>
                        <compilerArgument>-nowarn</compilerArgument>
                        <compilerArgument>-XDignore.symbol.file</compilerArgument>
                        <source>${maven.compiler.source}</source>
                        <target>${maven.compiler.target}</target>
                        <encoding>UTF-8</encoding>
                        <annotationProcessorPaths>
                            <path>
                                <groupId>org.mapstruct</groupId>
                                <artifactId>mapstruct-processor</artifactId>
                                <version>${mapstruct.version}</version>
                            </path>
                            <path>
                                <groupId>org.projectlombok</groupId>
                                <artifactId>lombok</artifactId>
                                <version>1.18.20</version>
                            </path>
                            <path>
                                <groupId>org.projectlombok</groupId>
                                <artifactId>lombok-mapstruct-binding</artifactId>
                                <version>${mapstruct-lombok.version}</version>
                            </path>
                            <path>
                                <groupId>org.openjdk.jmh</groupId>
                                <artifactId>jmh-generator-annprocess</artifactId>
                                <version>${jmh.version}</version>
                            </path>
                        </annotationProcessorPaths>
                    </configuration>
                </plugin>
                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-deploy-plugin</artifactId>
                    <version>2.8.2</version>
                </plugin>
                <plugin>
                    <groupId>org.springframework.boot</groupId>
                    <artifactId>spring-boot-maven-plugin</artifactId>
                    <version>2.6.1</version>
                </plugin>
            </plugins>
        </pluginManagement>
    </build>

    <profiles>
        <profile>
            <id>snp</id>
            <activation>
                <activeByDefault>true</activeByDefault>
            </activation>
            <properties>
                <yop-sys-event.version>1.1-SNAPSHOT</yop-sys-event.version>
                <yop-sys-facade.version>1.1-SNAPSHOT</yop-sys-facade.version>
            </properties>
        </profile>
        <profile>
            <id>rls</id>
            <properties>
                <yop-sys-event.version>1.1</yop-sys-event.version>
                <yop-sys-facade.version>1.1</yop-sys-facade.version>
            </properties>
        </profile>
    </profiles>

</project>