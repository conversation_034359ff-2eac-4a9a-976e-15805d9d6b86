-- data-source：yop_doc
-- 文档页面负责人管理功能数据库迁移脚本
-- 创建日期：2025-07-28
-- 功能描述：为YOP文档系统添加页面级别的负责人管理能力

USE yop_doc;

-- 创建页面负责人表
CREATE TABLE `tbl_doc_page_owner` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键',
  `page_id` bigint(20) NOT NULL COMMENT '页面ID，关联tbl_doc_page.id',
  `owner_id` varchar(32)  NOT NULL COMMENT '负责人ID',
  `owner_name` varchar(64)  NOT NULL COMMENT '负责人姓名',
  `created_datetime` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `last_modified_datetime` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '最后更新时间',
  `version` bigint(20) NOT NULL DEFAULT '0' COMMENT '乐观锁版本号',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uniq_idx_page_owner` (`page_id`, `owner_id`) COMMENT '页面和负责人的唯一约束',
  KEY `idx_page_id` (`page_id`) COMMENT '页面ID索引',
  KEY `idx_owner_id` (`owner_id`) COMMENT '负责人ID索引',
  KEY `idx_last_modified_datetime` (`last_modified_datetime`) COMMENT '最后修改时间索引',
  CONSTRAINT `fk_doc_page_owner_page_id` FOREIGN KEY (`page_id`) REFERENCES `tbl_doc_page` (`id`) ON DELETE CASCADE ON UPDATE CASCADE
) ENGINE=InnoDB COMMENT='文档页面负责人表';