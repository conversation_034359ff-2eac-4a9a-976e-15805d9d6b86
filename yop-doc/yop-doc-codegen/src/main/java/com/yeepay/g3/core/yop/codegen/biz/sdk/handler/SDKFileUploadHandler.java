package com.yeepay.g3.core.yop.codegen.biz.sdk.handler;

import com.yeepay.boot.components.config.utils.ConfigUtils;
import com.yeepay.g3.core.yop.codegen.biz.dto.SDKGenerateFileContext;
import com.yeepay.g3.core.yop.codegen.biz.sdk.AbstractSDKGenerateHandler;
import com.yeepay.g3.core.yop.codegen.biz.sdk.SDKGenerateContext;
import com.yeepay.g3.core.yop.codegen.utils.ceph.CephUtil;
import com.yeepay.g3.core.yop.codegen.utils.config.ConfigEnum;
import com.yeepay.g3.facade.yop.codegen.dto.FileInfo;
import com.yeepay.g3.facade.yop.codegen.dto.LangParam;
import com.yeepay.g3.facade.yop.codegen.enumtype.LangEnum;
import com.yeepay.g3.facade.yop.codegen.enumtype.SDKGenerateStatusEnum;
import com.yeepay.g3.utils.common.exception.YeepayRuntimeException;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.time.DateFormatUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import java.util.Date;
import java.util.Map;

import static com.yeepay.g3.core.yop.codegen.utils.constans.YopCodegenConstant.JAVA_REPOSITORY_KEY;
import static com.yeepay.g3.core.yop.codegen.utils.constans.YopCodegenConstant.PHP_REPOSITORY_KEY;

/**
 * title: SDK文件上传处理器<br/>
 * description: <br/>
 * Copyright: Copyright (c) 2018<br/>
 * Company: 易宝支付(YeePay)<br/>
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2018/6/7 18:16
 */
@Component
public class SDKFileUploadHandler extends AbstractSDKGenerateHandler {

    private static final Logger LOGGER = LoggerFactory.getLogger(SDKFileUploadHandler.class);

    private static final String DATE_FORMAT = "yyyyMMddHHmmss";
    private static final String DATE_FORMAT_FILE = "yyyy/MM/dd";
    private static final String PATH_SEPERATOR = "/";

    @Override
    protected boolean doHandle(SDKGenerateContext context) {
        context.getRecord().setDownloadUrl(uploadFile(context.getFileContext(), context.getRequest().getLangParam()));
        context.getRecord().setStatus(SDKGenerateStatusEnum.SUCCESS);
        return true;
    }

    private String uploadFile(SDKGenerateFileContext fileContext, LangParam langParam) {
        try {
            Map<String, String> repositoryConfig = (Map<String, String>) ConfigUtils.getSysConfigParam(ConfigEnum.YOP_CODEGEN_LANG_GITHUB_REPOSITORY);
            String sdkVersion;
            if (LangEnum.JAVA.name().equalsIgnoreCase(langParam.getLang())) {
                sdkVersion = repositoryConfig.get(JAVA_REPOSITORY_KEY).split(",")[4];
            } else if (LangEnum.PHP.name().equalsIgnoreCase(langParam.getLang())) {
                sdkVersion = repositoryConfig.get(PHP_REPOSITORY_KEY).split(",")[4];
            } else {
                sdkVersion = "1.0";
            }
            fileContext.getFileInfo().setName(getUploadFileName(fileContext.getFileInfo(), langParam, sdkVersion));
            return uploadToCeph(fileContext);
        } catch (Exception ex) {
            LOGGER.error("Errors occurred when compress and upload sdk.", ex);
            throw new YeepayRuntimeException("Errors occurred when compress and upload sdk.", ex);
        }
    }

    private String getUploadFileName(FileInfo fileInfo, LangParam langParam, String sdkVersion) {
        StringBuilder fileName = new StringBuilder()
                .append("yop-")
                .append(StringUtils.lowerCase(langParam.getLang()))
                .append("-sdk-")
                .append(StringUtils.replace(sdkVersion, "-", ""))
                .append("-")
                .append(DateFormatUtils.format(new Date(), DATE_FORMAT))
                .append("-")
                .append(fileInfo.getDigest().get("sha256"))
                .append(".zip");
        return fileName.toString();
    }

    private String uploadToCeph(SDKGenerateFileContext fileContext) throws Exception {
        return CephUtil.upload(getCephFileName(fileContext.getFileInfo().getName()), fileContext.getSdkFile());
    }

    private String getCephFileName(String fileName) {
        return new StringBuilder().append(DateFormatUtils.format(new Date(), DATE_FORMAT_FILE))
                .append(PATH_SEPERATOR).append(fileName).toString();
    }
}
