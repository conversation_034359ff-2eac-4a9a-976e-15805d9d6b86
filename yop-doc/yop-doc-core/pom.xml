<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/maven-v4_0_0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>com.yeepay.janus</groupId>
        <artifactId>janus-parent</artifactId>
        <version>1.1-SNAPSHOT</version>
        <relativePath>../../pom.xml</relativePath>
    </parent>

    <artifactId>yop-doc-core</artifactId>
    <packaging>jar</packaging>

    <dependencies>
        <dependency>
            <groupId>com.yeepay.janus</groupId>
            <artifactId>yop-doc-codegen-facade</artifactId>
        </dependency>
        <dependency>
            <groupId>com.yeepay.janus</groupId>
            <artifactId>yop-doc-infrastructure</artifactId>
        </dependency>
        <dependency>
            <groupId>com.yeepay.janus</groupId>
            <artifactId>yop-swagger-utils</artifactId>
        </dependency>

        <!--spring start-->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-web</artifactId>
            <exclusions>
                <exclusion>
                    <groupId>org.springframework.boot</groupId>
                    <artifactId>spring-boot-starter-logging</artifactId>
                </exclusion>
            </exclusions>
            <scope>provided</scope>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-actuator</artifactId>
            <scope>provided</scope>
        </dependency>
        <dependency>
            <groupId>org.springframework</groupId>
            <artifactId>spring-aop</artifactId>
        </dependency>
        <!--spring end-->

        <!-- yop start-->
        <dependency>
            <groupId>com.yeepay.janus</groupId>
            <artifactId>yop-doc-facade</artifactId>
        </dependency>
        <dependency>
            <groupId>com.yeepay.g3.yop</groupId>
            <artifactId>yop-sys-facade</artifactId>
        </dependency>
        <dependency>
            <groupId>com.yeepay.g3.yop</groupId>
            <artifactId>yop-sys-event</artifactId>
        </dependency>
        <dependency>
            <groupId>com.yeepay.g3.facade.notifier</groupId>
            <artifactId>notifier-facade</artifactId>
        </dependency>
        <!-- yop end-->

        <!-- yeepay tools start-->
        <dependency>
            <groupId>com.yeepay.g3.utils</groupId>
            <artifactId>yeepay-soa</artifactId>
        </dependency>
        <dependency>
            <groupId>com.yeepay.g3.utils</groupId>
            <artifactId>yeepay-rmi</artifactId>
        </dependency>
        <dependency>
            <groupId>com.yeepay.g3.starter</groupId>
            <artifactId>yeepay-soa-starter</artifactId>
        </dependency>
        <dependency>
            <groupId>com.yeepay.boot.components</groupId>
            <artifactId>yeepay-boot-utils</artifactId>
        </dependency>
        <dependency>
            <groupId>com.yeepay.g3.utils</groupId>
            <artifactId>yeepay-persistence</artifactId>
            <exclusions>
                <exclusion>
                    <groupId>com.ibm.db2</groupId>
                    <artifactId>db2jcc4</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.ibm.db2</groupId>
                    <artifactId>db2jcc_license_cu</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.yeepay.boot.components</groupId>
            <artifactId>yeepay-boot-config</artifactId>
        </dependency>
        <dependency>
            <groupId>object-storage</groupId>
            <artifactId>object-storage-sdk</artifactId>
        </dependency>
        <dependency>
            <groupId>com.yeepay.infra</groupId>
            <artifactId>accesslog-valve</artifactId>
        </dependency>
        <!-- yeepay tools end-->

        <!--db start-->
        <dependency>
            <groupId>org.mybatis</groupId>
            <artifactId>mybatis</artifactId>
        </dependency>
        <dependency>
            <groupId>org.mybatis</groupId>
            <artifactId>mybatis-spring</artifactId>
        </dependency>
        <dependency>
            <groupId>mysql</groupId>
            <artifactId>mysql-connector-java</artifactId>
        </dependency>
        <!--db end-->

        <!--redis cache start-->
        <dependency>
            <groupId>org.springframework.data</groupId>
            <artifactId>spring-data-commons</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.data</groupId>
            <artifactId>spring-data-redis</artifactId>
        </dependency>
        <dependency>
            <groupId>redis.clients</groupId>
            <artifactId>jedis</artifactId>
        </dependency>
        <!--redis cache end-->

        <!-- event begin-->

        <!-- 新事件 -->
        <dependency>
            <groupId>com.yeepay.g3.event</groupId>
            <artifactId>yeepay-event</artifactId>
        </dependency>

        <!-- 老事件 -->
        <dependency>
            <groupId>com.yeepay.boot.components</groupId>
            <artifactId>yeepay-boot-event</artifactId>
        </dependency>
        <dependency>
            <groupId>org.apache.rocketmq</groupId>
            <artifactId>rocketmq-spring-boot-starter</artifactId>
            <exclusions>
                <exclusion>
                    <groupId>org.springframework.boot</groupId>
                    <artifactId>spring-boot-starter</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <!-- event end-->

        <!-- 后端渲染模板引擎 start-->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-thymeleaf</artifactId>
        </dependency>
        <dependency>
            <groupId>org.freemarker</groupId>
            <artifactId>freemarker</artifactId>
        </dependency>
        <!-- 后端渲染模板引擎 end-->

        <!--搜索索引 start-->
        <dependency>
            <groupId>com.janeluo</groupId>
            <artifactId>ikanalyzer</artifactId>
            <version>2012_u6</version>
            <exclusions>
                <exclusion>
                    <groupId>org.apache.lucene</groupId>
                    <artifactId>lucene-core</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>org.apache.lucene</groupId>
                    <artifactId>lucene-queryparser</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>org.apache.lucene</groupId>
                    <artifactId>lucene-analyzers-common</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>org.apache.lucene</groupId>
            <artifactId>lucene-queryparser</artifactId>
        </dependency>
        <dependency>
            <groupId>org.apache.lucene</groupId>
            <artifactId>lucene-analyzers-common</artifactId>
        </dependency>
        <dependency>
            <groupId>org.apache.lucene</groupId>
            <artifactId>lucene-core</artifactId>
        </dependency>
        <!--搜索索引 end-->

        <!-- swagger-->
        <dependency>
            <groupId>io.swagger.core.v3</groupId>
            <artifactId>swagger-core</artifactId>
        </dependency>

        <!-- HTML 压缩 start -->
        <dependency>
            <groupId>com.googlecode.htmlcompressor</groupId>
            <artifactId>htmlcompressor</artifactId>
            <version>1.5.2</version>
        </dependency>
        <dependency>
            <groupId>com.yahoo.platform.yui</groupId>
            <artifactId>yuicompressor</artifactId>
            <version>2.4.7</version>
        </dependency>
        <!-- HTML 压缩 end -->

        <!-- other tools start -->
        <dependency>
            <groupId>com.fasterxml.jackson.module</groupId>
            <artifactId>jackson-module-jaxb-annotations</artifactId>
        </dependency>
        <dependency>
            <groupId>org.apache.commons</groupId>
            <artifactId>commons-collections4</artifactId>
        </dependency>
        <dependency>
            <groupId>org.aspectj</groupId>
            <artifactId>aspectjrt</artifactId>
        </dependency>
        <dependency>
            <groupId>org.aspectj</groupId>
            <artifactId>aspectjweaver</artifactId>
        </dependency>
        <dependency>
            <groupId>org.hibernate</groupId>
            <artifactId>hibernate-validator</artifactId>
        </dependency>
        <dependency>
            <groupId>com.samskivert</groupId>
            <artifactId>jmustache</artifactId>
        </dependency>
        <!-- other tools end -->

        <!-- test start -->
        <dependency>
            <groupId>org.springframework</groupId>
            <artifactId>spring-test</artifactId>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-test</artifactId>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-test</artifactId>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>org.mockito</groupId>
            <artifactId>mockito-all</artifactId>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>org.junit.jupiter</groupId>
            <artifactId>junit-jupiter-params</artifactId>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>log4j</groupId>
            <artifactId>log4j</artifactId>
            <version>1.2.14</version>
            <scope>test</scope>
        </dependency>
        <!-- test end -->
    </dependencies>

</project>