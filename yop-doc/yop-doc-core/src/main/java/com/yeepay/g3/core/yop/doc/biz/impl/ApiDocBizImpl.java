/*
 * Copyright: Copyright (c)2011
 * Company: 易宝支付(YeePay)
 */
package com.yeepay.g3.core.yop.doc.biz.impl;

import com.google.common.collect.Maps;
import com.yeepay.boot.components.config.utils.ConfigUtils;
import com.yeepay.g3.core.yop.doc.biz.ApiDocBiz;
import com.yeepay.g3.core.yop.doc.dto.api.ApiDocItem;
import com.yeepay.g3.core.yop.doc.dto.api.ApiParam;
import com.yeepay.g3.core.yop.doc.dto.api.SpiItem;
import com.yeepay.g3.core.yop.doc.generator.DocGenerator;
import com.yeepay.g3.core.yop.doc.generator.DocGeneratorFactory;
import com.yeepay.g3.core.yop.doc.generator.GeneratorConstants;
import com.yeepay.g3.core.yop.doc.service.ApiService;
import com.yeepay.g3.core.yop.doc.service.DocI18nService;
import com.yeepay.g3.core.yop.doc.utils.ApiDocUtils;
import com.yeepay.g3.core.yop.doc.utils.RemoteStorage;
import com.yeepay.g3.core.yop.doc.utils.bean.BeanHelper;
import com.yeepay.g3.core.yop.doc.utils.config.ConfigEnum;
import com.yeepay.g3.core.yop.doc.utils.mapper.JsonMapper;
import com.yeepay.g3.facade.yop.doc.util.ApiI18nUtils;
import com.yeepay.g3.facade.yop.doc.util.ErrorCodeI18nUtils;
import com.yeepay.g3.facade.yop.sys.dto.api.ApiDocQueryReqDTO;
import com.yeepay.g3.facade.yop.sys.facade.ApiRequestFacade;
import com.yeepay.g3.i18n.dto.I18nResourceDTO;
import com.yeepay.g3.utils.rmi.RemoteServiceFactory;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.time.StopWatch;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

import static com.yeepay.g3.core.yop.doc.generator.GeneratorConstants.API_SPIS_GENERATOR_NAME;
import static com.yeepay.g3.core.yop.doc.generator.GeneratorConstants.SPI_DEFINITION_GENERATOR_NAME;
import static com.yeepay.g3.core.yop.doc.utils.YopConstant.*;

/**
 * title: <br>
 * description: 描述<br>
 * Copyright: Copyright (c)2014<br>
 * Company: 易宝支付(YeePay)<br>
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 12/23/20 5:36 PM
 */
@Component
public class ApiDocBizImpl implements ApiDocBiz {

    private static final Logger LOGGER = LoggerFactory.getLogger(ApiDocBiz.class);

    private final JsonMapper nonNullMapper = JsonMapper.nonNullMapper();
    private final ApiRequestFacade apiRequestFacade = RemoteServiceFactory.getService(ApiRequestFacade.class);

    @Autowired
    private ApiService apiService;

    @Autowired
    private RemoteStorage remoteStorage;

    @Autowired
    private DocI18nService docI18nService;

    @Async
    @Override
    public void initApiDoc() {
        StopWatch watch = new StopWatch();
        try {
            watch.start();
            // 原版
            doInitApiDoc("");

            // 译版
            final List<String> langs = getDocSupportLangs();
            if (CollectionUtils.isNotEmpty(langs)) {
                for (String lang : langs) {
                    doInitApiDoc(lang);
                }
            }
        } catch (Exception e) {
            LOGGER.error("fail to initApiDoc, ex:", e);
        } finally {
            LOGGER.info("finish to initApiDoc, elapsed:{}ms", watch.getTime(TimeUnit.MILLISECONDS));
        }
    }

    private List<String> getDocSupportLangs() {
        return (List<String>) ConfigUtils.getSysConfigParam(ConfigEnum.YOP_DOC_I18N_SUPPORT_LANGS);
    }

    @Override
    public void refreshApiCommonConfig() {
        final List<String> supportLangs = new ArrayList<>(),
                configLangs = getDocSupportLangs();
        supportLangs.add("");
        if (CollectionUtils.isNotEmpty(configLangs)) {
            supportLangs.addAll(configLangs);
        }
        supportLangs.forEach(this::batchInitApiCommon);
    }

    private void doInitApiDoc(String lang) {
        // 查询api
        final List<ApiDocItem> apis = apiService.findForDoc(new ApiDocQueryReqDTO(), lang);

        // 公共信息: 统一配置
        batchInitApiCommon(lang);

        // API回调
        batchInitApiSpi(apis, lang);

        // 左侧树
        initApiMenuTree(lang);

        // 对接产品树
        initDockingProductTree(lang);

        // 历史版本
        batchInitApiHistory(apis, lang);

        // API定义
        batchInitApiDefinition(apis, lang);

        // API错误码
        batchInitApiErrcode(apis, lang);

        // API模型
        batchInitApiModel(apis, lang);
    }

    private void initDockingProductTree(String lang) {
        try {
            // 更新对接产品树
            Object dockingProductTree = DocGeneratorFactory.get(GeneratorConstants.DOCKING_PRODUCT_TREE_GENERATOR_NAME).generate(lang);
            remoteStorage.update(String.format(API_DOCKING_PRODUCT_TREE_JSON_FILE, getLangSuffix(lang)), nonNullMapper.toJson(dockingProductTree));
        } catch (Exception e) {
            LOGGER.error("fail to initDockingProductTree, ex:", e);
        }
    }

    private void batchInitApiCommon(String lang) {
        try {
            Map<String, Map<String, List<ApiParam>>> refMap = ApiDocUtils.apiCommonParamsConfig();
            refMap.forEach((refId,refParams) -> uploadApiCommon(String.format(API_COMMON_JSON_FILE_FORMAT, refId + getLangSuffix(lang)), refId, (Map<String, List<ApiParam>>)BeanHelper.deepClone(refParams), lang));
        } catch (Exception e) {
            LOGGER.error("fail to batchInitApiCommon, ex:", e);
        }
    }

    private void uploadApiCommon(String remotePath, String refId, Map<String, List<ApiParam>> obj, String lang) {
        try {
            if (StringUtils.isNotBlank(lang)) {
                Map<String, List<I18nResourceDTO>> resourcesMap = docI18nService.queryResources(Arrays.asList(ApiI18nUtils.API_COMMON_PARAMS_RESOURCE_KEY), Arrays.asList(lang), ApiI18nUtils.PROVIDER, ApiI18nUtils.BIZ_CODE);
                if (MapUtils.isNotEmpty(resourcesMap)) {
                    List<I18nResourceDTO> resources = resourcesMap.get(lang);
                    if (CollectionUtils.isNotEmpty(resources)) {
                        Map<String, String> translatedMap = new LinkedHashMap<>();
                        for (I18nResourceDTO resource : resources) {
                            Map<String, String> tmpMap = (Map<String, String>) nonNullMapper.fromJson(resource.getContent(), Map.class);
                            if (null != tmpMap) {
                                translatedMap.putAll(tmpMap);
                            }
                        }
                        if (MapUtils.isNotEmpty(translatedMap)) {
                            for (ApiParam reqParam : obj.getOrDefault(ApiDocUtils.REQ_PARAMS_KEY, Collections.emptyList())) {
                                String translatedDesc = null;
                                if (StringUtils.isNotBlank(translatedDesc = translatedMap.get(ApiI18nUtils.apiCommonReqParamDescResourceKey(refId, reqParam.getIn(), reqParam.getName())))) {
                                    reqParam.setDescription(translatedDesc);
                                }
                            }
                            for (ApiParam respParam : obj.getOrDefault(ApiDocUtils.RESP_PARAMS_KEY, Collections.emptyList())) {
                                String translatedDesc = null;
                                if (StringUtils.isNotBlank(translatedDesc = translatedMap.get(ApiI18nUtils.apiCommonRespParamDescResourceKey(refId, respParam.getIn(), respParam.getName())))) {
                                    respParam.setDescription(translatedDesc);
                                }
                            }
                        }
                    }
                }
            }
            remoteStorage.update(remotePath, nonNullMapper.toJson(obj));
        } catch (Exception e) {
            LOGGER.error("fail to uploadApiCommon, ex:", e);
        }
    }

    private String getLangSuffix(String lang) {
        return StringUtils.isNotBlank(lang) ? ("_" + lang) : "";
    }

    private void batchInitApiModel(List<ApiDocItem> apis, String lang) {
        try {
            if (CollectionUtils.isNotEmpty(apis)) {
                Set<String> apiGroups = new LinkedHashSet<>();
                apis.forEach(api -> {
                    final String apiGroup = api.getApiGroup();
                    if (!apiGroups.contains(apiGroup)) {
                        apiGroups.add(apiGroup);
                        DocGeneratorFactory.get(GeneratorConstants.API_MODELS_GENERATOR_NAME).generate(apiGroup, "", lang);
                    }
                });
            }
        } catch (Exception e) {
            LOGGER.error("fail to batchInitApiModel, ex:", e);
        }
    }

    private void batchInitApiSpi(List<ApiDocItem> apis, String lang) {
        try {
            if (CollectionUtils.isNotEmpty(apis)) {
                Map<String, Set<String>> spiGrouped = new LinkedHashMap<>();
                DocGenerator apiSpisGenerator = DocGeneratorFactory.get(API_SPIS_GENERATOR_NAME);
                apis.forEach(api -> {
                    try {
                        List<SpiItem> spiItems = (List<SpiItem>) apiSpisGenerator.generate(api.getApiId(), lang);
                        if (CollectionUtils.isNotEmpty(spiItems)) {
                            spiGrouped.computeIfAbsent(api.getApiGroup(), p -> new LinkedHashSet<>())
                                    .addAll(spiItems.stream().map(SpiItem::getName).collect(Collectors.toSet()));
                        }
                    } catch (Exception e) {
                        LOGGER.error("fail to initApiSpis, ex:", e);
                    }
                });
                DocGeneratorFactory.get(SPI_DEFINITION_GENERATOR_NAME).generate(spiGrouped, lang);
            }
        } catch (Exception e) {
            LOGGER.error("fail to batchInitApiSpi, ex:", e);
        }
    }

    private void batchInitApiErrcode(List<ApiDocItem> apis, String lang) {
        try {
            if (CollectionUtils.isNotEmpty(apis)) {
                Map<String, List<ApiDocItem>> apisGrouped = new LinkedHashMap<>();
                apis.forEach(api -> apisGrouped.computeIfAbsent(api.getApiGroup(), p -> new LinkedList<>()).add(api));
                apisGrouped.forEach((apiGroupCode, apiItems) -> {
                    Map langMap = new LinkedHashMap();
                    if (StringUtils.isNotBlank(lang)) {
                        Map<String, String> groupErrcodeResources = getApiErrcodeResources(apiGroupCode, lang);
                        if (MapUtils.isNotEmpty(groupErrcodeResources)) {
                            final String groupSubErrcodeStr = groupErrcodeResources.values().iterator().next();
                            if (StringUtils.isNotBlank(groupSubErrcodeStr)) {
                                Map tmpMap = nonNullMapper.fromJson(groupSubErrcodeStr, Map.class);
                                if (MapUtils.isNotEmpty(tmpMap)) {
                                    langMap.putAll(tmpMap);
                                }
                            }
                        }
                    }
                    apiItems.forEach(api -> DocGeneratorFactory.get(GeneratorConstants.API_ERROR_CODES_GENERATOR_NAME).generate(api.getApiId(), lang, langMap));
                });
            }
        } catch (Exception e) {
            LOGGER.error("fail to batchInitApiErrcode, ex:", e);
        }
    }

    private void batchInitApiDefinition(List<ApiDocItem> apis, String lang) {
        try {
            if (CollectionUtils.isNotEmpty(apis)) {
                apis.forEach(api -> DocGeneratorFactory.get(GeneratorConstants.API_DEFINITION_GENERATOR_NAME).generate(api, lang));
            }
        } catch (Exception e) {
            LOGGER.error("fail to batchInitApiDefinition, ex:", e);
        }
    }

    private void initApiMenuTree(String lang) {
        try {
            // 更新api菜单树
            Object apiTree = DocGeneratorFactory.get(GeneratorConstants.API_TREE_GENERATOR_NAME).generate(lang);
            remoteStorage.update(String.format(API_MENU_TREE_JSON_FILE, getLangSuffix(lang)), nonNullMapper.toJson(apiTree));
        } catch (Exception e) {
            LOGGER.error("fail to initApiMenuTree, ex:", e);
        }
    }

    /*错误码名称、描述、解决方案*/
    private Map<String, String> getApiErrcodeResources(String apiGroupCode, String lang) {
        if (StringUtils.isNotBlank(lang)) {
            return toI18nKVmap(docI18nService.queryResources(Arrays.asList(ErrorCodeI18nUtils.groupSubErrcodesResourceKey(apiGroupCode)), Arrays.asList(lang), ErrorCodeI18nUtils.PROVIDER, ErrorCodeI18nUtils.BIZ_CODE), lang);
        }
        return Collections.emptyMap();
    }

    private Map<String, String> toI18nKVmap(Map<String, List<I18nResourceDTO>> resources, String lang) {
        Map<String, String> result = new LinkedHashMap<>();
        if (MapUtils.isNotEmpty(resources)) {
            final List<I18nResourceDTO> resourceItems = resources.get(lang);
            if (CollectionUtils.isNotEmpty(resourceItems)) {
                resourceItems.forEach(item -> result.put(item.getI18nKey(), item.getContent()));
            }
        }

        return result;
    }

    public void batchInitApiHistory(List<ApiDocItem> apis, String lang) {
        try {
            if (CollectionUtils.isNotEmpty(apis)) {
                Map<String, ApiDocItem.ApiHistoryItem> apiMap = Maps.newHashMapWithExpectedSize(apis.size());
                Map<String, List<ApiDocItem.ApiHistoryItem>> apiHistoryMap = Maps.newLinkedHashMap();
                apis.forEach(api -> {
                    final ApiDocItem.ApiHistoryItem historyItem = api.toHistoryItem();
                    apiMap.put(api.getApiId(), historyItem);
                    if (StringUtils.isNotBlank(api.getLatestRef()) && !StringUtils.equals(api.getApiId(), api.getLatestRef())) {
                        apiHistoryMap.computeIfAbsent(api.getLatestRef(), p -> new LinkedList<>()).add(historyItem);
                    }
                });
                apiHistoryMap.forEach((latestRef, historyItems) -> {
                    final ApiDocItem.ApiHistoryItem latest = apiMap.get(latestRef);
                    if (null != latest) {
                        historyItems.add(0, latest);
                    } else {
                        // 主版本未发布，子版本自动升级(最新为准)
                        LOGGER.warn("latest api not published, apiId:{}", latestRef);
                    }
                    final Iterator<ApiDocItem.ApiHistoryItem> iterator = historyItems.iterator();
                    Set<String> historyApiSet = new LinkedHashSet<>();
                    while (iterator.hasNext()) {
                        // 多版本api方法与路径一样时，仅保留最新版
                        final ApiDocItem.ApiHistoryItem next = iterator.next();
                        final String uniquApiKey = next.getPath() + next.getMethod();
                        if (historyApiSet.contains(uniquApiKey)) {
                            iterator.remove();
                        } else {
                            historyApiSet.add(uniquApiKey);
                        }
                    }
                    remoteStorage.update(String.format(API_HISTORY_JSON_FILE_FORMAT, latestRef, getLangSuffix(lang)), nonNullMapper.toJson(historyItems));
                });
            }
        } catch (Exception e) {
            LOGGER.error("fail to batchInitApiHistory, ex:", e);
        }
    }

    @Override
    public void publishModelJson(String apiGroup, String modelName) {
        final List<String> langs = new ArrayList<>(),
                configLangs = (List<String>) ConfigUtils.getSysConfigParam(ConfigEnum.YOP_DOC_I18N_SUPPORT_LANGS);
        langs.add("");
        if (CollectionUtils.isNotEmpty(configLangs)) {
            langs.addAll(configLangs);
        }
        for (String lang : langs) {
            DocGeneratorFactory.get(GeneratorConstants.API_MODELS_GENERATOR_NAME).generate(apiGroup, modelName, lang);
        }
    }

    @Override
    public void publishApiErrcode(List<String> apiIds) {
        if (CollectionUtils.isNotEmpty(apiIds)) {
            apiIds.forEach(apiId -> {
                List<String> langs = new ArrayList<>();
                langs.add("");
                langs.addAll(getDocSupportLangs());
                langs.forEach(lang -> DocGeneratorFactory.get(GeneratorConstants.API_ERROR_CODES_GENERATOR_NAME).generate(apiId, lang));
            });
        }
    }

    @Override
    public void publishChangedApis(boolean isOldApi, Map<String, List<String>> changedApis) {
        if (MapUtils.isNotEmpty(changedApis)) {
            Set<String> apis = new HashSet<>();
            changedApis.values().forEach(apis::addAll);
            List<String> apiIds;
            if (isOldApi) {
                apiIds = new ArrayList<>(apis.size());
                for (String api : apis) {
                    try {
                        final String apiId = apiRequestFacade.findApiIdByPathForOldApi(api);
                        if (StringUtils.isNotBlank(apiId)) {
                            apiIds.add(apiId);
                        }
                    } catch (Exception e) {
                        LOGGER.error("error when findApiIdByPathForOldApi, api:" + api, e);
                    }
                }
            } else {
                apiIds = new ArrayList<>(apis);
            }
            final ApiDocQueryReqDTO queryParam = new ApiDocQueryReqDTO();
            queryParam.setApiIds(apiIds);
            List<String> langs = new ArrayList<>();
            langs.add("");
            langs.addAll(getDocSupportLangs());
            langs.forEach(lang -> {
                final List<ApiDocItem> apiDocItems = apiService.findForDoc(queryParam, lang);
                apiDocItems.forEach(p -> DocGeneratorFactory.get(GeneratorConstants.API_DEFINITION_GENERATOR_NAME).generate(p, lang));
            });
        }
    }
}
