package com.yeepay.g3.core.yop.doc.biz.impl;

import com.baomidou.dynamic.datasource.annotation.DSTransactional;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.util.concurrent.MoreExecutors;
import com.yeepay.boot.components.config.utils.ConfigUtils;
import com.yeepay.g3.core.yop.doc.biz.*;
import com.yeepay.g3.core.yop.doc.entity.DocCategoryRelateEntity;
import com.yeepay.g3.core.yop.doc.entity.DocProductRelateEntity;
import com.yeepay.g3.core.yop.doc.entity.example.DocPageExample;
import com.yeepay.g3.core.yop.doc.entity.example.DocPublishRecordExample;
import com.yeepay.g3.core.yop.doc.event.api.ApiTreeRefreshEvent;
import com.yeepay.g3.core.yop.doc.event.doc.DocApiListRefreshEvent;
import com.yeepay.g3.core.yop.doc.event.doc.DocApiListRefreshEventContent;
import com.yeepay.g3.core.yop.doc.event.idx.internal.DocRefreshEvent;
import com.yeepay.g3.core.yop.doc.event.idx.internal.DocRefreshEventContent;
import com.yeepay.g3.core.yop.doc.generator.DocGeneratorFactory;
import com.yeepay.g3.core.yop.doc.generator.GeneratorConstants;
import com.yeepay.g3.core.yop.doc.service.DocCategoryService;
import com.yeepay.g3.core.yop.doc.service.DocMgrService;
import com.yeepay.g3.core.yop.doc.service.DocPublishRecordService;
import com.yeepay.g3.core.yop.doc.service.DocSpService;
import com.yeepay.g3.core.yop.doc.strategy.utils.ReIKAnalyzer;
import com.yeepay.g3.core.yop.doc.strategy.utils.SearchIdxUtils;
import com.yeepay.g3.core.yop.doc.utils.AnalyzerUtil;
import com.yeepay.g3.core.yop.doc.utils.ProductUtils;
import com.yeepay.g3.core.yop.doc.utils.config.ConfigEnum;
import com.yeepay.g3.core.yop.doc.utils.thread.YopThreadPoolExecutor;
import com.yeepay.g3.facade.yop.api.enums.ApiStatusEnum;
import com.yeepay.g3.facade.yop.doc.dto.*;
import com.yeepay.g3.facade.yop.doc.dto.v2.*;
import com.yeepay.g3.facade.yop.doc.enums.DocPageOperEnum;
import com.yeepay.g3.facade.yop.doc.enums.v2.*;
import com.yeepay.g3.facade.yop.doc.util.DocCategoryUtils;
import com.yeepay.g3.facade.yop.doc.util.DocUtils;
import com.yeepay.g3.facade.yop.sys.dto.*;
import com.yeepay.g3.facade.yop.sys.dto.validate.ProductQueryReqDTO;
import com.yeepay.g3.facade.yop.sys.enums.ProductStatusEnum;
import com.yeepay.g3.facade.yop.sys.enums.ProductTypeEnum;
import com.yeepay.g3.facade.yop.sys.facade.CustomSolutionMgrFacade;
import com.yeepay.g3.facade.yop.sys.facade.ProductFacade;
import com.yeepay.g3.facade.yop.sys.facade.ProductQueryFacade;
import com.yeepay.g3.utils.common.exception.YeepayRuntimeException;
import com.yeepay.g3.utils.common.log.Logger;
import com.yeepay.g3.utils.common.log.LoggerFactory;
import com.yeepay.g3.utils.rmi.RemoteServiceFactory;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.time.FastDateFormat;
import org.apache.lucene.document.Document;
import org.apache.lucene.index.DirectoryReader;
import org.apache.lucene.index.IndexReader;
import org.apache.lucene.queryparser.classic.MultiFieldQueryParser;
import org.apache.lucene.queryparser.classic.ParseException;
import org.apache.lucene.queryparser.classic.QueryParser;
import org.apache.lucene.search.IndexSearcher;
import org.apache.lucene.search.Query;
import org.apache.lucene.search.ScoreDoc;
import org.apache.lucene.search.TopDocs;
import org.apache.lucene.store.Directory;
import org.apache.lucene.store.FSDirectory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.dao.RecoverableDataAccessException;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;
import org.springframework.util.Assert;

import javax.annotation.PreDestroy;
import java.io.IOException;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.util.*;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

import static com.yeepay.g3.core.yop.doc.utils.YopConstant.*;
import static com.yeepay.g3.facade.yop.doc.enums.v2.DocIncludesEnum.PAGES;
import static com.yeepay.g3.facade.yop.doc.enums.v2.DocIncludesEnum.PAGES_CONTENT;
import static com.yeepay.g3.facade.yop.doc.util.DocUtils.SEQ_STEP;
import static com.yeepay.g3.facade.yop.doc.util.DocUtils.YOP_DOC_ROOT_PAGE_ID;
import static com.yeepay.janus.domain.doc.exeption.DocExceptionEnum.*;

/**
 * title: 文档管理biz<br/>
 * description: <br/>
 * Copyright: Copyright (c) 2017<br/>
 * Company: 易宝支付(YeePay)<br/>
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 17/4/24 16:18
 */
@Component
public class DocMgrBizImpl extends AbstractDocPublishBiz implements DocMgrBiz {

    private static final Logger LOGGER = LoggerFactory.getLogger(DocMgrBizImpl.class);
    private static final String API_OFF_LINE = "OFFLINE";
    private static final String API_ON_LINE = "ONLINE";
    private static final FastDateFormat dateFormat = FastDateFormat.getInstance("yyyyMMddHHmmss");

    private CustomSolutionMgrFacade customSolutionMgrFacade = RemoteServiceFactory.getService(CustomSolutionMgrFacade.class);

    @Autowired
    private DocMgrService docMgrService;

    @Autowired
    private DocSpService docSpService;

    @Autowired
    private DocPublishRecordService docPublishRecordService;

    @Autowired
    private DocCategoryBiz docCategoryBiz;

    @Autowired
    private DocCategoryService docCategoryService;

    @Autowired
    private ApplicationEventPublisher publisher;

    @Autowired
    private DocPageBiz docPageBiz;

    @Autowired
    private DocI18nBiz docI18nBiz;

    private ProductFacade productFacade = RemoteServiceFactory.getService(ProductFacade.class);
    private ProductQueryFacade productQueryFacade = RemoteServiceFactory.getService(ProductQueryFacade.class);
    private ExecutorService docPublishPostWorkExecutor = YopThreadPoolExecutor.newSingleThreadExecutor("doc-publish-post-work");

    private static String ID = "id";
    private static String TOP = "top";
    private static String BASE = "base";
    private static String CONTENT = "content";

    private RedisTemplate<String, Object> redisTemplate;

    @Autowired
    public void setRedisTemplate(RedisTemplate<String, Object> redisTemplate) {
        this.redisTemplate = redisTemplate;
    }

    // region 文档搜索
    @Override
    public DocSearchReturnDTO findDocByContent(DocSearchDTO docSearchDTO) {
        LOGGER.info("searchInfo is : " + docSearchDTO);
        DocSearchReturnDTO docSearchReturnDTO = new DocSearchReturnDTO();
        List<DocSearchReturnPageDTO> returnPages = new ArrayList<>();
        String searchKeyWord = docSearchDTO.getWd();
        Integer searchMaxNum = (Integer) ConfigUtils.getSysConfigParam(ConfigEnum.YOP_DOC_SEARCH_NUM);
        String lang = StringUtils.defaultIfBlank(docSearchDTO.getLang(), SEARCH_DEFAULT_LANG);
        Path path = Paths.get(IDX_LOCAL_PATH + SEPRATOR + lang);
        Directory directory = null;
        try {
            directory = FSDirectory.open(path);
            IndexReader reader = DirectoryReader.open(directory);
            IndexSearcher search = new IndexSearcher(reader);

            ReIKAnalyzer analyzer = new ReIKAnalyzer(true);
            searchKeyWord = QueryParser.escape(searchKeyWord);
            // 目前分词仅支持中、英文
            List<String> keys = lang.equals(SEARCH_DEFAULT_LANG) ? AnalyzerUtil.cutWord(searchKeyWord)
                    : AnalyzerUtil.cutEngWord(searchKeyWord);
            docSearchReturnDTO.setCutWords(keys);
            String strKeys = StringUtils.join(keys.toArray(), " ");
            Query query;
            try {
                String[] fields = new String[]{TOP, BASE, CONTENT};
                query = new MultiFieldQueryParser(fields, analyzer).parse(strKeys);
            } catch (ParseException e) {
                LOGGER.error("Errors occurred when parse search DocPage ", e);
                return docSearchReturnDTO;
            }
            TopDocs topDocs = search.search(query, searchMaxNum);
            ScoreDoc[] scoreDocs = topDocs.scoreDocs;
            List<Double> scores = new ArrayList<>();

            for (ScoreDoc scoreDoc : scoreDocs) {
                int docId = scoreDoc.doc;
                Document doc = search.doc(docId);
                String pageId = doc.get("id");
                String pageNo = doc.get("pageNo");
                String docNo = doc.get("docNo");
                // 过滤掉不展示的文档和页面
                if (redisTemplate.opsForSet().isMember(CACHE_FILTERED_DOCS, docNo)
                        || redisTemplate.opsForSet().isMember(CACHE_FILTERED_PAGES, pageId)
                        || redisTemplate.opsForSet().isMember(CACHE_FILTERED_APIS, pageNo)
                        || DocUtils.getExcludePages(docNo, ConfigUtils.getSysConfigParam(ConfigEnum.YOP_DOC_PAGE_SWITCH,
                        Map.class)).contains(pageId)) {
                    continue;
                }
                DocSearchReturnPageDTO returnPageDTO = handleSearchReturnDoc(doc, keys);
                if (StringUtils.isBlank(returnPageDTO.getContent())) {
                    continue;
                }
                returnPages.add(returnPageDTO);
                //按照相关性和更新时间排序
                double score = scoreDoc.score * 0.9 + returnPageDTO.getLastModifiedDate().getTime() / 50000000000000.0;
                scores.add(scoreDoc.score * 0.9 + score);
            }
            // 排序
            for (int i = 0; i < scores.size(); i++) {
                for (int j = i; j < scores.size(); j++) {
                    if (scores.get(i) < scores.get(j)) {
                        double temScore = scores.get(i);
                        scores.set(i, scores.get(j));
                        scores.set(j, temScore);
                        DocSearchReturnPageDTO temDoc = returnPages.get(i);
                        returnPages.set(i, returnPages.get(j));
                        returnPages.set(j, temDoc);
                    }
                }
            }
            reader.close();
        } catch (IOException e) {
            LOGGER.warn("Errors occurred when search DocPage for " + searchKeyWord, e);
        }

        docSearchReturnDTO.setDocPageDTOList(returnPages);
        return docSearchReturnDTO;
    }

    private DocSearchReturnPageDTO handleSearchReturnDoc(Document doc, List<String> keyWords) {
        DocSearchReturnPageDTO returnPage = new DocSearchReturnPageDTO();
        returnPage.setId(Long.parseLong(doc.get(ID)));
        String content = doc.get("abbrContent");
        returnPage.setContent(SearchIdxUtils.handleContent(content, keyWords));
        returnPage.setPageUri(doc.get("pageUri"));
        returnPage.setPageTitle(doc.get("pageTitle"));
        returnPage.setDocNo(doc.get("docNo"));
        returnPage.setDocTitle(doc.get("docTitle"));
        returnPage.setDocUri(doc.get("docUri"));
        returnPage.setCategory(DocSearchCategoryEnum.parse(doc.get("category")));
        returnPage.setLastModifiedDate(new Date(Long.parseLong(doc.get("lastModifiedDate"))));
        return returnPage;
    }
    // endregion

    @Override
    public DocDTO find(Long id, DocIncludesEnum... includes) {
        return super.find(id, includes);
    }

    @Override
    public DocDTO find(String docNo, DocIncludesEnum... includes) {
        return super.find(docNo, includes);
    }

    @Override
    @DSTransactional
    public void refreshDocTree(Long docId) {
        DocDTO doc = docMgrService.findOne(docId);
        if (null == doc) {
            throw new YeepayRuntimeException("该文档不存在, docId[{0}]", docId);
        } else {
            doRefreshDocTree(doc);
        }
    }

    private void doRefreshDocTree(DocDTO doc) {
        if (DocTypeEnum.PRODUCT.equals(doc.getType())) {
            doRefreshProductApis(doc);
        } else if (DocTypeEnum.SOLUTION.equals(doc.getType())){
            doRefreshSolutionApis(doc);
        } else {
            LOGGER.warn("非产品/解决方案文档无需刷新");
        }
    }

    private void doRefreshProductApis(DocDTO doc) {
        //取出该文档关联的产品，聚合api信息、spi信息、场景信息
        final List<DocProductDTO> docProducts = docMgrService.findProductsByDocNo(doc.getDocNo());
        if (CollectionUtils.isEmpty(docProducts)) {
            throw new YeepayRuntimeException("该文档尚未关联产品, docNo[{0}]", doc.getDocNo());
        }
        ProductDocQueryResult productDocQueryResult = resolveProductInfo(docProducts);

        DocPageDTO apiParent = getApiParent(doc.getDocNo());
        Long apiParentId = apiParent.getId();
        List<DocPageDTO> existsApiPages = docPageService.findDirectSonPages(doc.getDocNo(), apiParentId);

        if (CollectionUtils.isEmpty(productDocQueryResult.getOldApis()) && CollectionUtils.isEmpty(productDocQueryResult.getNewApis())) {
            LOGGER.warn("no api/spi bound to doc, docNo:{}", doc.getDocNo());
            docPageService.batchDeleteDirectSons(apiParentId);
        } else {
            Long categoryId = docCategoryBiz.getDocApiDefaultCategory(doc.getDocNo());
            refreshApis(categoryId, apiParent, existsApiPages, productDocQueryResult.getOldApis(), productDocQueryResult.getNewApis());
        }

        // 自动发布文档api列表
        publisher.publishEvent(new DocApiListRefreshEvent(new DocApiListRefreshEventContent().setDocNo(doc.getDocNo())));
    }

    private void doRefreshSolutionApis(DocDTO doc) {
        final CustomSolutionDocQueryResult queryResult = customSolutionQueryFacade.queryForDoc(doc.getDocNo());
        if (null == queryResult) {
            return;
        }

        DocPageDTO apiParent = getApiParent(doc.getDocNo());
        Long apiParentId = apiParent.getId();
        List<DocPageDTO> existsApiPages = docPageService.findDirectSonPages(doc.getDocNo(), apiParentId);

        if (CollectionUtils.isEmpty(queryResult.getOldApis()) && CollectionUtils.isEmpty(queryResult.getNewApis())) {
            LOGGER.warn("no api/spi bound to doc, docNo:{}", doc.getDocNo());
            docPageService.batchDeleteDirectSons(apiParentId);
        } else {
            Long categoryId = docCategoryBiz.getDocApiDefaultCategory(doc.getDocNo());
            refreshApis(categoryId, apiParent, existsApiPages, queryResult.getOldApis(), queryResult.getNewApis());
        }
    }

    private void refreshApis(Long categoryId, DocPageDTO parentPage, List<DocPageDTO> existSonPages, List<ApiSummary> oldApis, List<ApiSummary> newApis) {
        //容器
        List<DocPageDTO> toBeUpdateList = Lists.newArrayList();
        List<DocPageDTO> toBeAddList = Lists.newArrayList();

        //拆分现存的api
        List<DocPageDTO> existApis = Lists.newArrayList();
        Map<String, DocPageDTO> existMap = Maps.newHashMap();
        if (CollectionUtils.isNotEmpty(existSonPages)) {
            existSonPages.forEach(e -> {
                existMap.put(e.getPageNo(), e);
                if (e.getPageType() == DocPageTypeEnum.API) {
                    existApis.add(e);
                }
            });
        }

        //计算api的title、status变更
        List<DocPageDTO> toBeUpdateApis = Lists.newArrayList();
        List<DocPageDTO> toBeAddApis = Lists.newArrayList();
        handleApis(categoryId, parentPage, existMap, oldApis, newApis, toBeUpdateApis, toBeAddApis);
        //计算api排序值
        int apiEndSeq = CollectionUtils.isNotEmpty(existApis) ? (existApis.get(existApis.size() - 1).getSeq() + SEQ_STEP) : (1 + SEQ_STEP);
        for (int i = 0; i < toBeAddApis.size(); i++) {
            toBeAddApis.get(i).setSeq(apiEndSeq);
            apiEndSeq += SEQ_STEP;
        }
        toBeUpdateList.addAll(toBeUpdateApis);
        toBeAddList.addAll(toBeAddApis);

        if (MapUtils.isNotEmpty(existMap)) {
            docPageService.batchDeleteById(existMap.values().stream().map(DocPageDTO::getId).collect(Collectors.toList()));
        }

        if (CollectionUtils.isNotEmpty(toBeAddList)) {
            docPageService.batchSave(toBeAddList);
        }
        if (CollectionUtils.isNotEmpty(toBeUpdateList)) {
            docPageService.batchUpdateSeqById(toBeUpdateList);
        }
    }

    private void handleApis(Long categoryId, DocPageDTO parentPage, Map<String, DocPageDTO> existMap, List<ApiSummary> oldApis, List<ApiSummary> newApis, List<DocPageDTO> toBeUpdateApis, List<DocPageDTO> toBeAddApis) {
        Map<String, Object> alreadyHandled = Maps.newHashMap();

        // 老版api
        handleApis(true, oldApis, alreadyHandled, parentPage, existMap, toBeUpdateApis, toBeAddApis);

        //新版本api
        handleApis(false, newApis, alreadyHandled, parentPage, existMap, toBeUpdateApis, toBeAddApis);

        if (CollectionUtils.isNotEmpty(toBeAddApis)) {
            final List<DocContentBlock> apiTemplateBlocks = getApiTemplateBlocks();
            toBeAddApis.forEach(e -> {
                e.setBlocks(apiTemplateBlocks);
                e.setCategoryId(categoryId);
            });
        }
    }

    private void handleApis(boolean isOldApi, List<ApiSummary> apiSummaries, Map<String, Object> alreadyHandled, DocPageDTO parentPage, Map<String, DocPageDTO> existMap, List<DocPageDTO> toBeUpdateApis, List<DocPageDTO> toBeAddApis) {
        if (CollectionUtils.isEmpty(apiSummaries)) {
            return;
        }
        final int disableControl = 1 << DocPageOperEnum.STATUS_DISABLE.ordinal();
        final int apiRequiredControl = 1 << DocPageOperEnum.REQUIRED.ordinal();
        apiSummaries.forEach(apiSummary -> {
            String pageNo;
            if (isOldApi) {
                pageNo = DocUtils.generateApiPageNo(null, apiSummary.getRequestPath());
            } else {
                pageNo = DocUtils.generateApiPageNo(apiSummary.getRequestMethod(), apiSummary.getRequestPath());
            }
            if (!alreadyHandled.containsKey(pageNo)) {
                alreadyHandled.put(pageNo, null);
                boolean apiDisable = isApiStatusDisabled(apiSummary.getStatus()),
                        apiEnable = isApiStatusEnabled(apiSummary.getStatus()),
                        apiRequired = !BooleanUtils.isFalse(apiSummary.getRequired());
                final DocPageDTO existPage = existMap.remove(pageNo);
                if (null != existPage) {
                    boolean statusDisable = !existPage.isStatusDisable() && apiDisable,
                            statusEnable = existPage.isStatusDisable() && apiEnable,
                            requiredApi = !existPage.isRequired() && apiRequired,
                            unRequiredApi = existPage.isRequired() && !apiRequired,
                            statusChanged = statusDisable || statusEnable,
                            requireChanged = requiredApi || unRequiredApi,
                            needUpdate = false;

                    if (StringUtils.isNotBlank(apiSummary.getTitle()) && !existPage.getTitle().equals(apiSummary.getTitle())) {
                        existPage.setTitle(apiSummary.getTitle());
                        needUpdate = true;
                    }
                    if (statusChanged) {
                        if (statusDisable) {
                            existPage.setPageControl(existPage.getPageControl() + disableControl);
                        } else {
                            existPage.setPageControl(existPage.getPageControl() - disableControl);
                        }
                        needUpdate = true;
                    }
                    if (requireChanged) {
                        if (requiredApi) {
                            existPage.setPageControl(existPage.getPageControl() - apiRequiredControl);
                        } else {
                            existPage.setPageControl(existPage.getPageControl() + apiRequiredControl);
                        }
                        needUpdate = true;
                    }
                    if (needUpdate) {
                        toBeUpdateApis.add(existPage);
                    }
                } else {
                    if (apiEnable) {
                        toBeAddApis.add(toApiDocPage(isOldApi, apiSummary, parentPage));
                    }
                }
            }
        });

    }

    private DocPageDTO toApiDocPage(boolean isOldApi, ApiSummary apiSummary, DocPageDTO parentPage) {
        String pageNo;
        if (isOldApi) {
            pageNo = DocUtils.generateApiPageNo(null, apiSummary.getRequestPath());
        } else {
            pageNo = DocUtils.generateApiPageNo(apiSummary.getRequestMethod(), apiSummary.getRequestPath());
        }
        DocPageDTO apiPage = new DocPageDTO();
        apiPage.setDocNo(parentPage.getDocNo());
        apiPage.setPageNo(pageNo);
        apiPage.setPid(parentPage.getId());
        apiPage.setDepth(parentPage.getDepth() + 1);
        apiPage.setPageType(DocPageTypeEnum.API);
        apiPage.setPageVisible(DocVisableEnum.PUBLIC);
        apiPage.setDisplay(true);
        apiPage.setPageControl(DocUtils.YOP_DOC_PRODUCT_API_PAGE_CONTROL);
        if (BooleanUtils.isFalse(apiSummary.getRequired())) {
            apiPage.setPageControl(apiPage.getPageControl() + (1 << DocPageOperEnum.REQUIRED.ordinal()));
        }
        apiPage.setTitle(apiSummary.getTitle());
        apiPage.setVersion(0L);
        return apiPage;
    }

    private DocPageDTO getApiParent(String docNo) {
        DocPageDTO apiParentPage = null;
        final List<DocPageDTO> apiParentList = docPageService.findByExample(new DocPageExample().setPid(YOP_DOC_ROOT_PAGE_ID).setDocNo(docNo).setPageNo(DocUtils.YOP_DOC_PRODUCT_API_PARENT_PAGENO));
        if (CollectionUtils.isEmpty(apiParentList)) {
            apiParentPage = DocUtils.getProductApiParentPage(docNo);
            apiParentPage.setId(docPageService.save(apiParentPage));
        } else {
            apiParentPage = apiParentList.get(0);
        }
        return apiParentPage;
    }

    private List<DocContentBlock> getApiTemplateBlocks() {
        return docPageService.initApiPageFromTemplate().getBlocks();
    }

    @Override
    @Async
    public void publishProductDoc(DocPublishRequest publishRequest) {
        DocDTO doc = find(publishRequest.getId(), PAGES_CONTENT);
        if (DocUtils.isTemplateDoc(doc.getType()) && CollectionUtils.isNotEmpty(checkApisWhenPublishDoc(doc))) {
            throw new YeepayRuntimeException("请编辑该文档内相关API的描述与使用说明，稍后再发布");
        }
        // 首次发布
        boolean isFirstPublish = StringUtils.isBlank(doc.getDocVersion()) || DOC_INIT_VERSION.equalsIgnoreCase(doc.getDocVersion());

        //发布版本
        String docVersion = dateFormat.format(new Date()),
            tmpDir = UUID.randomUUID().toString().replace("-", ""),
            workDir = THYMELEAF_TEMPLATE_UPLOAD_PATH + tmpDir;
        doc.setDocVersion(docVersion);

        //生成文档左侧树(json)
        renderDocMenu(workDir, doc, "");

        //生成wiki页面(markdown)
        renderWikiPages(doc, workDir, "");

        //上传ceph
        zipAndUploadToCeph(doc, workDir);

        //持久化
        docMgrService.publishDoc(doc, publishRequest, isFirstPublish);
    }

    @Override
    public void postPublishDoc(DocDTO doc, boolean isFirstPublish) {
        docPublishPostWorkExecutor.submit(() -> {
            boolean canRecover = false, success = false;
            // 尝试3次
            for (int i = 0; i < 3; i++) {
                try {
                    //平台错误码刷新
                    if (DocTypeEnum.PLATFORM.equals(doc.getType())) {
                        docPageBiz.refreshPlatformErrorCodePage(doc.getDocVersion());
                    } else if (DocUtils.isTemplateDoc(doc.getType())) {
                        //更新api列表页
                        publisher.publishEvent(new DocApiListRefreshEvent(new DocApiListRefreshEventContent().setDocNo(doc.getDocNo())));
                    }
                    //推送i18n素材
                    docI18nBiz.pushI18nDoc(doc, isFirstPublish);
                    success = true;
                } catch (Exception e) {
                    LOGGER.error("doc publish post work error, ex", e);
                    canRecover = e instanceof RecoverableDataAccessException;
                }
                if (!canRecover) {
                    break;
                }
            }
            if (!success) {
                LOGGER.warn("doc publish post work fail after trying 3 times, doc:{}", doc.getDocNo());
            }
        });
    }

    private List<String> checkApisWhenPublishDoc(DocDTO doc) {
        final Optional<DocPageDTO> apiParent = doc.getPages().stream().filter(e -> e.getPageType() == DocPageTypeEnum.API).findAny();
        Map<Long, DocCategoryDTO> apiCategoryMap = getApiCategories(doc.getDocNo());
        List<DocPageDTO> apis = null;
        if (apiParent.isPresent() && CollectionUtils.isNotEmpty(apis = apiParent.get().getChildren())) {
            final List<DocContentBlock> apiTemplateBlocks = getApiTemplateBlocks();
            final List<String> template = apiTemplateBlocks.stream().map(e -> StringUtils.isNotBlank(e.getData()) ? e.getData().replaceAll("\\s*", "") : "").collect(Collectors.toList());
            boolean apiContentNotEdited = false;
            List<String> apisNoContent = Lists.newArrayListWithExpectedSize(apis.size());
            for (DocPageDTO api : apis) {
                if (null != api.getCategoryId() && apiCategoryMap.containsKey(api.getCategoryId())) {
                    apiCategoryMap.remove(api.getCategoryId());
                }
                if (api.isStatusDisable() || !api.getDisplay() || api.getPageType() == DocPageTypeEnum.SPI) {
                    continue;
                }
                String invalidApiStr = DocUtils.fromApiPageNo(api.getPageNo())[1] + "(" + api.getTitle() + ")";
                if (CollectionUtils.isEmpty(api.getBlocks())) {
                    apiContentNotEdited = true;
                    apisNoContent.add(invalidApiStr);
                } else {
                    List<String> apiContent = api.getBlocks().stream().map(e -> StringUtils.isNotBlank(e.getData()) ? e.getData().replaceAll("\\s*", "") : "").collect(Collectors.toList());
                    if (CollectionUtils.isEmpty(apiContent) || (apiContent.size() != template.size())) {
                        apiContentNotEdited = true;
                        apisNoContent.add(invalidApiStr);
                    } else {
                        for (int i = 0; i < template.size(); i++) {
                            if (template.get(i).equals(apiContent.get(i))) {
                                apiContentNotEdited = true;
                                apisNoContent.add(invalidApiStr);
                                break;
                            }
                        }
                    }
                }
            }
            if (apiContentNotEdited) {
                LOGGER.info("fail to publish doc when apis not edited, apis:{}", apisNoContent);
                return apisNoContent;
            }
            if (MapUtils.isNotEmpty(apiCategoryMap)) {
                String categoryNames = apiCategoryMap.values().stream().filter(p -> !p.getCode().equals(DocCategoryUtils.DEFAULT_CATEGORY_CODE)).map(DocCategoryDTO::getName).collect(Collectors.joining(","));
                if (StringUtils.isNotBlank(categoryNames)) {
                    throw new YeepayRuntimeException("文档发布失败，请删除空的API模块后重试，categories:{0}", categoryNames);
                }
            }
        } else {
            throw new YeepayRuntimeException("文档发布失败，请先同步API信息.");
        }

        return Collections.emptyList();
    }

    private Map<Long, DocCategoryDTO> getApiCategories(String docNo) {
        Map<Long, DocCategoryDTO> apiCategoryMap = new LinkedHashMap<>();
        List<DocCategoryDTO> apiCategories = docCategoryService.findByScope(DocCategoryUtils.apiCategoryScope(docNo));
        if (CollectionUtils.isNotEmpty(apiCategories)) {
            apiCategories.forEach(apiCategory -> apiCategoryMap.put(apiCategory.getId(), apiCategory));
        }
        return apiCategoryMap;
    }

    /**
     * 兼容新旧版api
     * @param status
     * @return
     */
    private boolean isApiStatusDisabled(String status) {
        return ApiStatusEnum.FORBID.name().equals(status) || API_OFF_LINE.equals(status);
    }

    /**
     * 兼容新旧版api
     * @param status
     * @return
     */
    private boolean isApiStatusEnabled(String status) {
        return ApiStatusEnum.ACTIVE.name().equals(status) || API_ON_LINE.equals(status);
    }

    private ProductDocQueryResult resolveProductInfo(List<DocProductDTO> docProducts) {
        return ProductUtils.resolveProductInfo(docProducts.stream()
                .map(DocProductDTO::getProductCode).collect(Collectors.toList()));
    }

    @Override
    public void recoverDoc(DocPublishRecordDTO docPublishRecord) {
        //校验指定的发布记录、文档是否存在
        final DocPublishRecordExample example = new DocPublishRecordExample();
        example.setId(docPublishRecord.getId());
        final List<DocPublishRecordDTO> publishRecords = docPublishRecordService.findByExample(example);
        DocPublishRecordDTO oldPublishRecord = null;

        if (CollectionUtils.isEmpty(publishRecords)) {
            throw new YeepayRuntimeException("该文档发布记录不存在, recordId[{0}]", docPublishRecord.getId());
        } else {
            oldPublishRecord = publishRecords.get(0);
            docMgrService.recoverDoc(oldPublishRecord);
            publisher.publishEvent(new DocRefreshEvent(new DocRefreshEventContent(oldPublishRecord.getDocNo())));
        }
    }

    @Override
    @DSTransactional
    public boolean logicDelete(Long id) {
        Exception ex = null;
        String docNo = null;
        try {
            final DocDTO docExists = docMgrService.findOne(id);
            // 仅模版文档存在逻辑删除
            if (null == docExists || !DocUtils.isTemplateDoc(docExists.getType())) {
                return false;
            }

            docNo = docExists.getDocNo();
            if (DocTypeEnum.PRODUCT.equals(docExists.getType())) {
                // 仅文档下所有产品的状态均不正常(NORMAL)时，文档可删除(逻辑删)
                final List<DocProductDTO> docProducts = docMgrService.findProductsByDocNo(docNo);
                if (CollectionUtils.isNotEmpty(docProducts)) {
                    final ProductQueryReqDTO queryReq = new ProductQueryReqDTO();
                    queryReq.setProductCodes(docProducts.stream().
                            map(DocProductDTO::getProductCode).collect(Collectors.toList()));
                    final List<ProductDTO> productDTOS = productQueryFacade.queryProducts(queryReq);
                    if (productDTOS.stream().anyMatch(p -> p.getStatus().equals(ProductStatusEnum.NORMAL))) {
                        throw new YeepayRuntimeException("该文档不允许删除，请核实相关产品是否均已下线, docNo[{0}].", docNo);
                    }
                } else {
                    //无产品的【产品文档】，脏数据？？
                    LOGGER.warn("dirty data, docNo:{}", docNo);
                }
            }
            //文档状态
            docExists.setStatus(DocStatusEnum.DELETED);
            docMgrService.logicDeleteDoc(docExists);
            //搜索索引
            redisTemplate.opsForSet().add(CACHE_FILTERED_DOCS, docNo);
            //API概览
            publisher.publishEvent(ApiTreeRefreshEvent.DEFAULT_EVENT);
        } catch (YeepayRuntimeException e) {
            ex = e;
            throw e;
        } catch (Exception e) {
            ex = e;
            throw new YeepayRuntimeException("文档删除异常，", e);
        } finally {
            if (null != ex) {
                LOGGER.error("fail to logicDelete doc,", ex);
            } else {
                LOGGER.info("success to logicDelete doc, id:{}, code:{}", id, docNo);
            }
        }
        return true;
    }

    @Override
    @DSTransactional
    public boolean delete(Long id) {
        Exception ex = null;
        String docNo = null;
        try {
            final DocDTO docExists = docMgrService.findOne(id);
            if (null != docExists) {
                docNo = docExists.getDocNo();
                //ceph资源
                String docRootDir = getDocRootDir(docExists);
                // 平台文档，慎重
                if (!DocTypeEnum.PLATFORM.equals(docExists.getType())) {
                    remoteStorage.batchDelete(docRootDir);
                    //文档页面
                    docPageService.deleteByDocNo(docNo);
                    //文档
                    docMgrService.deleteDoc(docNo);
                    //搜索索引
                    redisTemplate.opsForSet().add(CACHE_FILTERED_DOCS, docNo);
                    //API概览
                    publisher.publishEvent(ApiTreeRefreshEvent.DEFAULT_EVENT);
                }
            }
        } catch (YeepayRuntimeException e) {
            ex = e;
            throw e;
        } catch (Exception e) {
            ex = e;
            throw new YeepayRuntimeException("文档删除异常，", e);
        } finally {
            if (null != ex) {
                LOGGER.error("fail to delete doc,", ex);
            } else {
                LOGGER.info("success to delete doc, id:{}, code:{}", id, docNo);
            }
        }
        return true;
    }

    @Override
    public void refreshDocTreeForProduct(String productCode) {
        final List<DocDTO> docs = docMgrService.findDocByProductCode(productCode);
        if (CollectionUtils.isEmpty(docs)) {
            throw new YeepayRuntimeException("该产品相关文档不存在, productCode[{0}]", productCode);
        } else {
            docs.forEach(doc -> {
                try {
                    doRefreshDocTree(doc);
                } catch (Exception e) {
                    LOGGER.error("error to refresh doc tree, productCode:" + productCode + "docNo:" + doc.getDocNo(), e);
                }
            });
        }
    }

    @Override
    public void refreshDocTree(String docNo) {
        final DocDTO doc = docMgrService.findOneByDocNo(docNo);
        if (null != doc) {
            try {
                doRefreshDocTree(doc);
            } catch (Exception e) {
                LOGGER.error("error to refresh doc tree, docNo:" + doc.getDocNo(), e);
            }
        }
    }

    @Override
    public void refreshDocTreeForApiPage(List<String> apiPageNos) {
        final DocPageExample example = new DocPageExample();
        example.setPageNos(apiPageNos);
        example.setPageType(DocPageTypeEnum.API);
        final List<DocPageDTO> pages = docPageService.findByExample(example);

        if (CollectionUtils.isNotEmpty(pages)) {
            final Set<String> docs = pages.stream().map(DocPageDTO::getDocNo).collect(Collectors.toSet());
            docs.forEach(docNo -> {
                final DocDTO doc = docMgrService.findOneByDocNo(docNo);
                if (null != doc) {
                    docPublishPostWorkExecutor.submit(() -> doRefreshDocTree(doc));
                }
            });
        }
    }

    @Override
    public DocPublishCheckResultDTO checkForPublish(Long docId) {
        DocDTO doc = find(docId, PAGES_CONTENT);
        List<String> invalidApis = Collections.emptyList();
        if (DocUtils.isTemplateDoc(doc.getType())) {
            invalidApis = checkApisWhenPublishDoc(doc);
        }
        final DocPublishCheckResultDTO checkResult = new DocPublishCheckResultDTO();
        checkResult.setInvalidApis(invalidApis);
        return checkResult;
    }

    @Override
    public DocQueryResultDTO queryForProducts(List<String> products) {
        final DocQueryResultDTO queryResult = new DocQueryResultDTO();
        if (CollectionUtils.isNotEmpty(products)) {
            Map<String, List<DocDTO>> productDocs = Maps.newHashMapWithExpectedSize(products.size());
            products.forEach(productCode -> {
                final List<DocDTO> docs = docMgrService.findPublishedProductDoc(productCode);
                if (CollectionUtils.isNotEmpty(docs)) {
                    docs.forEach(doc -> {
                        DocMgrBizImpl.super.fillIncludes(doc, false, PAGES);
                        if (CollectionUtils.isNotEmpty(doc.getPages())) {
                            doc.setPages(doc.getPages().stream().filter(e -> e.getDisplay() && !e.isStatusDisable()
                                    && e.getPageType() == DocPageTypeEnum.API
                                    && !e.getPageNo().equals(DocUtils.YOP_DOC_PRODUCT_API_PARENT_PAGENO)).collect(Collectors.toList()));
                        }
                    });
                    productDocs.put(productCode, docs);
                } else {
                    productDocs.put(productCode, Collections.emptyList());
                }
            });
            queryResult.setProductDocs(productDocs);
        } else {
            queryResult.setProductDocs(Collections.emptyMap());
        }
        return queryResult;
    }

    @Override
    public DocDTO findDocTree(Long id) {
        return docPageService.findDocTree(id);
    }

    @Override
    public void create(DocCreateDTO dto) {
        validateWhenCreateDoc(dto);
        docMgrService.create(dto);
    }

    @Override
    public void logicRecover(DocDTO doc) {
        docMgrService.logicRecoverDoc(doc);
        // 搜索索引
        redisTemplate.opsForSet().remove(CACHE_FILTERED_DOCS, doc.getDocNo());
        //API概览
        publisher.publishEvent(ApiTreeRefreshEvent.DEFAULT_EVENT);
    }

    @Override
    @DSTransactional
    public void update(DocUpdateDTO dto) {
        final String docNo = dto.getDocNo();
        final DocDTO oldDoc = docMgrService.findOneByDocNo(docNo);
        if (null == oldDoc) {
            throw new YeepayRuntimeException("该文档不存在, docNo[{0}]", docNo);
        }
        if (oldDoc.getType().equals(DocTypeEnum.PRODUCT)) {
            updateProductDoc(dto);
        }

        // 文档标题调整（非解决方案可改）
        if (!oldDoc.getType().equals(DocTypeEnum.SOLUTION)
                && !StringUtils.equals(oldDoc.getTitle(), dto.getTitle())) {
            oldDoc.setTitle(dto.getTitle());
        }

        // 文档sp调整
        if (!CollectionUtils.isEqualCollection(oldDoc.getSpCodes(), dto.getSpCodes())) {
            docMgrService.validateSpPerm(calculateOperateSpCodes(oldDoc.getSpCodes(), dto.getSpCodes()), dto.getOperator());
            oldDoc.setSpCodes(dto.getSpCodes());
            docSpService.deleteByDocNo(oldDoc.getDocNo());
            docSpService.batchSave(oldDoc.getDocNo(), oldDoc.getSpCodes());
        }

        // 其他变更，更新入库
        oldDoc.setDesc(dto.getDesc());
        if (oldDoc.getStatus() == DocStatusEnum.PUBLISHED) {
            oldDoc.setStatus(DocStatusEnum.EDIT);
        }
        docMgrService.updateDoc(oldDoc);
    }

    private void updateProductDoc(DocUpdateDTO dto) {
        String docNo = dto.getDocNo();
        // 产品编码调整
        List<DocProductRelateEntity> oldRelateProducts = docProductRelateRepository.findByDocNo(docNo);
        Map<String, DocProductRelateEntity> oldRelateProductMap = Maps.newHashMapWithExpectedSize(oldRelateProducts.size());
        oldRelateProducts.forEach(p -> oldRelateProductMap.put(p.getProductCode(), p));
        if (!CollectionUtils.isEqualCollection(oldRelateProducts.stream()
                .map(DocProductRelateEntity::getProductCode).collect(Collectors.toSet()), dto.getProductCodes())) {
            validateProductsWhenUpdateDoc(dto, oldRelateProducts);
            List<String> toBeAdd = new ArrayList<>(dto.getProductCodes().size());
            for (String productCode : dto.getProductCodes()) {
                if (!oldRelateProductMap.containsKey(productCode)) {
                    toBeAdd.add(productCode);
                } else {
                    oldRelateProductMap.remove(productCode);
                }
            }
            List<String> checkProducts = new ArrayList<>(dto.getProductCodes().size() + oldRelateProductMap.size());
            checkProducts.addAll(oldRelateProductMap.values().stream().map(DocProductRelateEntity::getProductCode).collect(Collectors.toList()));
            checkProducts.addAll(toBeAdd);
            docMgrService.validateProductsPerm(checkProducts, dto.getOperator());
            if (!oldRelateProductMap.isEmpty()) {
                docProductRelateRepository.batchDelete(new ArrayList<>(oldRelateProductMap.values()));
            }
            if (CollectionUtils.isNotEmpty(toBeAdd)) {
                docProductRelateRepository.batchSave(toBeAdd.stream().map(p -> {
                    final DocProductRelateEntity dpRelate = new DocProductRelateEntity();
                    dpRelate.setDocNo(docNo);
                    dpRelate.setProductCode(p);
                    return dpRelate;
                }).collect(Collectors.toList()));
            }
        }

        // 文档分类调整
        List<DocCategoryRelateEntity> oldRelateCategories = docCategoryRelateRepository.findByDocNo(docNo);
        Map<Long, DocCategoryRelateEntity> oldRelateCategoryMap = Maps.newHashMapWithExpectedSize(oldRelateCategories.size());
        oldRelateCategories.forEach(p -> oldRelateCategoryMap.put(p.getCategoryId(), p));
        if (!CollectionUtils.isEqualCollection(oldRelateCategories.stream()
                .map(DocCategoryRelateEntity::getCategoryId).collect(Collectors.toSet()), dto.getCategoryIds())) {
            List<Long[]> toBeAdd = new ArrayList<>(dto.getCategoryIds().size());
            for (Long[] categoryIds : dto.getCategoryIds()) {
                final Long categoryId = categoryIds[categoryIds.length - 1];
                if (!oldRelateCategoryMap.containsKey(categoryId)) {
                    toBeAdd.add(categoryIds);
                } else {
                    oldRelateCategoryMap.remove(categoryId);
                }
            }
            doValidateCategories(toBeAdd);
            if (!oldRelateCategoryMap.isEmpty()) {
                docCategoryRelateRepository.batchDelete(new ArrayList<>(oldRelateCategoryMap.values()));
            }
            if (CollectionUtils.isNotEmpty(toBeAdd)) {
                docCategoryRelateRepository.batchSave(toBeAdd.stream().map(c -> {
                    final DocCategoryRelateEntity dcRelate = new DocCategoryRelateEntity();
                    dcRelate.setDocNo(docNo);
                    final Long categoryId = c[c.length - 1];
                    final Integer maxSeqInCategory = docCategoryRelateRepository.findMaxSeqInCategory(categoryId);
                    int seq = 0;
                    if (null != maxSeqInCategory) {
                        seq = maxSeqInCategory + 1;
                    }
                    dcRelate.setSeq(seq);
                    dcRelate.setCategoryId(categoryId);
                    return dcRelate;
                }).collect(Collectors.toList()));
            }
        }
    }

    private List<String> calculateOperateSpCodes(List<String> oldSpCodes, List<String> newSpCodes) {
        Assert.isTrue(CollectionUtils.isNotEmpty(oldSpCodes) &&
                CollectionUtils.isNotEmpty(newSpCodes), "spCodes is necessary");
        // operateSpCodes: sp to be deleted or added
        List<String> operateSpCodes = new ArrayList<>(oldSpCodes.size() + newSpCodes.size());
        Set<String> spCodeSet = new HashSet<>(newSpCodes);
        for (String oldSp : oldSpCodes) {
            if (!spCodeSet.remove(oldSp)) {
                operateSpCodes.add(oldSp);
            }
        }
        operateSpCodes.addAll(new ArrayList<>(spCodeSet));
        return operateSpCodes;
    }

    @Override
    @DSTransactional
    public void arrangeDocsInCategory(DocSeqInCategoryUpdateDTO docSeqUpdateDTO) {
        docCategoryRelateRepository.batchUpdateSeqById(docSeqUpdateDTO.getSequences());
        publisher.publishEvent(ApiTreeRefreshEvent.DEFAULT_EVENT);
    }

    @Override
    @DSTransactional
    public void copySolutionDoc(String sourceSolutionCode, String targetSolutionCode) {
        validateDocNo(targetSolutionCode);
        final DocDTO sourceDoc = find(sourceSolutionCode, PAGES, PAGES_CONTENT, DocIncludesEnum.DOC_API_CATEGORY);
        DOC_NOT_EXISTS.assertNotNull(sourceDoc, sourceSolutionCode);
        final CustomSolutionDTO customSolution = customSolutionMgrFacade.find(targetSolutionCode);
        DOC_SOLUTION_NOT_EXISTS.assertNotNull(customSolution, sourceSolutionCode);
        doCopySolutionDoc(sourceDoc, customSolution);
    }

    @Override
    public void publishHotProduct(List<String> codes) {
        docMgrService.publishHotProduct(codes);
    }

    @Override
    @Async
    public void publishDocMd(String docNo) {
        DocDTO doc = this.find(docNo, DocIncludesEnum.PAGES_CONTENT);
        if (null == doc) {
           return;
        }
        DocTypeEnum docType = doc.getType();
        boolean platformDoc = doc.getDocNo().equals("platform-doc");
        if (platformDoc) {
            docType = DocTypeEnum.PLATFORM;
        }
        if (DocTypeEnum.PLATFORM.equals(docType)) {
            String docNoMerge = platformDoc ? "platform" : "platform-doc";
            DocDTO docMerge = this.find(docNoMerge, PAGES_CONTENT);
            doc.getPages().addAll(docMerge.getPages());
        }
        switch (docType) {
            case PLATFORM:
                Object platformLLms = DocGeneratorFactory.get(GeneratorConstants.MD_PLATFORM_DOC_LLMS_GENERATOR_NAME).generate(doc);
                if (platformLLms instanceof String && StringUtils.isNotBlank((String) platformLLms)) {
                    remoteStorage.update(LLMS_PLATFORM_FILE, (String) platformLLms);
                }
                break;
            case PRODUCT:
                Object productLLms = DocGeneratorFactory.get(GeneratorConstants.MD_PRODUCT_DOC_LLMS_GENERATOR_NAME).generate(doc);
                if (productLLms instanceof String && StringUtils.isNotBlank((String) productLLms)) {
                    remoteStorage.update(String.format(LLMS_PRODUCT_FILE_FORMAT, docNo), (String) productLLms);
                }
                break;
            default:
                LOGGER.info("ignore other doc type, docNo:{}", docNo);
                break;
        }
    }

    private void doCopySolutionDoc(DocDTO sourceDoc, CustomSolutionDTO customSolution) {
        sourceDoc.setDocNo(customSolution.getSolutionCode());
        sourceDoc.setTitle(customSolution.getSolutionName());
        sourceDoc.setStatus(DocStatusEnum.DRAFT);
        sourceDoc.setPages(copyDocPages(sourceDoc.getPages(), sourceDoc.getDocNo()));
        sourceDoc.setDocVersion("draft");
        docMgrService.copyDoc(sourceDoc);
    }

    private List<DocPageDTO> copyDocPages(List<DocPageDTO> pages, String targetDocNo) {
        if (CollectionUtils.isEmpty(pages)) {
            return Collections.emptyList();
        }
        for (DocPageDTO page : pages) {
            page.setDocNo(targetDocNo);
            page.setChildren(copyDocPages(page.getChildren(), targetDocNo));
        }
        return pages;
    }

    private void doValidateCategories(List<Long[]> categoryIds) {
        for (Long[] categories : categoryIds) {
            Long pid = null;
            for (Long category : categories) {
                // 分类存在
                final DocCategoryDTO docCategory = docCategoryService.find(category);
                if (null == docCategory) {
                    throw new IllegalArgumentException("参数有误，分类信息未找到, categoryId:" + category);
                }

                // 父子分类关系校验
                if (null == pid) {
                    pid = category;
                }
                if (-1 != docCategory.getPid() && !pid.equals(docCategory.getPid())) {
                    throw new YeepayRuntimeException("产品父级分类与子级分类不匹配, 父级分类:{0}, 子级分类:{1}", pid, docCategory.getId());
                }

            }
        }
    }

    private void validateProductsWhenUpdateDoc(DocUpdateDTO dto, List<DocProductRelateEntity> oldRelateProducts) {
        // 产品存在
        dto.setProductCodes(filterInvalidProducts(dto.getProductCodes()));
    }


    private void validateWhenCreateDoc(DocCreateDTO dto) {
        // 文档编码重复
        validateDocNo(dto.getDocNo());
        if (dto.getType().equals(DocTypeEnum.PRODUCT)) {
            // 过滤非法产品编码
            dto.setProductCodes(filterInvalidProducts(dto.getProductCodes()));
            // 校验产品权限
            docMgrService.validateProductsPerm(dto.getProductCodes(), dto.getOperator());
            doValidateProductType(dto.getProductCodes());
            doValidateCategories(dto.getCategoryIds());
        } else if (dto.getType().equals(DocTypeEnum.SOLUTION)) {
            // 校验解决方案
            if (null == customSolutionMgrFacade.find(dto.getSolutionCode())) {
                throw new IllegalArgumentException("自定义解决方案不存在，编码：" + dto.getSolutionCode());
            }
        }
        docMgrService.validateSpPerm(dto.getSpCodes(), dto.getOperator());
    }

    private void doValidateProductType(List<String> productCodes) {
        final ProductQueryReqDTO queryReq = new ProductQueryReqDTO();
        queryReq.setProductCodes(productCodes);
        final List<ProductDTO> preCreatedProducts = productQueryFacade.queryProducts(queryReq);

        // 产品类型检查
        if (CollectionUtils.isNotEmpty(preCreatedProducts)) {
            List<ProductTypeEnum> productTypes = preCreatedProducts.stream().map(ProductDTO::getType).collect(Collectors.toList());
            if (productTypes.contains(ProductTypeEnum.DOCKING)) {
                if (productCodes.size() > 1) {
                    throw new IllegalArgumentException("产品码列表中存在对接产品码。对接产品码不能多选，不能与其他类型产品码同时选择");
                }
            }
        }
    }

    private void validateDocNo(String docNo) {
        DOC_ALREADY_EXISTS.assertIsNull(docMgrService.findOneByDocNo(docNo), docNo);
    }

    private List<String> filterInvalidProducts(List<String> productCodes) {
        List<String> filtered = new ArrayList<>(productCodes.size());
        for (String productCode : productCodes) {
            final ProductDTO product = productFacade.find(productCode);
            if (null == product) {
                LOGGER.warn("product not exists, productCode:{}", productCode);
            } else if (!filtered.contains(productCode)) {
                filtered.add(productCode);
            }
        }
        if (filtered.size() == 0) {
            throw new IllegalArgumentException("参数有误，关联的产品编码均不存在！");
        }
        return filtered;
    }

    /**
     * 产品文档根目录：docs/products/{docNo}
     * 普通文档根目录：docs/open/{docNo}
     * 平台文档根目录：docs/platform
     * @param doc
     * @return
     */
    private String getDocRootDir(DocDTO doc) {
        return DocUtils.docPrefix(doc.getType()) + "/" + doc.getDocNo();
    }

    @PreDestroy
    public void destroy() {
        MoreExecutors.shutdownAndAwaitTermination(docPublishPostWorkExecutor, 20, TimeUnit.SECONDS);
    }
}
