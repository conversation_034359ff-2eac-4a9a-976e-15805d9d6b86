/*
 * Copyright: Copyright (c)2011
 * Company: 易宝支付(YeePay)
 */
package com.yeepay.g3.core.yop.doc.event.handler;

import com.yeepay.boot.components.config.utils.ConfigUtils;
import com.yeepay.g3.core.yop.doc.event.AbstractEventHandler;
import com.yeepay.g3.core.yop.doc.event.Event;
import com.yeepay.g3.core.yop.doc.generator.DocGeneratorFactory;
import com.yeepay.g3.core.yop.doc.generator.GeneratorConstants;
import com.yeepay.g3.core.yop.doc.utils.RemoteStorage;
import com.yeepay.g3.core.yop.doc.utils.config.ConfigEnum;
import com.yeepay.g3.facade.yop.doc.util.DocI18nUtils;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static com.yeepay.g3.core.yop.doc.event.EventConstants.API_TREE_CHANGED;
import static com.yeepay.g3.core.yop.doc.utils.YopConstant.*;

/**
 * title: <br>
 * description: 描述<br>
 * Copyright: Copyright (c)2014<br>
 * Company: 易宝支付(YeePay)<br>
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2021-04-26
 */
@Component
public class ApiTreeEventHandler extends AbstractEventHandler {

    private RemoteStorage remoteStorage;

    @Override
    protected String getEventName() {
        return API_TREE_CHANGED;
    }

    @Override
    public void handle(Event event) {
        doHandle("");
        final List<String> langs = (List<String>) ConfigUtils.getSysConfigParam(ConfigEnum.YOP_DOC_I18N_SUPPORT_LANGS);
        if (CollectionUtils.isNotEmpty(langs)) {
            for (String lang : langs) {
                doHandle(lang);
            }
        }
    }

    private void doHandle(String lang) {
        List<Map<String, Object>> products = new ArrayList<>();
        boolean needHandleLLMs = false;
        if (!StringUtils.isNotBlank(DocI18nUtils.getLangSuffix(lang))) {
            needHandleLLMs = true;
        }
        try {
            // 更新对接产品树
            Object dockingProductTree = DocGeneratorFactory.get(GeneratorConstants.DOCKING_PRODUCT_TREE_GENERATOR_NAME).generate(lang);
            if (null != dockingProductTree) {
                String json = JSON_MAPPER.toJson(dockingProductTree);
                remoteStorage.update(String.format(API_DOCKING_PRODUCT_TREE_JSON_FILE, getLangSuffix(lang)), json);
                if (needHandleLLMs) {
                    products.addAll(extractThirdLevelNodes(JSON_MAPPER.fromJson(json, Object.class)));
                }
            }
        } catch (Exception e) {
            LOGGER.error("更新对接产品树失败", e);
        }
        try {
            // 更新api菜单树
            Object apiTree = DocGeneratorFactory.get(GeneratorConstants.API_TREE_GENERATOR_NAME).generate(lang);
            if (null != apiTree) {
                String json = JSON_MAPPER.toJson(apiTree);
                remoteStorage.update(String.format(API_MENU_TREE_JSON_FILE, getLangSuffix(lang)), json);
                if (needHandleLLMs) {
                    products.addAll(extractThirdLevelNodes(JSON_MAPPER.fromJson(json, Object.class)));
                }
            }
        } catch (Exception e) {
            LOGGER.error("更新api菜单树失败", e);
        }

        try {
            if (needHandleLLMs && CollectionUtils.isNotEmpty(products)) {
                // 生成Markdown文档
                String markdown = toMarkDown(products);
                // 上传Markdown文档
                remoteStorage.update(LLMS_PRODUCT_CENTER_FILE, markdown);
            }
        } catch (Exception e) {
            LOGGER.error("生成产品中心Markdown文档失败", e);
        }

    }


    private String toMarkDown(List<Map<String, Object>> thirdLevelNodes) {
        StringBuilder markdown = new StringBuilder();
        markdown.append("# 产品中心\n\n");
        markdown.append("## 产品列表\n\n");
        String accessPrefix = ConfigUtils.getSysConfigParam(ConfigEnum.YOP_MD_DOC_ACCESS_PREFIX, String.class);
        // 生成Markdown列表
        for (Map<String, Object> node : thirdLevelNodes) {
            String code = (String) node.get("code");
            String name = (String) node.get("name");
            String path = (String) node.get("path");

            // 添加Markdown列表项
            markdown.append("- ").append(path).append("(").append(code).append(") : ").append("[").append(name).append("](")
                    .append(accessPrefix).append("/product/").append(code).append("/llms.txt").append(")\n");
        }

        return markdown.toString();
    }

    /**
     * 提取第三层级的节点信息
     * @param dockingProductTree 对接产品树
     * @return 第三层级节点列表，每个节点包含code和name
     */
    @SuppressWarnings("unchecked")
    private List<Map<String, Object>> extractThirdLevelNodes(Object dockingProductTree) {
        List<Map<String, Object>> thirdLevelNodes = new ArrayList<>();

        if (dockingProductTree instanceof List) {
            List<Map<String, Object>> firstLevel = (List<Map<String, Object>>) dockingProductTree;

            // 遍历第一层
            for (Map<String, Object> firstNode : firstLevel) {
                if (firstNode.containsKey("children")) {
                    List<Map<String, Object>> secondLevel = (List<Map<String, Object>>) firstNode.get("children");

                    // 遍历第二层
                    for (Map<String, Object> secondNode : secondLevel) {
                        if (secondNode.containsKey("children")) {
                            List<Map<String, Object>> thirdLevel = (List<Map<String, Object>>) secondNode.get("children");

                            // 遍历第三层，提取code和name
                            for (Map<String, Object> thirdNode : thirdLevel) {
                                Map<String, Object> nodeInfo = new HashMap<>();
                                nodeInfo.put("code", thirdNode.get("code"));
                                nodeInfo.put("name", thirdNode.get("name"));
                                // 可选：添加完整路径信息
                                nodeInfo.put("path", firstNode.get("name") + " > " +
                                        ("_DEFAULT".equals(secondNode.get("code")) ? "" : secondNode.get("name") + " > ") +
                                        thirdNode.get("name"));
                                thirdLevelNodes.add(nodeInfo);
                            }
                        }
                    }
                }
            }
        }

        return thirdLevelNodes;
    }

    @Autowired
    public void setRemoteStorage(RemoteStorage remoteStorage) {
        this.remoteStorage = remoteStorage;
    }
}
