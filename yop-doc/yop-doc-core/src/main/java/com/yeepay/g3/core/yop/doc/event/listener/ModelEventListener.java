/*
 * Copyright: Copyright (c)2011
 * Company: 易宝支付(YeePay)
 */
package com.yeepay.g3.core.yop.doc.event.listener;

import com.yeepay.boot.components.config.utils.ConfigUtils;
import com.yeepay.g3.core.yop.doc.generator.DocGeneratorFactory;
import com.yeepay.g3.core.yop.doc.generator.GeneratorConstants;
import com.yeepay.g3.core.yop.doc.utils.config.ConfigEnum;
import com.yeepay.g3.event.yop.sys.model.ModelBatchCreateEvent;
import com.yeepay.g3.event.yop.sys.model.ModelChangedEvent;
import com.yeepay.g3.event.yop.sys.model.ModelDeletedEvent;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.springframework.context.event.EventListener;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;

/**
 * title: 模型事件监听<br>
 * description: 描述<br>
 * Copyright: Copyright (c)2014<br>
 * Company: 易宝支付(YeePay)<br>
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2022/4/1
 */
@Component
@Slf4j
public class ModelEventListener extends AbstractEventListener {

    @EventListener
    public void onEvent(ModelBatchCreateEvent modelBatchCreateEvent) {
        log.info("receive a ModelBatchCreateEvent, models:{}", modelBatchCreateEvent);
        try {
            if (MapUtils.isEmpty(modelBatchCreateEvent.getModels())) {
                return;
            }
            final List<String> langs = new ArrayList<>(),
                    configLangs = (List<String>) ConfigUtils.getSysConfigParam(ConfigEnum.YOP_DOC_I18N_SUPPORT_LANGS);
            langs.add("");
            if (CollectionUtils.isNotEmpty(configLangs)) {
                langs.addAll(configLangs);
            }
            modelBatchCreateEvent.getModels().forEach((apiGroup, modelList) -> modelList.forEach(modelName -> {
                for (String lang : langs) {
                    doGenerateModel(apiGroup, modelName, lang);
                }
            }));
        } catch (Throwable e) {
            log.error("error when handle ModelBatchCreateEvent, ex:", e);
        }
    }

    private void doGenerateModel(String apiGroup, String modelName, String lang) {
        try {
            DocGeneratorFactory.get(GeneratorConstants.API_MODELS_GENERATOR_NAME).generate(apiGroup, modelName, lang);
        } catch (Exception e) {
            log.warn("error when generate model, apiGroup:" + apiGroup + ", modelName:" + modelName, e);
        }
    }

    @EventListener
    public void onEvent(ModelChangedEvent modelChangedEvent) {
        log.info("receive a ModelChangedEvent, content:{}", modelChangedEvent);
        final List<String> langs = new ArrayList<>(),
                configLangs = (List<String>) ConfigUtils.getSysConfigParam(ConfigEnum.YOP_DOC_I18N_SUPPORT_LANGS);
        langs.add("");
        if (CollectionUtils.isNotEmpty(configLangs)) {
            langs.addAll(configLangs);
        }
        for (String lang : langs) {
            doGenerateModel(modelChangedEvent.getApiGroup(), modelChangedEvent.getName(), lang);
        }
    }

    @EventListener
    public void onEvent(ModelDeletedEvent event) {
        log.info("receive a ModelDeletedEvent, content:{}", event);
        try {
            String modelName = event.getName();
            String apiGroup = event.getApiGroup();
            remoteStorage.batchDelete("docs/models/" + apiGroup + "/" + modelName + ".json");
        } catch (Throwable e) {
            log.error("error when handle ModelDeletedEvent, ex:", e);
        }
    }
}
