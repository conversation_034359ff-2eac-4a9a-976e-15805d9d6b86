/*
 * Copyright: Copyright (c)2011
 * Company: 易宝支付(YeePay)
 */
package com.yeepay.g3.core.yop.doc.generator;

import com.yeepay.g3.core.yop.doc.service.DocI18nService;
import com.yeepay.g3.core.yop.doc.service.DocUrlService;
import com.yeepay.g3.core.yop.doc.utils.DocI18nConverter;
import com.yeepay.g3.core.yop.doc.utils.RemoteStorage;
import com.yeepay.g3.core.yop.doc.utils.converter.UrlConverter;
import com.yeepay.g3.core.yop.doc.utils.mapper.JsonMapper;
import com.yeepay.g3.i18n.dto.I18nResourceDTO;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;

import javax.annotation.PostConstruct;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;

/**
 * title: <br>
 * description: 描述<br>
 * Copyright: Copyright (c)2014<br>
 * Company: 易宝支付(YeePay)<br>
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2021-04-22
 */
public abstract class AbstractDocGenerator implements DocGenerator {

    private static final Logger LOGGER = LoggerFactory.getLogger(DocGenerator.class);
    protected static final JsonMapper NON_NULL_MAPPER = JsonMapper.nonNullMapper();

    protected RemoteStorage remoteStorage;
    protected DocI18nService docI18nService;
    protected DocI18nConverter docI18nConverter;
    protected DocUrlService docUrlService;
    protected UrlConverter urlConverter;

    @Autowired
    public void setRemoteStorage(RemoteStorage remoteStorage) {
        this.remoteStorage = remoteStorage;
    }

    @Autowired
    public void setDocI18nService(DocI18nService docI18nService) {
        this.docI18nService = docI18nService;
    }

    @Autowired
    public void setDocI18nConverter(DocI18nConverter docI18nConverter) {
        this.docI18nConverter = docI18nConverter;
    }

    @Autowired
    public void setDocUrlService(DocUrlService docUrlService) {
        this.docUrlService = docUrlService;
    }

    @PostConstruct
    public void register() {
        DocGeneratorFactory.register(this);
        urlConverter = url -> docUrlService.convMdUrl(url);
    }

    protected String getLangSuffix(String lang) {
        return StringUtils.isNotBlank(lang) ? ("_" + lang) : "";
    }

    protected Map<String, String> toI18nKVmap(Map<String, List<I18nResourceDTO>> resources, String lang) {
        Map<String, String> result = new LinkedHashMap<>();
        if (MapUtils.isNotEmpty(resources)) {
            final List<I18nResourceDTO> resourceItems = resources.get(lang);
            if (CollectionUtils.isNotEmpty(resourceItems)) {
                resourceItems.forEach(item -> result.put(item.getI18nKey(), item.getContent()));
            }
        }

        return result;
    }

    protected void zipAndUpload(String zipFile, String workDir, String remotePath) {
        remoteStorage.zipAndBatchUpload(zipFile, workDir, remotePath);
    }
}
