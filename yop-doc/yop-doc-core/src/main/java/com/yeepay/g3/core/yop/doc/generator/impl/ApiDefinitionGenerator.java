/*
 * Copyright: Copyright (c)2011
 * Company: 易宝支付(YeePay)
 */
package com.yeepay.g3.core.yop.doc.generator.impl;

import com.fasterxml.jackson.databind.JsonNode;
import com.google.common.collect.ImmutableMap;
import com.google.common.collect.Maps;
import com.yeepay.boot.components.config.utils.ConfigUtils;
import com.yeepay.g3.core.yop.doc.dto.api.ApiDefinition;
import com.yeepay.g3.core.yop.doc.dto.api.ApiDocItem;
import com.yeepay.g3.core.yop.doc.generator.AbstractDocGenerator;
import com.yeepay.g3.core.yop.doc.generator.GeneratorConstants;
import com.yeepay.g3.core.yop.doc.service.ApiService;
import com.yeepay.g3.core.yop.doc.utils.ApiMdUtils;
import com.yeepay.g3.core.yop.doc.utils.SwaggerUtil;
import com.yeepay.g3.core.yop.doc.utils.YopConstant;
import com.yeepay.g3.core.yop.doc.utils.config.ConfigEnum;
import com.yeepay.g3.core.yop.doc.utils.converter.UrlConverter;
import com.yeepay.g3.core.yop.doc.utils.swagger.SwaggerExporter;
import com.yeepay.g3.core.yop.doc.utils.swagger.SwaggerUtils;
import com.yeepay.g3.core.yop.doc.utils.swagger.impl.ApiFoxSwaggerOptions;
import com.yeepay.g3.facade.yop.api.dto.ApiDefineDTO;
import com.yeepay.g3.facade.yop.codegen.dto.ApiDefineSampleCodeGenerateRequest;
import com.yeepay.g3.facade.yop.codegen.dto.ApiSampleCodeGenerateRequest;
import com.yeepay.g3.facade.yop.codegen.dto.ApiSampleCodes;
import com.yeepay.g3.facade.yop.codegen.enumtype.LangEnum;
import com.yeepay.g3.facade.yop.codegen.enumtype.SampleCodePlusEnum;
import com.yeepay.g3.facade.yop.codegen.facade.SampleCodeGenerateFacade;
import com.yeepay.g3.facade.yop.doc.util.DocI18nUtils;
import com.yeepay.g3.facade.yop.sys.dto.*;
import com.yeepay.g3.facade.yop.sys.enums.SecurityReqTypeEnum;
import com.yeepay.g3.facade.yop.sys.enums.SpiStatusEnum;
import com.yeepay.g3.facade.yop.sys.enums.SwaggerDataFormatEnum;
import com.yeepay.g3.facade.yop.sys.facade.*;
import com.yeepay.g3.utils.rmi.RemoteServiceFactory;
import io.swagger.v3.oas.models.OpenAPI;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import java.io.IOException;
import java.util.*;

import static com.yeepay.g3.core.yop.doc.utils.YopConstant.*;
import static com.yeepay.g3.facade.yop.doc.util.DocUtils.toApiAccessUri;

/**
 * title: API定义自动发布到ceph<br>
 * description: 存储：/docs/apis/{apiId}/definition{_lang}.json<br>
 * Copyright: Copyright (c)2014<br>
 * Company: 易宝支付(YeePay)<br>
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2021/8/12
 */
@Component
@Slf4j
public class ApiDefinitionGenerator extends AbstractDocGenerator {
    private static final Logger LOGGER = LoggerFactory.getLogger(ApiDefinitionGenerator.class);

    private static final Map<String, Integer> SAMPLE_CODE_ORDER;
    private static final Comparator<String> SAMPLE_CODE_COMPARATOR;

    static {
        SAMPLE_CODE_ORDER = Maps.newHashMapWithExpectedSize(LangEnum.values().length + SampleCodePlusEnum.values().length);
        int i = 0;
        for (LangEnum value : LangEnum.values()) {
            SAMPLE_CODE_ORDER.put(value.name(), i++);
        }
        for (SampleCodePlusEnum value : SampleCodePlusEnum.values()) {
            SAMPLE_CODE_ORDER.put(value.name(), i++);
        }
        SAMPLE_CODE_COMPARATOR = Comparator.comparingInt(o -> SAMPLE_CODE_ORDER.getOrDefault(o, Integer.MAX_VALUE));
    }


    private final ApiGroupFacade apiGroupFacade = RemoteServiceFactory.getService(ApiGroupFacade.class);
    private final SampleCodeGenerateFacade sampleCodeGenerateFacade = RemoteServiceFactory.getService(SampleCodeGenerateFacade.class);
    private final SecurityReqMgrFacade securityReqMgrFacade = RemoteServiceFactory.getService(SecurityReqMgrFacade.class);
    private final ApiPublishQueryFacade apiPublishQueryFacade = RemoteServiceFactory.getService(ApiPublishQueryFacade.class);
    private final ApiMgrFacade apiMgrFacade = RemoteServiceFactory.getService(ApiMgrFacade.class);
    private final ModelQueryFacade modelQueryFacade = RemoteServiceFactory.getService(ModelQueryFacade.class);
    private final ApiErrcodeFacade apiErrcodeFacade = RemoteServiceFactory.getService(ApiErrcodeFacade.class);
    private final SpiQueryFacade spiQueryFacade = RemoteServiceFactory.getService(SpiQueryFacade.class);

    private ApiService apiService;


    @Autowired
    public void setApiService(ApiService apiService) {
        this.apiService = apiService;
    }

    @Override
    public String name() {
        return GeneratorConstants.API_DEFINITION_GENERATOR_NAME;
    }

    @Override
    public Object generate(Object... args) {
        ApiDocItem api = (ApiDocItem) args[0];
        String lang = null;
        if (args.length > 1) {
            lang = (String) args[1];
        }
        if (OLD_API.equals(api.getApiVersion())) {
            generateV1Api(api, lang);
        } else {
            generateV2Api(api, lang);
        }
        return true;
    }

    private void generateV1Api(ApiDocItem api, String lang) {
        try {
            final ApiDefineDTO apiDefine = apiService.findByUri(api.getPath());
            final ApiGroupDTO apiGroup = apiGroupFacade.findByApiGroupCode(apiDefine.getApiGroup());
            if (null != apiGroup) {
                List<SecurityReqDTO> securities = securityReqMgrFacade.findByTypeAndValue(SecurityReqTypeEnum.API, apiDefine.getApiUri());
                if (CollectionUtils.isEmpty(securities)) {
                    securities = apiGroup.getSecurity();
                }
                generateApiDefinition(api, apiDefine, apiGroup, securities, lang);
//                generateV1ApiSwagger(api, apiDefine, apiGroup, securities, lang);
                generateV1ApiMd(api, apiDefine, apiGroup, securities, lang);
            } else {
                LOGGER.error("fail to generateV1Api, apiGroup not exists, apiId:{}, apiGroup:{}.", api.getApiId(), api.getApiGroup());
            }
        } catch (Exception e) {
            LOGGER.error("fail to generateV1Api, apiId:" + api.getApiId() + ",ex:", e);
        }
    }

    private void generateV1ApiMd(ApiDocItem api, ApiDefineDTO apiDefine, ApiGroupDTO apiGroup, List<SecurityReqDTO> securities, String lang) {
        if (StringUtils.isNotBlank(DocI18nUtils.getLangSuffix(lang))) {
            return;
        }
        Map<String, String> sampleCodesMap = generateSampleCode(apiGroup, apiDefine);
        ApiDefinition apiDefinition = ApiDefinition.fromV1Api(api, apiDefine, securities, sampleCodesMap, "");
        ApiDTO apiDTO = apiMgrFacade.findByPath(apiDefine.getApiUri());
        Map<String, Set<String>> rootModelNames = new HashMap<>();
        List<SpiDTO> spis = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(apiDTO.getCallbacks())) {
            apiDTO.getCallbacks().forEach(callback -> {
                SpiDTO spiDTO = spiQueryFacade.find(callback);
                if (null != spiDTO && SpiStatusEnum.ENABLED.equals(spiDTO.getBasic().getStatus())) {
                    spis.add(spiDTO);
                    spiDTO.getRequest().getRequestBody().getContents().forEach((k, v) -> {
                        try {
                            JsonNode jsonNode = NON_NULL_MAPPER.getMapper().readTree(v.getSchema());
                            String modelName = jsonNode.get("$ref").asText().replace("#/components/schemas/", "");
                            rootModelNames.computeIfAbsent(spiDTO.getBasic().getApiGroup(), p -> new HashSet<>()).add(modelName);
                        } catch (IOException e) {
                            throw new RuntimeException(e);
                        }
                    });
                }
            });
        }
        Map<String, List<String>> modelMap = new HashMap<>();
        rootModelNames.forEach((k, v) -> modelMap.put(k, new ArrayList<>(v)));
        List<ModelDTO> models = modelQueryFacade.findModelsRecursively(modelMap);
        Map<String, String> modelParamMap = new HashMap<>();
        if (CollectionUtils.isNotEmpty(models)) {
            models.forEach(model -> {
                try {
                    modelParamMap.put(model.getApiGroup() + "." + model.getName(), NON_NULL_MAPPER.getMapper().writeValueAsString(SwaggerUtil.parseModelSchema(model)));
                } catch (Exception e) {
                    e.printStackTrace();
                }
            });
        }
        List<ErrorCodeDTO> errorCodeDTOS = apiErrcodeFacade.findByApiId(apiDTO.getApiId());
        List<Map<String, String>> apiErrcodeList = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(errorCodeDTOS)) {
            errorCodeDTOS.stream()
                    .filter(apiErrcode -> StringUtils.isNotEmpty(apiErrcode.getSubErrorMsg()))
                    .forEach(apiErrcode -> {
                        Map<String, String> map = new HashMap<>();
                        map.put("subErrorCode", apiErrcode.getSubErrorCode());
                        map.put("subErrorMsg", apiErrcode.getSubErrorMsg());
                        map.put("innerSolution", apiErrcode.getInnerSolution());
                        map.put("outerSolution", apiErrcode.getOuterSolution());
                        apiErrcodeList.add(map);
                    });
        }

        List<Map<String, String>> callbacks = new ArrayList<>();
        String accessPrefix = ConfigUtils.getSysConfigParam(ConfigEnum.YOP_MD_DOC_ACCESS_PREFIX, String.class);
        spis.forEach(spi -> {
            Map<String, String> map = new HashMap<>();
            map.put("name", spi.getBasic().getName());
            map.put("title", spi.getBasic().getTitle());
            map.put("description", spi.getBasic().getDescription());
            map.put("referenceUrl", accessPrefix + "/notify/" + spi.getBasic().getName() + ".md");
            callbacks.add(map);
        });

        String apiAccessUri = toApiAccessUri("options", api.getPath());
        String apiMd = new ApiMdUtils(apiDefinition, modelParamMap, apiErrcodeList, callbacks, this.urlConverter).convertToMarkdown();
        remoteStorage.update(String.format(API_MARKDOWN_FILE_FORMAT, apiAccessUri), apiMd);
    }

    private void generateV1ApiSwagger(ApiDocItem api, ApiDefineDTO apiDefine, ApiGroupDTO apiGroup, List<SecurityReqDTO> securities, String lang) {
        if (StringUtils.isNotBlank(DocI18nUtils.getLangSuffix(lang))) {
            return;
        }
        try {
            Map<String, List<SecurityReqDTO>> securityMapping = Collections.singletonMap(apiDefine.getApiUri(), securities);
            final OpenAPI openAPI = SwaggerUtils.generate(apiGroup, Collections.singletonList(apiDefine), securityMapping,
                    false, new ApiFoxSwaggerOptions(apiGroup.getApiGroupCode()));
            if (MapUtils.isEmpty(openAPI.getPaths())) {
                log.warn("swagger generate fail for empty paths, apiGroup:{}, oldApi:{}.", apiGroup.getApiGroupCode(), api);
            } else {
                String swagger = SwaggerUtil.getJsonMapper().writeValueAsString(openAPI);
                remoteStorage.update(String.format(YopConstant.API_SWAGGER_JSON_FILE_FORMAT, api.getUri()), swagger);
            }
        } catch (Exception e) {
            log.error("swagger generate fail for empty paths, apiGroup:{}, oldApi:{}.", apiGroup.getApiGroupCode(), api, e);
        }
    }

    private void generateApiDefinition(ApiDocItem api, ApiDefineDTO apiDefine, ApiGroupDTO apiGroup, List<SecurityReqDTO> securities, String lang) {
        Map<String, String> sampleCode = generateSampleCode(apiGroup, apiDefine);
        if (StringUtils.isNotBlank(lang)) {
            docI18nConverter.convV1Api(apiDefine, lang);
        }
        ApiDefinition apiDefinition = ApiDefinition.fromV1Api(api, apiDefine, securities, sampleCode, lang);
        remoteStorage.update(String.format(API_DEFINITION_JSON_FILE_FORMAT, api.getApiId(), getLangSuffix(lang)), NON_NULL_MAPPER.toJson(apiDefinition));
    }

    private Map<String, String> generateSampleCode(ApiGroupDTO apiGroup, ApiDefineDTO apiDefine) {
        Map<String, String> sampleCode = Maps.newTreeMap(SAMPLE_CODE_COMPARATOR);
        try {
            final ApiDefineSampleCodeGenerateRequest sampleCodeRequest = new ApiDefineSampleCodeGenerateRequest();
            sampleCodeRequest.setApiGroup(apiGroup);
            sampleCodeRequest.setApiDefines(Collections.singletonList(apiDefine));
            final Map<String, ApiSampleCodes> sampleGenerated = sampleCodeGenerateFacade.generate(sampleCodeRequest);
            if (MapUtils.isNotEmpty(sampleGenerated)) {
                final ApiSampleCodes apiSampleCodes = sampleGenerated.get(apiDefine.getApiUri());
                if (null != apiSampleCodes) {
                    final EnumMap<LangEnum, String> sampleCodesMap = apiSampleCodes.getSampleCodes();
                    final Map<String, String> sampleCodesPlus = apiSampleCodes.getSampleCodesPlus();
                    if (MapUtils.isNotEmpty(sampleCodesMap)) {
                        sampleCodesMap.forEach((k, v) -> sampleCode.put(k.name(), v));
                    }
                    if (MapUtils.isNotEmpty(sampleCodesPlus)) {
                        sampleCode.putAll(sampleCodesPlus);
                    }
                }
            }
        } catch (Exception e) {
            LOGGER.error("fail to generateSampleCode, apiUri:" + apiDefine.getApiUri() + "ex:", e);
        }
        return sampleCode;
    }

    /*******************************新版***********************************/
    private void generateV2Api(ApiDocItem api, String lang) {
        try {
            final ApiExportRequest apiExportReq = new ApiExportRequest();
            apiExportReq.setDataFormat(SwaggerDataFormatEnum.JSON);
            apiExportReq.setApiGroup(api.getApiGroup());
            apiExportReq.setApis(Collections.singletonList(api.getApiId()));
            final ApiExportResult apiExportResult = apiPublishQueryFacade.exportForDoc(apiExportReq);
            final String swagger = apiExportResult.getData();
            OpenAPI openAPI = SwaggerUtil.getJsonMapper().readValue(swagger, OpenAPI.class);
            final ApiGroupDTO apiGroup = apiGroupFacade.findByApiGroupCode(api.getApiGroup());
            Map<String, String> sampleCode = generateSampleCode(api, apiGroup, swagger);
            List<SecurityReqDTO> securities = securityReqMgrFacade.findByTypeAndValue(SecurityReqTypeEnum.API, api.getApiId());
            if (CollectionUtils.isEmpty(securities)) {
                securities = apiGroup.getSecurity();
            }
            generateV2ApiDefinition(api, openAPI, securities, sampleCode, lang);
//            generateV2ApiSwagger(api, apiGroup, securities, lang);
            generateV2ApiMd(api, openAPI, securities, sampleCode, lang);
        } catch (Exception e) {
            LOGGER.error("fail to generateV2Api, apiId:" + api.getApiId() + ", ex:", e);
        }
    }

    private void generateV2ApiMd(ApiDocItem api, OpenAPI openAPI, List<SecurityReqDTO> securities, Map<String, String> sampleCode, String lang) {
        if (StringUtils.isNotBlank(DocI18nUtils.getLangSuffix(lang))) {
            return;
        }
        ApiDefinition apiDefinition = ApiDefinition.fromV2Api(api, openAPI, securities, sampleCode, lang);
        Map<String, Set<String>> rootModelNames = new HashMap<>();
        ApiDTO apiDTO = apiPublishQueryFacade.findLatestPublishedApi(api.getApiId());
        List<String> apiDirectedRefModels = apiDTO.getDirectedRefModels();
        if (CollectionUtils.isNotEmpty(apiDirectedRefModels)) {
            rootModelNames.computeIfAbsent(apiDTO.getBasic().getApiGroup(), k -> new HashSet<>()).addAll(apiDirectedRefModels);
        }
        List<SpiDTO> spis = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(apiDTO.getCallbacks())) {
            apiDTO.getCallbacks().forEach(callback -> {
                SpiDTO spiDTO = spiQueryFacade.find(callback);
                if (null != spiDTO && SpiStatusEnum.ENABLED.equals(spiDTO.getBasic().getStatus())) {
                    spis.add(spiDTO);
                    spiDTO.getRequest().getRequestBody().getContents().forEach((k, v) -> {
                        try {
                            JsonNode jsonNode = NON_NULL_MAPPER.getMapper().readTree(v.getSchema());
                            String modelName = jsonNode.get("$ref").asText().replace("#/components/schemas/", "");
                            rootModelNames.computeIfAbsent(spiDTO.getBasic().getApiGroup(), p -> new HashSet<>()).add(modelName);
                        } catch (IOException e) {
                            LOGGER.error("fail to parse schema, apiId:" + callback + ", ex:", e);
                        }
                    });
                }
            });
        }
        Map<String, List<String>> modelMap = new HashMap<>();
        rootModelNames.forEach((k, v) -> modelMap.put(k, new ArrayList<>(v)));
        List<ModelDTO> models = modelQueryFacade.findModelsRecursively(modelMap);
        Map<String, String> modelParamMap = new HashMap<>();
        if (CollectionUtils.isNotEmpty(models)) {
            models.forEach(model -> {
                try {
                    modelParamMap.put(model.getApiGroup() + "." + model.getName(), NON_NULL_MAPPER.getMapper().writeValueAsString(SwaggerUtil.parseModelSchema(model)));
                } catch (Exception e) {
                    log.error("fail to generate modelParamMap, modelName:" + model.getName() + ", ex:", e);
                }
            });
        }
        List<ErrorCodeDTO> errorCodeDTOS = apiErrcodeFacade.findByApiId(api.getApiId());
        List<Map<String, String>> apiErrcodeList = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(errorCodeDTOS)) {
            errorCodeDTOS.stream()
                    .filter(apiErrcode -> StringUtils.isNotEmpty(apiErrcode.getSubErrorMsg()))
                    .forEach(apiErrcode -> {
                        Map<String, String> map = new HashMap<>();
                        map.put("subErrorCode", apiErrcode.getSubErrorCode());
                        map.put("subErrorMsg", apiErrcode.getSubErrorMsg());
                        map.put("innerSolution", apiErrcode.getInnerSolution());
                        map.put("outerSolution", apiErrcode.getOuterSolution());
                        apiErrcodeList.add(map);
                    });
        }

        String accessPrefix = ConfigUtils.getSysConfigParam(ConfigEnum.YOP_MD_DOC_ACCESS_PREFIX, String.class);

        List<Map<String, String>> callbacks = new ArrayList<>();
        spis.forEach(spi -> {
            Map<String, String> map = new HashMap<>();
            map.put("name", spi.getBasic().getName());
            map.put("title", spi.getBasic().getTitle());
            map.put("description", spi.getBasic().getDescription());
            map.put("referenceUrl", accessPrefix + "/notify/" + spi.getBasic().getName() + ".md");
            callbacks.add(map);
        });

        String apiAccessUri = toApiAccessUri(api.getMethod(), api.getPath());
        String apiMd = new ApiMdUtils(apiDefinition, modelParamMap, apiErrcodeList, callbacks, this.urlConverter).convertToMarkdown();
        remoteStorage.update(String.format(API_MARKDOWN_FILE_FORMAT, apiAccessUri), apiMd);
    }

    private void generateV2ApiSwagger(ApiDocItem apiDocItem, ApiGroupDTO apiGroup, List<SecurityReqDTO> securities, String lang) {
        if (StringUtils.isNotBlank(DocI18nUtils.getLangSuffix(lang))) {
            return;
        }
        try {
            ApiDTO api = apiPublishQueryFacade.findLatestPublishedApi(apiDocItem.getApiId());
            Set<String> rootModelNames = new HashSet<>();
            if (CollectionUtils.isNotEmpty(api.getDirectedRefModels())) {
                rootModelNames.addAll(api.getDirectedRefModels());
            }
            List<ModelDTO> models = null;
            if (rootModelNames.size() > 0) {
                models = modelQueryFacade.findModelsRecursively(ImmutableMap.of(apiGroup.getApiGroupCode(), new ArrayList<>(rootModelNames)));
            }
            api.setSecurity(securities);
            final OpenAPI openAPI = SwaggerExporter.export(apiGroup, Collections.singletonList(api), null, null,
                    models, new ApiFoxSwaggerOptions(apiGroup.getApiGroupCode()));
            if (MapUtils.isEmpty(openAPI.getPaths())) {
                log.warn("swagger generate fail for empty paths, apiGroup:{}, newApi:" + NON_NULL_MAPPER.toJson(api));
            } else {
                String swagger = SwaggerUtil.getJsonMapper().writeValueAsString(openAPI);
                remoteStorage.update(String.format(YopConstant.API_SWAGGER_JSON_FILE_FORMAT, apiDocItem.getUri()), swagger);
            }
        } catch (Exception e) {
            log.error("swagger generate fail, apiGroup:{}, newApi:", apiGroup.getApiGroupCode(), NON_NULL_MAPPER.toJson(apiDocItem), e);
        }
    }

    private void generateV2ApiDefinition(ApiDocItem api, OpenAPI openAPI, List<SecurityReqDTO> securities, Map<String, String> sampleCode, String lang) {
        ApiDefinition apiDefinition = ApiDefinition.fromV2Api(api, openAPI, securities, sampleCode, lang);

        if (StringUtils.isNotBlank(lang)) {
            docI18nConverter.convV2Api(apiDefinition, lang);
        }
        remoteStorage.update(String.format(API_DEFINITION_JSON_FILE_FORMAT, api.getApiId(), getLangSuffix(lang)), NON_NULL_MAPPER.toJson(apiDefinition));
    }

    private Map<String, String> generateSampleCode(ApiDocItem api, ApiGroupDTO apiGroup, String swagger) {
        Map<String, String> sampleCode = Maps.newTreeMap(SAMPLE_CODE_COMPARATOR);
        try {
            final ApiSampleCodeGenerateRequest request = new ApiSampleCodeGenerateRequest();
            request.setApiGroup(apiGroup);
            request.setOpenApiContent(swagger);
            final Map<String, ApiSampleCodes> sampleGenerated = sampleCodeGenerateFacade.generate(request);
            if (MapUtils.isNotEmpty(sampleGenerated)) {
                final ApiSampleCodes apiSampleCodes = sampleGenerated.get(api.getPath() + ":" + api.getMethod());
                if (null != apiSampleCodes) {
                    final EnumMap<LangEnum, String> sampleCodesMap = apiSampleCodes.getSampleCodes();
                    final Map<String, String> sampleCodesPlus = apiSampleCodes.getSampleCodesPlus();
                    if (MapUtils.isNotEmpty(sampleCodesMap)) {
                        sampleCodesMap.forEach((k, v) -> sampleCode.put(k.name(), v));
                    }
                    if (MapUtils.isNotEmpty(sampleCodesPlus)) {
                        sampleCode.putAll(sampleCodesPlus);
                    }
                }
            }
        } catch (Exception e) {
            LOGGER.error("fail to generateSampleCode, apiId:" + api.getApiId() + "ex:", e);
        }
        return sampleCode;
    }
}
