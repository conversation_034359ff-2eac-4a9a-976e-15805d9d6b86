/*
 * Copyright: Copyright (c)2011
 * Company: 易宝支付(YeePay)
 */
package com.yeepay.g3.core.yop.doc.generator.impl;

import com.google.common.collect.Maps;
import com.yeepay.g3.facade.yop.doc.enums.DocCategoryScopeEnum;
import org.springframework.stereotype.Component;

import java.util.LinkedList;
import java.util.List;
import java.util.Map;

import static com.yeepay.g3.core.yop.doc.generator.GeneratorConstants.DOCKING_PRODUCT_TREE_GENERATOR_NAME;

/**
 * title: <br>
 * description: 描述<br>
 * Copyright: Copyright (c)2014<br>
 * Company: 易宝支付(YeePay)<br>
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2024/7/9 10:51
 */
@Component
public class DockingProductTreeGenerator extends AbstractApiNodeGenerator {
    @Override
    public String name() {
        return DOCKING_PRODUCT_TREE_GENERATOR_NAME;
    }

    @Override
    public Object generate(Object... args) {
        String lang = (String) args[0];
        List<Node> result = new LinkedList<>();
        Map<String, Node> nodeMap = Maps.newHashMap();
        fillDocCategoryNodes(result, nodeMap, lang, DocCategoryScopeEnum.DOCKING.name());
        List<String> docs = fillDocNodes(nodeMap, lang, DocCategoryScopeEnum.DOCKING.name());
        fillApiCategoryNodes(docs, nodeMap, lang);
        fillApiItems(docs, nodeMap, lang);
        return result;
    }
}
