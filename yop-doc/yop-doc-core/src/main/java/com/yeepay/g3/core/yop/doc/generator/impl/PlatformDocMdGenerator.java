/*
 * Copyright: Copyright (c)2011
 * Company: 易宝支付(YeePay)
 */

package com.yeepay.g3.core.yop.doc.generator.impl;

import com.yeepay.boot.components.config.utils.ConfigUtils;
import com.yeepay.g3.core.yop.doc.generator.AbstractDocGenerator;
import com.yeepay.g3.core.yop.doc.utils.config.ConfigEnum;
import com.yeepay.g3.core.yop.doc.utils.converter.MdUrlConverterUtils;
import com.yeepay.g3.facade.yop.doc.dto.v2.DocContentBlock;
import com.yeepay.g3.facade.yop.doc.dto.v2.DocDTO;
import com.yeepay.g3.facade.yop.doc.dto.v2.DocPageDTO;
import com.yeepay.g3.facade.yop.doc.enums.v2.DocPageTypeEnum;
import com.yeepay.g3.facade.yop.doc.util.DocUtils;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;

import static com.yeepay.g3.core.yop.doc.generator.GeneratorConstants.MD_PLATFORM_DOC_LLMS_GENERATOR_NAME;
import static com.yeepay.g3.core.yop.doc.utils.YopConstant.MD_PLATFORM_DOC_PAGE_FILE_FORMAT;

/**
 * title: <br>
 * description: 描述<br>
 * Copyright: Copyright (c)2014<br>
 * Company: 易宝支付(YeePay)<br>
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2025/5/8
 */
@Component
public class PlatformDocMdGenerator extends AbstractDocGenerator {

    @Override
    public String name() {
        return MD_PLATFORM_DOC_LLMS_GENERATOR_NAME;
    }

    @Override
    public Object generate(Object... args) {
        DocDTO doc = (DocDTO) args[0];
        StringBuilder content = new StringBuilder();
        content.append("# ").append("平台简介").append("\n\n");
        Optional<DocPageDTO> profilePage = doc.getPages().stream().filter(p -> p.getPageType() == DocPageTypeEnum.WIKI && p.getPageNo().equals("platform-profile")).findAny();
        if (profilePage.isPresent()) {
            for (DocContentBlock block : profilePage.get().getBlocks()) {
                if (StringUtils.isNotBlank(block.getData())) {
                    String data = block.getData();
                    if (StringUtils.isNotBlank(block.getTitle())) {
                        content.append("## ").append(block.getTitle()).append("\n\n");
                        if (data.startsWith("#")) {
                            data = StringUtils.substringAfter(data, "\n");
                        }
                    }
                    content.append(data);
                }
            }
            content.append("\n");
        }

        content.append("# ").append("快速接入").append("\n\n");
        Optional<DocPageDTO> userGuide = doc.getPages().stream().filter(p -> p.getPageType() == DocPageTypeEnum.WIKI && p.getPageNo().equals("user-guide")).findAny();
        if (userGuide.isPresent()) {
            for (DocContentBlock block : userGuide.get().getBlocks()) {
                if (StringUtils.isNotBlank(block.getData())) {
                    String data = block.getData();
                    if (StringUtils.isNotBlank(block.getTitle())) {
                        content.append("## ").append(block.getTitle()).append("\n\n");
                        if (data.startsWith("#")) {
                            data = StringUtils.substringAfter(data, "\n");
                        }
                    }
                    content.append(data);
                }
            }
            content.append("\n");
        }
        content.append("# ").append("子页面索引").append("\n\n");
        Map<Long, DocPageDTO> pagesMap = new HashMap<>();
        String docNo = doc.getDocNo(), docTitle = doc.getTitle();
        List<DocPageDTO> pages = doc.getPages();
        processPages(docNo, docTitle, pages, pagesMap, content);
        return content.toString();
    }

    private void processPages(String docNo, String docTitle, List<DocPageDTO> pages, Map<Long, DocPageDTO> pagesMap, StringBuilder content) {
        String accessPrefix = ConfigUtils.getSysConfigParam(ConfigEnum.YOP_MD_DOC_ACCESS_PREFIX, String.class);
        for (DocPageDTO page : pages) {
            pagesMap.put(page.getId(), page);
            String pageContent = generatePageContent(docNo, page, 1);
            if (StringUtils.isNotBlank(pageContent)) {
                content.append("- ").append(docTitle).append(" > ")
                        .append(generateBreadcrumb(page, pagesMap))
                        .append(" [").append(page.getTitle()).append("](").append(accessPrefix).append("/platform/").append(page.getId()).append(".md)")
                        .append("\n");
            }
            if (CollectionUtils.isNotEmpty(page.getChildren())) {
                processPages(docNo, docTitle, page.getChildren(), pagesMap, content);
            }
        }
    }

    /**
     * 生成页面的面包屑导航
     * @param page 当前页面
     * @param pagesMap 页面ID到页面对象的映射
     * @return 面包屑导航字符串
     */
    private String generateBreadcrumb(DocPageDTO page, Map<Long, DocPageDTO> pagesMap) {
        StringBuilder breadcrumb = new StringBuilder();

        // 递归构建面包屑，从当前页面向上查找父页面
        buildBreadcrumbRecursive(page, pagesMap, breadcrumb);

        return breadcrumb.toString();
    }

    /**
     * 递归构建面包屑
     * @param page 当前页面
     * @param pagesMap 页面ID到页面对象的映射
     * @param breadcrumb 面包屑构建器
     */
    private void buildBreadcrumbRecursive(DocPageDTO page, Map<Long, DocPageDTO> pagesMap, StringBuilder breadcrumb) {
        // 获取父页面ID
        Long pid = page.getPid();

        // 如果是根页面，则不需要继续
        if (pid == null || DocUtils.YOP_DOC_ROOT_PAGE_ID.equals(pid)) {
            return;
        }

        // 获取父页面
        DocPageDTO parentPage = pagesMap.get(pid);
        if (parentPage == null) {
            return;
        }

        // 递归处理父页面的父页面
        buildBreadcrumbRecursive(parentPage, pagesMap, breadcrumb);

        // 添加父页面到面包屑
        if (breadcrumb.length() > 0) {
            breadcrumb.append(" > ");
        }
        breadcrumb.append(parentPage.getTitle());
    }

    private String generatePageContent(String docNo, DocPageDTO page, int headingLevel) {

        StringBuilder pageContent = new StringBuilder();

        // Add page content blocks
        if (CollectionUtils.isNotEmpty(page.getBlocks()) && page.getRefId() == null) {
            // Add page title as heading
            pageContent.append(StringUtils.repeat('#', headingLevel)).append(" ")
                    .append(page.getTitle()).append("\n\n");

            for (DocContentBlock block : page.getBlocks()) {
                if (StringUtils.isNotBlank(block.getData())) {
                    String data = block.getData();
                    if (StringUtils.isNotBlank(block.getTitle())) {
                        pageContent.append(StringUtils.repeat('#', headingLevel + 1)).append(" ")
                                .append(block.getTitle()).append("\n\n");
                        if (data.startsWith("#")) {
                            data = StringUtils.substringAfter(data, "\n");
                        }
                    }
                    pageContent.append(MdUrlConverterUtils.parseUrlsAndConvert(data, urlConverter)).append("\n\n");
                }
            }
        }

        if (StringUtils.isNotBlank(pageContent.toString())) {
            remoteStorage.update(String.format(MD_PLATFORM_DOC_PAGE_FILE_FORMAT, page.getId()), pageContent.toString());
        }

        return pageContent.toString();
    }
}
