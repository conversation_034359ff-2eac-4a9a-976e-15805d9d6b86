/*
 * Copyright: Copyright (c)2011
 * Company: 易宝支付(YeePay)
 */
package com.yeepay.g3.core.yop.doc.repository.mysql.doc;

import com.yeepay.g3.core.yop.doc.entity.v2.DocPageHistoryEntity;
import com.yeepay.g3.utils.persistence.mybatis.GenericYeepayRepository;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * title: <br>
 * description: 描述<br>
 * Copyright: Copyright (c)2014<br>
 * Company: 易宝支付(YeePay)<br>
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2019-10-28 12:49
 */
@Mapper
public interface DocPageHistoryRepository extends GenericYeepayRepository<Long, DocPageHistoryEntity> {

    DocPageHistoryEntity findOneWithContent(Long id);

    DocPageHistoryEntity findOneWithContentByDocVersion(@Param("docNo") String docNo, @Param("pageNo") String pageNo, @Param("docVersion") String docVersion);

    void batchDeleteByPageId(List<Long> list);

    List<DocPageHistoryEntity> findByDocNoAndDocVersion(@Param("docNo") String docNo, @Param("docVersion") String docVersion);

    List<DocPageHistoryEntity> findByDocNo(@Param("docNo") String docNo);

    /**
     * 根据id更新内容
     * @param id
     * @param content
     * @return
     */
    Integer updateContentById(@Param("id") Long id, @Param("content") String content);

    /**
     * 删除文档页面历史
     *
     * @param docNo 文档编码
     */
    void deleteByDocNo(String docNo);
}
