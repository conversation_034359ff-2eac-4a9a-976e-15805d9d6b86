/*
 * Copyright: Copyright (c)2011
 * Company: 易宝支付(YeePay)
 */
package com.yeepay.g3.core.yop.doc.repository.mysql.doc;

import com.yeepay.g3.core.yop.doc.entity.example.DocPageExample;
import com.yeepay.g3.core.yop.doc.entity.v2.DocPageEntity;
import com.yeepay.g3.utils.persistence.mybatis.GenericYeepayRepository;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * title: <br>
 * description: 描述<br>
 * Copyright: Copyright (c)2014<br>
 * Company: 易宝支付(YeePay)<br>
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2019-10-16 17:37
 */
@Mapper
public interface DocPageRepository extends GenericYeepayRepository<Long, DocPageEntity> {

    /**
     * 回滚时新增(恢复原id)
     * @param entity
     * @return
     */
    Integer saveWithId(DocPageEntity entity);

    /**
     * 条件查询，不关联text大字段
     * @param example
     * @return
     */
    List<DocPageEntity> findByExample(DocPageExample example);

    /**
     * 条件查询，关联text大字段（查询该版本文档已发布页面的内容history）
     *
     * @param docNo      文档编号
     * @param docVersion 文档版本
     * @return
     */
    List<DocPageEntity> findPublishedWithContent(@Param("docNo") String docNo, @Param("docVersion") String docVersion);

    /**
     * 查询尾部子页面
     * @param pid
     * @return
     */
    DocPageEntity findLastSonByPid(Long pid);

    /**
     * 更新页面位置（seq、pid、depth）
     * @param entity
     */
    void updateLocation(DocPageEntity entity);

    /**
     * 查找前一个页面
     * @param id
     * @return
     */
    DocPageEntity findPreSibling(Long id);

    /**
     * 查找后一个页面
     * @param id
     * @param id
     * @return
     */
    DocPageEntity findNextSibling(Long id);

    /**
     * 查找同级队尾的页面集合
     * @param pid
     * @param categoryId
     * @param seq
     * @return
     */
    List<DocPageEntity> findTailSiblings(@Param("pid") Long pid, @Param("categoryId") Long categoryId, @Param("seq") Integer seq);

    /**
     * 批量更新页面排序(seq)
     * @param entities
     */
    void batchUpdateSeqById(List<DocPageEntity> entities);

    /**
     * 批量删除直接子页面(目前apis、spis用到)
     * @param pid
     */
    void batchDeleteDirectSons(Long pid);

    /**
     * 更新页面可见性
     * @param father
     */
    void updateVisible(DocPageEntity father);

    /**
     * 批量更新page_visible、display
     * @param entities
     */
    void batchUpdateVisible(List<DocPageEntity> entities);

    /**
     * 批量移除引用关系
     * @param ids
     */
    void batchRemoveRefsById(List<Long> ids);

    void batchUpdatePageControl(@Param("ids") List<Long> ids, @Param("pageControl") Integer pageControl);

    void batchUpdatePageSeq(@Param("ids") List<Long> ids, @Param("seq") Integer seq);

    Long existsCategoryRef(@Param("categoryId") Long categoryId);

    void batchUpdateApiCategory(@Param("ids") List<Long> ids, @Param("categoryId") Long categoryId);

    /**
     * 批量删除文档页面
     *
     * @param docNo 文档编码
     */
    void deleteByDocNo(String docNo);

    /**
     * 根据多级pageNo路径定位页面
     * @param docNo 文档编号
     * @param grandPageNo 祖父级页面编号，可空
     * @param parentPageNo 父级页面编号，可空
     * @param currentPageNo 当前页面编号
     * @return DocPageEntity
     */
    DocPageEntity findByPageNoPath(@Param("docNo") String docNo,
                                   @Param("grandPageNo") String grandPageNo,
                                   @Param("parentPageNo") String parentPageNo,
                                   @Param("currentPageNo") String currentPageNo);
}
