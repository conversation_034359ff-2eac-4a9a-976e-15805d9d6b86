/*
 * Copyright: Copyright (c)2011
 * Company: 易宝支付(YeePay)
 */
package com.yeepay.g3.core.yop.doc.repository.mysql.doc;

import com.yeepay.g3.core.yop.doc.entity.PageOwnerEntity;
import com.yeepay.g3.utils.persistence.mybatis.GenericYeepayRepository;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * title: 页面负责人仓储接口<br>
 * description: 页面负责人数据访问接口<br>
 * Copyright: Copyright (c)2014<br>
 * Company: 易宝支付(YeePay)<br>
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2025-01-26
 */
@Mapper
public interface PageOwnerRepository extends GenericYeepayRepository<Long, PageOwnerEntity> {

    /**
     * 根据页面ID查询负责人列表
     * @param pageId 页面ID
     * @return 负责人列表
     */
    List<PageOwnerEntity> findByPageId(@Param("pageId") Long pageId);

    /**
     * 根据页面ID批量查询负责人列表
     * @param pageIds 页面ID列表
     * @return 负责人列表
     */
    List<PageOwnerEntity> findByPageIds(@Param("pageIds") List<Long> pageIds);

    /**
     * 批量插入页面负责人
     * @param pageOwners 页面负责人列表
     * @return 插入数量
     */
    Integer batchSave(@Param("pageOwners") List<PageOwnerEntity> pageOwners);

    /**
     * 根据页面ID删除负责人
     * @param pageId 页面ID
     * @return 删除数量
     */
    Integer deleteByPageId(@Param("pageId") Long pageId);

    /**
     * 根据页面ID批量删除负责人
     * @param pageIds 页面ID列表
     * @return 删除数量
     */
    Integer batchDeleteByPageIds(@Param("pageIds") List<Long> pageIds);

    /**
     * 删除页面负责人(乐观锁)
     * @param pageOwnerEntity 实体
     * @return 删除结果
     */
    Integer delete(PageOwnerEntity pageOwnerEntity);
}