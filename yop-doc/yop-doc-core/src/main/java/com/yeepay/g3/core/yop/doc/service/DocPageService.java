/*
 * Copyright: Copyright (c)2011
 * Company: 易宝支付(YeePay)
 */
package com.yeepay.g3.core.yop.doc.service;

import com.yeepay.g3.core.yop.doc.entity.example.DocPageExample;
import com.yeepay.g3.facade.yop.doc.dto.v2.DocDTO;
import com.yeepay.g3.facade.yop.doc.dto.v2.DocPageDTO;
import com.yeepay.g3.facade.yop.doc.dto.v2.DocPageHistoryDTO;
import com.yeepay.g3.facade.yop.doc.enums.v2.DocPageOperTypeEnum;

import java.util.List;
import java.util.Map;

/**
 * title: <br>
 * description: 描述<br>
 * Copyright: Copyright (c)2014<br>
 * Company: 易宝支付(YeePay)<br>
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2019-10-18 12:57
 */
public interface DocPageService {
    Long save(DocPageDTO page);

    void batchSave(List<DocPageDTO> pages);

    List<DocPageDTO> findByExample(DocPageExample example);

    DocPageDTO findById(Long id, boolean withContent);

    DocPageDTO findLastSonByPid(Long pagePid);

    List<DocPageDTO> findDirectSonPages(String docNo, Long pagePid);

    List<DocPageDTO> findAllSonPages(String docNo, Long pagePid, boolean withContent);

    void batchDeleteById(List<Long> ids);

    void batchDeleteDirectSons(Long pid);

    void update(DocPageDTO docPage, boolean updateContent);

    void updateLocation(DocPageDTO docPage);

    DocPageDTO findPreSibling(Long id);

    DocPageDTO findNextSibling(Long id);

    /**
     * 查询页面列表
     *
     * @param example        查询条件
     * @param groupingResult 是否对页面进行父子组装
     * @return
     */
    List<DocPageDTO> findPagesWithOutContent(DocPageExample example, boolean groupingResult);

    /**
     * 查询页面列表
     *
     * @param example        查询条件
     * @param groupingResult 是否对页面进行父子组装
     * @return
     */
    List<DocPageDTO> findPagesWithContent(DocPageExample example, boolean groupingResult);

    /**
     * 查找队尾的兄弟页面（包括本身）
     *
     * @param pid        父页面id
     * @param categoryId 页面分类
     * @param seq        本身排序值
     * @return
     */
    List<DocPageDTO> findTailSiblings(Long pid, Long categoryId, Integer seq);

    /**
     * 批量更新seq
     *
     * @param pages
     */
    void batchUpdateSeqById(List<DocPageDTO> pages);

    /**
     * 批量更新页面可见性
     * <p>
     * 页面/目录设置展示/不展示，则其子目录、子页面均跟随设置，对父级的影响如下
     * 1. 设置展示时，自动设置其直系父级，以及父级的父级为展示
     * 2. 设置不展示时，对父级无影响
     *
     * @param template 可见性模版
     * @return 受影响的页面id
     */
    List<Long> batchUpdateVisible(DocPageDTO template);

    /**
     * 批量清楚引用关系
     *
     * @param ids
     */
    void batchRemoveRefsById(List<Long> ids);

    /**
     * 获取api模板页面(两个描述模块)
     *
     * @return
     */
    DocPageDTO initApiPageFromTemplate();

    /**
     * 根据docNo获取wiki模板页面(产品文档)
     *
     * @return
     */
    Map<String, DocPageDTO> initWikiPageFromTemplate(String docNo);

    /**
     * 获取模板blockId(产品&Api)
     *
     * @return blockTitleKey:blockId
     */
    Map<String, String> templateBlockTitleAndId(String docNo);

    void batchUpdatePageControl(List<Long> ids, Integer pageControl);

    void batchUpdatePageSeq(List<Long> ids, Integer seq);

    /**
     * 文档分类引用
     *
     * @param categoryId 分类id
     * @return
     */
    Long existsCategoryRef(Long categoryId);

    /**
     * 获取文档页面树
     *
     * @param id
     * @return
     */
    DocDTO findDocTree(Long id);

    void batchUpdateApiCategory(List<Long> apiPageIds, Long categoryId);

    DocPageHistoryDTO toDocPageHistory(DocPageDTO docPage, DocPageOperTypeEnum operType, String cause);

    /**
     * 页面内容正则查询
     *
     * @param pattern     内容正则
     * @param targetGroup 目标内容的正则分组
     * @param docNo       指定文档，非必填
     */
    void findByPattern(String pattern, int targetGroup, String docNo);

    /**
     * 页面内容替换
     *
     * @param source
     * @param target
     * @param docNo
     */
    void replaceAll(String source, String target, String docNo);

    /**
     * 删除文档页面
     *
     * @param docNo
     */
    void deleteByDocNo(String docNo);

    /**
     * 批量复制页面
     *
     * @param pages 页面
     */
    void batchCopy(List<DocPageDTO> pages);

    /**
     * 根据多级pageNo路径定位页面
     * @param docNo 文档编号
     * @param grandPageNo 祖父级页面编号，可空
     * @param parentPageNo 父级页面编号，可空
     * @param currentPageNo 当前页面编号
     * @return DocPageDTO
     */
    DocPageDTO findByPageNoPath(String docNo, String grandPageNo, String parentPageNo, String currentPageNo);
}
