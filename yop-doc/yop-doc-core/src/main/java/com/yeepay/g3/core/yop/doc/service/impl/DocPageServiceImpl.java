package com.yeepay.g3.core.yop.doc.service.impl;

import cn.hutool.core.text.csv.CsvUtil;
import cn.hutool.core.text.csv.CsvWriter;
import cn.hutool.crypto.digest.DigestUtil;
import com.baomidou.dynamic.datasource.annotation.DSTransactional;
import com.fasterxml.jackson.databind.JavaType;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.yeepay.boot.components.utils.UUIDExt;
import com.yeepay.boot.components.utils.mapper.BeanMapper;
import com.yeepay.g3.core.yop.doc.entity.DocCategoryEntity;
import com.yeepay.g3.core.yop.doc.entity.DocProductRelateEntity;
import com.yeepay.g3.core.yop.doc.entity.example.DocPageExample;
import com.yeepay.g3.core.yop.doc.entity.v2.DocEntity;
import com.yeepay.g3.core.yop.doc.entity.v2.DocPageContentEntity;
import com.yeepay.g3.core.yop.doc.entity.v2.DocPageEntity;
import com.yeepay.g3.core.yop.doc.event.api.ApiExtendChangeEvent;
import com.yeepay.g3.core.yop.doc.event.api.ApiExtendChangeEventContent;
import com.yeepay.g3.core.yop.doc.repository.mysql.doc.*;
import com.yeepay.g3.core.yop.doc.service.DocMgrService;
import com.yeepay.g3.core.yop.doc.service.DocPageService;
import com.yeepay.g3.core.yop.doc.utils.RemoteStorage;
import com.yeepay.g3.core.yop.doc.utils.YopConstant;
import com.yeepay.g3.core.yop.doc.utils.mapper.JsonMapper;
import com.yeepay.g3.facade.yop.doc.dto.DocCategory;
import com.yeepay.g3.facade.yop.doc.dto.v2.DocContentBlock;
import com.yeepay.g3.facade.yop.doc.dto.v2.DocDTO;
import com.yeepay.g3.facade.yop.doc.dto.v2.DocPageDTO;
import com.yeepay.g3.facade.yop.doc.dto.v2.DocPageHistoryDTO;
import com.yeepay.g3.facade.yop.doc.enums.v2.DocPageOperTypeEnum;
import com.yeepay.g3.facade.yop.doc.enums.v2.DocPageTypeEnum;
import com.yeepay.g3.facade.yop.doc.util.DocCategoryUtils;
import com.yeepay.g3.facade.yop.doc.util.DocUtils;
import com.yeepay.g3.utils.common.BeanUtils;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.stereotype.Component;

import java.io.File;
import java.nio.charset.StandardCharsets;
import java.util.*;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

import static com.yeepay.g3.core.yop.doc.utils.YopConstant.SEPRATOR;

/**
 * title: <br>
 * description: 描述<br>
 * Copyright: Copyright (c)2014<br>
 * Company: 易宝支付(YeePay)<br>
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2019-10-18 12:58
 */
@Component
public class DocPageServiceImpl implements DocPageService {
    private static final Logger LOGGER = LoggerFactory.getLogger(DocPageServiceImpl.class);
    public static final JsonMapper JSON_MAPPER = JsonMapper.nonNullMapper();
    public static final JavaType DOC_PAGE_CONTENT_TYPE = JSON_MAPPER.contructCollectionType(List.class, DocContentBlock.class);

    @Autowired
    private DocProductRelateRepository docProductRelateRepository;

    @Autowired
    private DocPageRepository docPageRepository;

    @Autowired
    private DocPageContentRepository docPageContentRepository;

    @Autowired
    private DocPageHistoryRepository docPageHistoryRepository;

    @Autowired
    private RemoteStorage remoteStorage;

    @Autowired
    private DocMgrService docMgrService;

    @Autowired
    private DocCategoryRepository docCategoryRepository;

    @Autowired
    private DocRepository docRepository;

    @Autowired
    private ApplicationEventPublisher publisher;

    @Override
    @DSTransactional
    public Long save(DocPageDTO page) {
        final DocPageEntity entity = BeanMapper.convert(page, DocPageEntity.class);
        if (null == entity.getDisplay()) {
            entity.setDisplay(true);
        }
        if (null != entity.getId()) {
            docPageRepository.saveWithId(entity);
        } else {
            docPageRepository.save(entity);
        }
        if (CollectionUtils.isNotEmpty(page.getBlocks())) {
            fixBlockId(page);
            docPageContentRepository.save(toPageContent(entity.getId(), JSON_MAPPER.toJson(page.getBlocks())));
        }
        return entity.getId();
    }

    private DocPageContentEntity toPageContent(Long pageId, String content) {
        DocPageContentEntity pageContentEntity = new DocPageContentEntity();
        pageContentEntity.setPageId(pageId);
        pageContentEntity.setContent(content);
        return pageContentEntity;
    }

    @Override
    @DSTransactional
    public void batchSave(List<DocPageDTO> pages) {
        if (CollectionUtils.isEmpty(pages)) {
            return;
        }
        pages.forEach(page -> {
            final Long pid = save(page);
            if (CollectionUtils.isNotEmpty(page.getChildren())) {
                page.getChildren().forEach(son -> son.setPid(pid));
                batchSave(page.getChildren());
            }
        });
    }

    @Override
    public List<DocPageDTO> findByExample(DocPageExample example) {
        return convertEntities(docPageRepository.findByExample(example), example.isGroupResult());
    }

    private List<DocPageDTO> convertEntities(List<DocPageEntity> pageEntities, boolean groupingResult) {
        if (CollectionUtils.isNotEmpty(pageEntities)) {
            List<DocPageDTO> result = new ArrayList<>(pageEntities.size());
            //转换content->blocks
            for (DocPageEntity pageEntity : pageEntities) {
                result.add(convertEntity(pageEntity));
            }

            //转换为父子结构
            if (groupingResult) {
                Map<Long, List<DocPageDTO>> groupResult = result.stream().
                        collect(Collectors.groupingBy(DocPageDTO::getPid));
                final List<DocPageDTO> defaultValue = Collections.emptyList();
                Long rootId = result.get(0).getPid();
                for (DocPageDTO docPageDTO : result) {
                    List<DocPageDTO> sonPages = groupResult.getOrDefault(docPageDTO.getId(), defaultValue);
                    sonPages.sort(Comparator.comparingInt(DocPageDTO::getSeq));
                    docPageDTO.setChildren(sonPages);
                    if (rootId > docPageDTO.getPid()) {
                        rootId = docPageDTO.getPid();
                    }
                }
                return groupResult.get(rootId);
            } else {
                result.sort(Comparator.comparingInt(DocPageDTO::getSeq));
                return result;
            }
        }
        return Collections.emptyList();
    }

    private DocPageDTO convertEntity(DocPageEntity pageEntity) {
        if (null != pageEntity) {
            DocPageDTO pageDTO = BeanMapper.convert(pageEntity, DocPageDTO.class);
            pageDTO.setBlocks(toBlocks(pageEntity.getContent()));
            return pageDTO;
        }
        return null;
    }

    public static List<DocContentBlock> toBlocks(String content) {
        if (StringUtils.isNotBlank(content)) {
            return JSON_MAPPER.fromJson(content, DOC_PAGE_CONTENT_TYPE);
        }
        return Collections.emptyList();
    }

    @Override
    public DocPageDTO findById(Long id, boolean withContent) {
        final DocPageExample example = new DocPageExample();
        example.setId(id);
        example.withContent(withContent);

        List<DocPageEntity> pageEntities = docPageRepository.findByExample(example);
        if (CollectionUtils.isNotEmpty(pageEntities)) {
            return convertEntity(pageEntities.get(0));
        }
        return null;
    }

    @Override
    public DocPageDTO findLastSonByPid(Long pagePid) {
        DocPageEntity pageEntity = docPageRepository.findLastSonByPid(pagePid);
        return convertEntity(pageEntity);
    }

    @Override
    public List<DocPageDTO> findDirectSonPages(String docNo, Long pid) {
        final DocPageExample example = new DocPageExample().setDocNo(docNo).setPid(pid);
        return convertEntities(docPageRepository.findByExample(example), false);
    }

    @Override
    public List<DocPageDTO> findAllSonPages(String docNo, Long pagePid, boolean withContent) {
        final DocPageExample parentPageExample = new DocPageExample().setDocNo(docNo);
        parentPageExample.setId(pagePid);
        final List<DocPageEntity> foundedParent = docPageRepository.findByExample(parentPageExample);
        if (CollectionUtils.isNotEmpty(foundedParent)) {
            final List<DocPageEntity> docPages = docPageRepository.findByExample(new DocPageExample().setDocNo(docNo).withContent(withContent));
            final Map<Long, List<DocPageEntity>> groupResult = docPages.stream().collect(Collectors.groupingBy(DocPageEntity::getPid));
            final List<DocPageEntity> defaultValue = Collections.emptyList();
            docPages.forEach(e -> e.setChildren(groupResult.getOrDefault(e.getId(), defaultValue)));
            final List<DocPageEntity> pageEntities = groupResult.get(pagePid);
            if (CollectionUtils.isNotEmpty(pageEntities)) {
                List<DocPageEntity> result = new ArrayList<>();
                final List<DocPageEntity> unionAll = unionAllPages(pageEntities, result);
                return convertEntities(unionAll, false);
            }
        }
        return Collections.emptyList();
    }

    private List<DocPageEntity> unionAllPages(List<DocPageEntity> pageEntities, List<DocPageEntity> result) {
        if (CollectionUtils.isNotEmpty(pageEntities)) {
            pageEntities.forEach(pageEntity -> {
                result.add(pageEntity);
                if (CollectionUtils.isNotEmpty(pageEntity.getChildren())) {
                    result.addAll(unionAllPages(pageEntity.getChildren(), result));
                }
            });
        }
        return result;
    }


    @Override
    public void batchDeleteById(List<Long> pageIds) {
        if (CollectionUtils.isNotEmpty(pageIds)) {
            docPageRepository.batchDeleteById(pageIds);
            docPageContentRepository.batchDeleteByPageId(pageIds);
            docPageHistoryRepository.batchDeleteByPageId(pageIds);
        }
    }

    @Override
    public void batchDeleteDirectSons(Long pid) {
        docPageRepository.batchDeleteDirectSons(pid);
    }

    @Override
    public void update(DocPageDTO docPage, boolean updateContent) {
        final DocPageEntity pageEntity = BeanMapper.convert(docPage, DocPageEntity.class);
        if (null == pageEntity.getDisplay()) {
            pageEntity.setDisplay(true);
        }
        docPageRepository.update(pageEntity);
        if (updateContent) {
            DocPageContentEntity contentEntity = docPageContentRepository.checkByPageId(docPage.getId());
            if (null == contentEntity) {
                if (CollectionUtils.isNotEmpty(docPage.getBlocks())) {
                    docPageContentRepository.save(toPageContent(docPage.getId(), JSON_MAPPER.toJson(docPage.getBlocks())));
                }
            } else {
                contentEntity.setContent(JSON_MAPPER.toJson(docPage.getBlocks()));
                docPageContentRepository.update(contentEntity);
            }
        }
    }

    @Override
    public void updateLocation(DocPageDTO docPage) {
        docPageRepository.updateLocation(BeanMapper.convert(docPage, DocPageEntity.class));
    }

    @Override
    public DocPageDTO findPreSibling(Long id) {
        return convertEntity(docPageRepository.findPreSibling(id));
    }

    @Override
    public DocPageDTO findNextSibling(Long id) {
        return convertEntity(docPageRepository.findNextSibling(id));
    }

    @Override
    public List<DocPageDTO> findPagesWithOutContent(DocPageExample example, boolean groupingResult) {
        final List<DocPageEntity> pageEntities = docPageRepository.findByExample(example);
        if (CollectionUtils.isNotEmpty(pageEntities)) {
            return convertEntities(pageEntities, groupingResult);
        }
        return Collections.emptyList();
    }

    @Override
    public List<DocPageDTO> findPagesWithContent(DocPageExample example, boolean groupingResult) {
        final List<DocPageEntity> pageEntities = docPageRepository.findByExample(example.withContent(true));
        if (CollectionUtils.isNotEmpty(pageEntities)) {
            return convertEntities(pageEntities, groupingResult);
        }
        return Collections.emptyList();
    }

    @Override
    public List<DocPageDTO> findTailSiblings(Long pid, Long categoryId, Integer seq) {
        final List<DocPageEntity> pageEntities = docPageRepository.findTailSiblings(pid, categoryId, seq);
        if (CollectionUtils.isNotEmpty(pageEntities)) {
            return convertEntities(pageEntities, false);
        }
        return Collections.emptyList();
    }

    @Override
    public void batchUpdateSeqById(List<DocPageDTO> pages) {
        if (CollectionUtils.isNotEmpty(pages)) {
            docPageRepository.batchUpdateSeqById(pages.stream().map(p ->
                    BeanMapper.convert(p, DocPageEntity.class)).collect(Collectors.toList()));
        }
    }

    @Override
    public List<Long> batchUpdateVisible(DocPageDTO template) {
        List<DocPageDTO> pagesToUpdate = Lists.newArrayList();

        // 子孙页面
        final List<DocPageDTO> sonPagesToBeUpdate = findAllSonPages(template.getDocNo(), template.getId(), false);
        if (CollectionUtils.isNotEmpty(sonPagesToBeUpdate)) {
            sonPagesToBeUpdate.forEach(e -> {
                e.setPageVisible(template.getPageVisible());
                e.setDisplay(template.getDisplay());
            });
            pagesToUpdate.addAll(sonPagesToBeUpdate);
        }

        // 父页面
        if (template.getDisplay()) {
            List<DocPageDTO> parentPagesToUpdate = findAllParents(template.getDocNo(), template.getId(), false);
            pagesToUpdate.addAll(parentPagesToUpdate);
        }

        // 自身
        pagesToUpdate.add(template);

        docPageRepository.batchUpdateVisible(pagesToUpdate.stream().map(p ->
                BeanMapper.convert(p, DocPageEntity.class)).collect(Collectors.toList()));
        return pagesToUpdate.stream().map(DocPageDTO::getId).collect(Collectors.toList());
    }

    private List<DocPageDTO> findAllParents(String docNo, Long pageId, boolean withContent) {
        final List<DocPageEntity> allPages = docPageRepository.findByExample(new DocPageExample().setDocNo(docNo).withContent(withContent));
        if (CollectionUtils.isNotEmpty(allPages)) {
            Map<Long, DocPageEntity> pagesMap = Maps.newHashMapWithExpectedSize(allPages.size());
            for (DocPageEntity page : allPages) {
                pagesMap.put(page.getId(), page);
            }
            List<DocPageDTO> result = Lists.newArrayList();
            DocPageEntity docPageEntity = pagesMap.get(pageId);
            do {
                docPageEntity = pagesMap.get(docPageEntity.getPid());
                if (null != docPageEntity) {
                    result.add(convertEntity(docPageEntity));
                }
            } while (null != docPageEntity);
            return result;
        }
        return Collections.emptyList();
    }



    @Override
    public void batchRemoveRefsById(List<Long> ids) {
        if (CollectionUtils.isNotEmpty(ids)) {
            docPageRepository.batchRemoveRefsById(ids);
        }
    }

    @Override
    public DocPageDTO initApiPageFromTemplate() {
        final String templateJson = remoteStorage.downloadAsString(getDocApiPageTemplateFile());
        return JSON_MAPPER.fromJson(templateJson, DocPageDTO.class);
    }

    private String getDocApiPageTemplateFile() {
        return YopConstant.DOC_TEMPLATE_BASE_PATH + SEPRATOR + YopConstant.PRODUCT_DOC_TEMPLATE_BASE_PATH + SEPRATOR + YopConstant.DOC_API_PAGE_TEMPLATE_FILE;
    }

    @Override
    public Map<String, DocPageDTO> initWikiPageFromTemplate(String docNo) {
        // 根据docNo，查产品编码
        final List<DocProductRelateEntity> docProductRelates = docProductRelateRepository.findByDocNo(docNo);
        List<String> productCodes = docProductRelates.stream().map(DocProductRelateEntity::getProductCode).collect(Collectors.toList());

        Map<String, DocPageDTO> wikiPageTemplate;
        final DocDTO doc = docMgrService.initDocFromTemplate(productCodes);
        if (CollectionUtils.isNotEmpty(doc.getPages())) {
            wikiPageTemplate = Maps.newHashMapWithExpectedSize(doc.getPages().size());
            doc.getPages().forEach(page -> wikiPageTemplate.put(page.getPageNo(), page));
        } else {
            wikiPageTemplate = Collections.emptyMap();
        }
        return wikiPageTemplate;
    }

    @Override
    public Map<String, String> templateBlockTitleAndId(String docNo) {
        // 根据docNo，查产品编码
        final List<DocProductRelateEntity> docProductRelates = docProductRelateRepository.findByDocNo(docNo);
        List<String> productCodes = docProductRelates.stream().map(DocProductRelateEntity::getProductCode).collect(Collectors.toList());

        DocDTO docTemplate = docMgrService.initDocFromTemplate(productCodes);
        DocPageDTO apiPageTemplate = initApiPageFromTemplate();
        Map<String, String> titleBlockIdMap = Maps.newLinkedHashMap();
        Map<String, DocPageDTO> wikiPageTemplates;
        if (CollectionUtils.isNotEmpty(docTemplate.getPages())) {
            wikiPageTemplates = Maps.newHashMapWithExpectedSize(docTemplate.getPages().size());
            for (DocPageDTO page : docTemplate.getPages()) {
                wikiPageTemplates.put(page.getPageNo(), page);
            }
        } else {
            wikiPageTemplates = Collections.emptyMap();
        }
        wikiPageTemplates.values().forEach(page -> {
            if (CollectionUtils.isNotEmpty(page.getBlocks())) {
                page.getBlocks().forEach(block -> titleBlockIdMap.put(DocUtils.blockTitleKey(DocPageTypeEnum.WIKI, page.getPageNo(), block.getTitle()), block.getId()));
            }
        });
        if (CollectionUtils.isNotEmpty(apiPageTemplate.getBlocks())) {
            apiPageTemplate.getBlocks().forEach(block -> titleBlockIdMap.put(DocUtils.blockTitleKey(DocPageTypeEnum.API, apiPageTemplate.getPageNo(), block.getTitle()), block.getId()));
        }
        return titleBlockIdMap;
    }

    @Override
    public void batchUpdatePageControl(List<Long> ids, Integer pageControl) {
        docPageRepository.batchUpdatePageControl(ids, pageControl);
    }

    @Override
    public void batchUpdatePageSeq(List<Long> ids, Integer seq) {
        docPageRepository.batchUpdatePageSeq(ids, seq);
    }

    @Override
    public Long existsCategoryRef(Long categoryId) {
        return docPageRepository.existsCategoryRef(categoryId);
    }

    @Override
    public DocDTO findDocTree(Long id) {
        DocDTO doc = docMgrService.findOne(id);
        List<DocPageDTO> pages = findByExample(new DocPageExample().setDocNo(doc.getDocNo()).withCategory(true).setGroupResult(true));
        if (DocUtils.isTemplateDoc(doc.getType())) {
            fillApiCategoryPages(pages);
        }
        doc.setPages(pages);
        return doc;
    }

    @Override
    public void batchUpdateApiCategory(List<Long> apiPageIds, Long categoryId) {
        docPageRepository.batchUpdateApiCategory(apiPageIds, categoryId);
    }

    @Override
    public DocPageHistoryDTO toDocPageHistory(DocPageDTO docPage, DocPageOperTypeEnum operType, String cause) {
        final DocPageHistoryDTO pageHistory = new DocPageHistoryDTO();
        BeanUtils.copyProperties(docPage, pageHistory);
        pageHistory.setOperator(cause);
        pageHistory.setOperType(operType);
        pageHistory.setPageId(docPage.getId());
        pageHistory.setPagePid(docPage.getPid());
        return pageHistory;
    }

    @Override
    public void findByPattern(String pattern, int targetGroup, String docNo) {
        if (StringUtils.isBlank(pattern)) {
            return;
        }
        final Pattern matchedPattern = Pattern.compile(pattern);
        List<String> docs;
        if (StringUtils.isBlank(docNo)) {
            docs = docRepository.findAll().stream().map(DocEntity::getDocNo).collect(Collectors.toList());
        } else {
            docs = Collections.singletonList(docNo);
        }
        final String csvFileLocal = YopConstant.CEPH_TEMP_WORK_DIR + SEPRATOR + UUID.randomUUID() + ".csv";
        // docNo, pageNo, pageTitle, matchedStr
        try (CsvWriter writer = CsvUtil.getWriter(csvFileLocal, StandardCharsets.UTF_8)) {
            writer.write(new String[]{"文档编码", "页面编码", "页面标题", "匹配片段"});
            for (String doc : docs) {
                List<DocPageEntity> docPages = docPageRepository.findByExample(new DocPageExample().setDocNo(doc).withContent(true));
                if (CollectionUtils.isNotEmpty(docPages)) {
                    for (DocPageEntity docPage : docPages) {
                        writer.write(new String[]{docPage.getDocNo(), docPage.getPageNo(), docPage.getTitle(),
                                getMatchedStr(docPage.getContent(), matchedPattern, targetGroup)});
                    }
                }
            }

            final String remoteFileName = "docs/tmp/" + DigestUtil.md5Hex(pattern) + ".csv";
            remoteStorage.uploadSingleFile(new File(csvFileLocal), remoteFileName);
            LOGGER.info("finish to findByPattern, pattern:{}, remote file:{}", pattern, remoteFileName);
        } catch (Exception e) {
            LOGGER.error("error to findByPattern, ex:", e);
        }
    }

    private String getMatchedStr(String content, Pattern pattern, int targetGroup) {
        Set<String> result = new LinkedHashSet<>();
        final Matcher matcher = pattern.matcher(content);
        while (matcher.find()) {
            final String matchedEle = matcher.group(Math.max(targetGroup, 0));
            result.add(matchedEle.trim());
        }
        return StringUtils.join(result, '\n');
    }

    @Override
    public void replaceAll(String source, String target, String docNo) {
        docPageContentRepository.replaceAll(source, target, docNo);
    }

    @Override
    @DSTransactional
    public void deleteByDocNo(String docNo) {
        docPageHistoryRepository.deleteByDocNo(docNo);
        docPageContentRepository.deleteByDocNo(docNo);
        docPageRepository.deleteByDocNo(docNo);
    }

    @Override
    public void batchCopy(List<DocPageDTO> pages) {
        if (CollectionUtils.isEmpty(pages)) {
            return;
        }
        Map<Long, Long> categoryMapping = Maps.newHashMapWithExpectedSize(pages.size());
        pages.forEach(page -> {
            final Long pid = copy(page, categoryMapping);
            if (CollectionUtils.isNotEmpty(page.getChildren())) {
                page.getChildren().forEach(son -> son.setPid(pid));
                batchCopy(page.getChildren());
            }
        });
    }

    public Long copy(DocPageDTO page, Map<Long, Long> categoryMapping) {
        final DocPageEntity entity = BeanMapper.convert(page, DocPageEntity.class);
        entity.setId(null);
        if (null == entity.getDisplay()) {
            entity.setDisplay(true);
        }
        if (null != entity.getCategoryId()) {
            entity.setCategoryId(resolveCategoryId(entity, categoryMapping));
        }
        docPageRepository.save(entity);

        if (CollectionUtils.isNotEmpty(page.getBlocks())) {
            fixBlockId(page);
            docPageContentRepository.save(toPageContent(entity.getId(), JSON_MAPPER.toJson(page.getBlocks())));
        }
        if (DocPageTypeEnum.API.equals(page.getPageType())) {
            publisher.publishEvent(new ApiExtendChangeEvent(new ApiExtendChangeEventContent().setId(entity.getId())
                    .setDocNo(entity.getDocNo()).setPageNo(entity.getPageNo()).setBlocks(page.getBlocks())));
        }
        return entity.getId();
    }

    private Long resolveCategoryId(DocPageEntity entity, Map<Long, Long> categoryMapping) {
        if (null == entity.getCategoryId()) {
            return null;
        }
        final Long alreadySaved = categoryMapping.get(entity.getCategoryId());
        if (null == alreadySaved) {
            final DocCategoryEntity foundCategory = docCategoryRepository.findOne(entity.getCategoryId());
            final DocCategoryEntity targetCategory = BeanMapper.convert(foundCategory, DocCategoryEntity.class);
            targetCategory.setId(null);
            targetCategory.setScope(String.format(DocCategoryUtils.DOC_API_SCOPE_FORMAT, entity.getDocNo()));
            targetCategory.setVersion(0L);
            docCategoryRepository.save(targetCategory);
            categoryMapping.put(entity.getCategoryId(), targetCategory.getId());
        }
        return categoryMapping.get(entity.getCategoryId());
    }

    private void fixBlockId(DocPageDTO page) {
        if (CollectionUtils.isEmpty(page.getBlocks())) {
            return;
        }
        page.getBlocks().forEach(block -> {
            if (StringUtils.isBlank(block.getId())) {
                block.setId(UUIDExt.compressV4UUID());
            }
        });
    }

    private void fillApiCategoryPages(List<DocPageDTO> pages) {
        Map<Long, DocPageDTO> categoryMap = new LinkedHashMap<>();
        Optional<DocPageDTO> apiParent = pages.stream().filter(p ->
                p.getPid().equals(DocUtils.YOP_DOC_ROOT_PAGE_ID) && p.getPageNo().equals(DocUtils.YOP_DOC_PRODUCT_API_PARENT_PAGENO)).findAny();
        if (apiParent.isPresent()) {
            DocPageDTO apiParentPage = apiParent.get();
            List<DocPageDTO> apis = apiParentPage.getChildren();
            List<DocCategoryEntity> categories = docCategoryRepository.findByScope(String.format(DocCategoryUtils.DOC_API_SCOPE_FORMAT, apiParentPage.getDocNo()));
            if (CollectionUtils.isNotEmpty(apis)) {
                DocCategory defaultApiCategory = BeanMapper.convert(categories.stream().filter(docCategory -> docCategory.getName().equals(DocCategoryUtils.DEFAULT_CATEGORY_NAME)).findAny().get(), DocCategory.class);
                for (DocPageDTO api : apis) {
                    DocCategory category = api.getCategory();
                    if (null == category) {
                        LOGGER.warn("api without category, please add one, apiTitle:{}", api.getTitle());
                        category = defaultApiCategory;
                    }
                    DocPageDTO docCategoryPage = categoryMap.get(category.getId());
                    if (null == docCategoryPage) {
                        docCategoryPage = DocUtils.getDocApiCategoryPage(api.getDocNo(), category.getId(), category.getPid(), category.getName(), category.getDesc(), category.getSeq());
                        categoryMap.put(category.getId(), docCategoryPage);
                    }
                    docCategoryPage.getChildren().add(api);
                }
                for (DocCategoryEntity category : categories) {
                    if (!categoryMap.containsKey(category.getId())) {
                        categoryMap.put(category.getId(), DocUtils.getDocApiCategoryPage(apiParentPage.getDocNo(), category.getId(), category.getPid(), category.getName(), category.getDesc(), category.getSeq()));
                    }
                }
                apiParentPage.setChildren(sortCategoryPages(categoryMap.values()));
            } else if (CollectionUtils.isNotEmpty(categories)) {
                apiParentPage.setChildren(sortCategoryPages(categories.stream().map(category ->
                        DocUtils.getDocApiCategoryPage(apiParentPage.getDocNo(), category.getId(), category.getPid(), category.getName(), category.getDesc(), category.getSeq()))
                        .collect(Collectors.toList())));
            }
        }
    }

    private List<DocPageDTO> sortCategoryPages(Collection<DocPageDTO> apiCategoryPages) {
        return apiCategoryPages.stream().sorted(Comparator.comparingInt(DocPageDTO::getSeq)).collect(Collectors.toList());
    }

    @Override
    public DocPageDTO findByPageNoPath(String docNo, String grandPageNo, String parentPageNo, String currentPageNo) {
        DocPageEntity entity = docPageRepository.findByPageNoPath(docNo, grandPageNo, parentPageNo, currentPageNo);
        return convertEntity(entity);
    }
}
