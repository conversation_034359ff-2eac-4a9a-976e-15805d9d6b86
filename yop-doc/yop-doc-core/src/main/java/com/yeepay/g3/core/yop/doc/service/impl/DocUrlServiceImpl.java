package com.yeepay.g3.core.yop.doc.service.impl;

import com.yeepay.boot.components.config.utils.ConfigUtils;
import com.yeepay.g3.core.yop.doc.service.DocPageService;
import com.yeepay.g3.core.yop.doc.service.DocUrlService;
import com.yeepay.g3.core.yop.doc.utils.config.ConfigEnum;
import com.yeepay.g3.facade.yop.doc.dto.v2.DocPageDTO;
import com.yeepay.g3.facade.yop.doc.enums.v2.DocPageTypeEnum;
import com.yeepay.g3.facade.yop.doc.util.DocUtils;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.concurrent.TimeUnit;
import java.util.regex.Pattern;

@Service
public class DocUrlServiceImpl implements DocUrlService {
    private static final Logger logger = LoggerFactory.getLogger(DocUrlServiceImpl.class);
    private static final String REDIS_KEY_PREFIX = "yop:doc:url:md:";
    private static final long CACHE_TTL = 30L; // 1天
    private static final Pattern PRODUCT_API_URL_PATTERN = Pattern.compile("^/docs/products/(.+)/apis/(.+)$");

    private DocPageService docPageService;
    private RedisTemplate<String, Object> redisTemplate;

    @Autowired
    public void setDocPageService(DocPageService docPageService) {
        this.docPageService = docPageService;
    }

    @Autowired
    public void setRedisTemplate(RedisTemplate<String, Object> redisTemplate) {
        this.redisTemplate = redisTemplate;
    }

    @Override
    public String convMdUrl(String url) {
        if (StringUtils.isBlank(url)) {
            return url;
        }
        String cacheKey = REDIS_KEY_PREFIX + url;
        try {
            Object cached = redisTemplate.opsForValue().get(cacheKey);
            if (cached != null) {
                return cached.toString();
            }
        } catch (Exception e) {
            logger.warn("redis get error，key: {}", cacheKey, e);
        }
        String result = doConvMdUrl(url);
        if (!url.equals(result)) {
            try {
                redisTemplate.opsForValue().set(cacheKey, result, CACHE_TTL, TimeUnit.MINUTES);
            } catch (Exception e) {
                logger.warn("redis set error，key: {}", cacheKey, e);
            }
        }
        return result;
    }

    private String doConvMdUrl(String url) {
        String docCenterAccessHost = ConfigUtils.getSysConfigParam(ConfigEnum.YOP_DOC_CENTER_ACCESS_HOST, String.class);
        String urlEndpoint = "";
        String relativeUrl = url;
        // 去除http端点
        if (relativeUrl.startsWith("http")) {
            int idx = relativeUrl.indexOf(docCenterAccessHost);
            if (idx > 0) {
                urlEndpoint = relativeUrl.substring(0, idx + docCenterAccessHost.length());
                relativeUrl = relativeUrl.substring(idx + docCenterAccessHost.length());
            } else {
                // 不是open.yeepay.com域名，��接返回原url
                return url;
            }
        }
        // 处理/docs-v3/(.*)
        if (relativeUrl.matches("^/docs-v3/.*")) {
            return url;
        }
        // 处理/attachments/(.*)
        if (relativeUrl.matches("^/attachments/.*")) {
            return url;
        }
        // 处理/docs/v2/(.*) -> /docs/(.*)
        if (relativeUrl.matches("^/docs/v2/.*")) {
            relativeUrl = relativeUrl.replaceFirst("^/docs/v2/", "/docs/");
        }
        // 处理/docs/solutions/(.*)
        if (relativeUrl.matches("^/docs/solutions/.*")) {
            logger.warn("solutions page has ref, {}", url);
            return url;
        }
        // 仅处理平台文档、产品文档、API文档
        if (!StringUtils.startsWithAny(relativeUrl, "/docs/open/platform-doc", "/docs/platform", "/docs/products", "/docs/apis", "/docs-v2/apis")) {
            return url;
        }
        // 去掉尾部/index.html
        if (relativeUrl.endsWith("/index.html")) {
            relativeUrl = relativeUrl.substring(0, relativeUrl.length() - "/index.html".length());
        }
        // 去掉后缀.html
        if (relativeUrl.endsWith(".html")) {
            relativeUrl = relativeUrl.substring(0, relativeUrl.length() - ".html".length());
        }

        // 提取docNo,docType
        String docNo = null;
        DocPageTypeEnum pageType = null;
        String pagePath = null;
        if (relativeUrl.startsWith("/docs/open/platform-doc")) {
            docNo = "platform-doc";
            pageType = DocPageTypeEnum.WIKI;
            pagePath = relativeUrl.substring("/docs/open/platform-doc".length());
        } else if (relativeUrl.startsWith("/docs/platform")) {
            docNo = "platform";
            pageType = DocPageTypeEnum.WIKI;
            pagePath = relativeUrl.substring("/docs/platform".length());
        } else if (relativeUrl.startsWith("/docs/products") && !PRODUCT_API_URL_PATTERN.matcher(relativeUrl).matches()) {
            docNo = relativeUrl.split("/")[3]; // 提取第三个部分作为docNo
            pageType = DocPageTypeEnum.WIKI;
            pagePath = relativeUrl.substring(("/docs/products/" + docNo).length());
        } else {
            pageType = DocPageTypeEnum.API;
            pagePath = relativeUrl.substring(relativeUrl.lastIndexOf("/") + 1);
        }

        if (DocPageTypeEnum.WIKI.equals(pageType)) {
            DocPageDTO page = null;
            // 判断是否为首页
            if (StringUtils.isBlank(pagePath)) {
                List<DocPageDTO> allSonPages = docPageService.findAllSonPages(docNo, DocUtils.YOP_DOC_ROOT_PAGE_ID, false);
                if (CollectionUtils.isNotEmpty(allSonPages)) {
                    page = allSonPages.get(0);
                }
            } else {
                // 根据pagePath解析三级pageNo
                String[] pageNos = DocUtils.toPageNos(pagePath);
                String grandPageNo = pageNos[0];
                String parentPageNo = pageNos[1];
                String currentPageNo = pageNos[2];
                page = docPageService.findByPageNoPath(docNo, grandPageNo, parentPageNo, currentPageNo);
            }
            if (page != null) {
                while (page.getRefId() != null) {
                    page = docPageService.findById(page.getRefId(), false);
                }
                if (DocUtils.isPlatformDoc(page.getDocNo())) {
                    return urlEndpoint + "/docs-v3/platform/" + page.getId() + ".md";
                } else {
                    return urlEndpoint + "/docs-v3/product/" + docNo + "/" + page.getId() + ".md";
                }
            } else {
                logger.warn("page not found, {}", url);
                return url;
            }
        }
        // 处理API文档
        for (String path : pagePath.split("/")) {
            if (StringUtils.startsWithAny(path, "options__", "get__", "post__")) {
                return urlEndpoint + "/docs-v3/api/" + path.replace("__", "_") + ".md";
            }
        }

        // 其他情况，暂不处理
        logger.info("other page, {}", url);
        return url;
    }
}
