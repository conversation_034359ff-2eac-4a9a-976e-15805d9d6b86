package com.yeepay.g3.core.yop.doc.utils;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.yeepay.g3.core.yop.doc.dto.api.ApiDefinition;
import com.yeepay.g3.core.yop.doc.utils.converter.MdUrlConverterUtils;
import com.yeepay.g3.core.yop.doc.utils.converter.UrlConverter;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.IOException;
import java.io.UnsupportedEncodingException;
import java.net.URLEncoder;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.util.*;
import java.util.concurrent.atomic.AtomicBoolean;

import static com.yeepay.g3.core.yop.doc.utils.YopConstant.API_DOC_ATTENTION_URL_ENCODE;

public class ApiMdUtils {
    private static final Logger log = LoggerFactory.getLogger(ApiMdUtils.class);
    private static final UrlConverter DEFAULT_URL_CONVERTER = new UrlConverter.DefaultUrlConverter();

    private static final ObjectMapper objectMapper;
    static {
        objectMapper = new ObjectMapper();
        // 配置日期序列化格式
        objectMapper.setDateFormat(new java.text.SimpleDateFormat("yyyy-MM-dd HH:mm:ss"));
    }
    
    private final String jsonFilePath;
    private final String outputDir;
    private JsonNode apiData;
    private final StringBuilder content;
    private final Set<String> processedRefs;
    private final Map<String, String> modelMap = new HashMap<>();
    private final List<Map<String, String>> errorCodes = new ArrayList<>();
    private final List<Map<String, String>> callbacks = new ArrayList<>();
    private final Map<String, Object> defaultReqExampleMap = new HashMap<>();
    private final Map<String, Object> defaultRespExampleMap = new HashMap<>();
    private final UrlConverter urlConverter;

    public ApiMdUtils(String jsonFilePath, String outputDir) {
        this.jsonFilePath = jsonFilePath;
        this.outputDir = outputDir != null ? outputDir : Paths.get(jsonFilePath).getParent().toString();
        this.content = new StringBuilder();
        this.processedRefs = new HashSet<>();
        this.urlConverter = DEFAULT_URL_CONVERTER;
    }

    public ApiMdUtils(String jsonFilePath, String outputDir, Map<String, String> modelMap) {
        this.jsonFilePath = jsonFilePath;
        this.outputDir = outputDir != null ? outputDir : Paths.get(jsonFilePath).getParent().toString();
        this.content = new StringBuilder();
        this.processedRefs = new HashSet<>();
        if (null != modelMap && !modelMap.isEmpty()) {
            this.modelMap.putAll(modelMap);
        }
        this.urlConverter = DEFAULT_URL_CONVERTER;
    }

    public ApiMdUtils(ApiDefinition apiDefinition, Map<String, String> modelMap
            , List<Map<String, String>> errorCodes, List<Map<String, String>> callbacks, UrlConverter urlConverter) {
        this.jsonFilePath = null;
        this.outputDir = null;
        this.content = new StringBuilder();
        this.processedRefs = new HashSet<>();
        if (null != modelMap && !modelMap.isEmpty()) {
            this.modelMap.putAll(modelMap);
        }
        if (null != errorCodes && !errorCodes.isEmpty()) {
            this.errorCodes.addAll(errorCodes);
        }
        if (null != callbacks && !callbacks.isEmpty()) {
            this.callbacks.addAll(callbacks);
        }
        try {
            this.apiData = objectMapper.readTree(objectMapper.writeValueAsString(apiDefinition));
        } catch (JsonProcessingException e) {
            throw new RuntimeException(e);
        }
        this.urlConverter = null == urlConverter ? DEFAULT_URL_CONVERTER : urlConverter;
    }

    private void loadApiData() throws IOException {
        String jsonContent = new String(Files.readAllBytes(Paths.get(jsonFilePath)));
        this.apiData = objectMapper.readTree(jsonContent);
    }

    public String convertToMarkdown() {
        if (apiData == null) {
            try {
                loadApiData();
            } catch (IOException e) {
                throw new RuntimeException("Failed to load JSON file: " + e.getMessage(), e);
            }
        }

        content.setLength(0);
        processedRefs.clear();

        // Add title
        content.append("# ").append(getOrDefault(apiData, "title", getOrDefault(apiData, "path", "Unknown API"))).append("\n\n");

        // Add sections
        addBasicInfo();
        addRequestParameters();
        addRequestExamples();
        addResponseParameters();
        addResponseExamples();
        addErrCodes();
        addCallbacks();
        addSampleCodes();

        return content.toString();
    }

    private void addCallbacks() {
        if (callbacks.isEmpty()) {
            return;
        }
        content.append("## 结果通知\n\n");
        content.append("| 通知编码 | 通知名称 | 通知描述 | 通知参数参考 |\n");
        content.append("| ------ | ------ | ------ | ------ |\n");
        callbacks.forEach(callback ->
                content.append("| ")
                        .append(callback.get("name")).append(" | ")
                        .append(formatDescription(callback.get("title"))).append(" | ")
                        .append(MdUrlConverterUtils.parseUrlsAndConvert(formatDescription(callback.get("description")), urlConverter)).append(" | ")
                        .append(formatDescription(callback.get("referenceUrl"))).append(" |\n"));
        content.append("\n");
    }

    private void addErrCodes() {
        content.append("## 错误码\n\n");
        if (errorCodes.isEmpty()) {
            content.append("暂无错误码信息。\n");
            return;
        }
        content.append("| 子错误码 | 子错误码描述 | 解决方案 |\n");
        content.append("| ------ | ------ | ------ |\n");
        errorCodes.forEach(errorCode ->
                content.append("| ")
                        .append(errorCode.get("subErrorCode")).append(" | ")
                        .append(formatDescription(errorCode.get("subErrorMsg"))).append(" | ")
                        .append(MdUrlConverterUtils.parseUrlsAndConvert(formatDescription(errorCode.get("outerSolution")), urlConverter)).append(" |\n"));
        content.append("\n");
    }

    private void addBasicInfo() {
        content.append("## 基本信息\n\n");
        content.append("- **API ID**: ").append(getOrDefault(apiData, "apiId", "")).append("\n");
        content.append("- **API 分组**: ").append(getOrDefault(apiData, "apiGroup", "")).append("\n");
        content.append("- **请求路径**: `").append(getOrDefault(apiData, "path", "")).append("`\n");
        content.append("- **请求方法**: ").append(getOrDefault(apiData, "method", "")).append("\n");
//        content.append("- **类型**: ").append(getOrDefault(apiData, "type", "")).append("\n");
        content.append("- **描述**: ").append(MdUrlConverterUtils.parseUrlsAndConvert(getOrDefault(apiData, "desc", "", true), urlConverter)).append("\n");
        JsonNode securityReqs = apiData.get("securityReqs");
        if (securityReqs != null && securityReqs.isArray() && securityReqs.size() > 0) {
            content.append("- **安全需求**: ");
            for (int i = 0; i < securityReqs.size(); i++) {
                if (i > 0) content.append(", ");
                content.append(securityReqs.get(i).asText());
            }
            content.append("\n");
        }
        content.append("- **最后更新时间**: ").append(getOrDefault(apiData, "lastModifiedDate", "")).append("\n");

        content.append("\n");
    }

    private void addRequestParameters() {
        JsonNode reqParams = apiData.get("reqParams");
        if (reqParams != null && reqParams.size() > 0) {
            content.append("## 请求参数\n\n");

            reqParams.fields().forEachRemaining(entry -> {
                String contentType = entry.getKey();
                JsonNode params = entry.getValue();

                boolean isJsonContentType = contentType.toLowerCase().contains("json");
                if (reqParams.size() > 1 && !isJsonContentType) {
                    return;
                }

                content.append("### ").append(contentType).append("\n\n");

                if (params != null && params.size() > 0) {
                    content.append("| 参数名 | 参数说明 | 类型 | 是否必填 | 描述 | 示例值 |\n");
                    content.append("| ------ | ------ | ------ | ------ | ------ | ------ |\n");
                    // 兼容json参数
                    int indentLevel;
                    if (isJsonContentType && params.size() == 1 && params.get(0).has("ref") &&
                            (!params.get(0).has("name") || params.get(0).get("name").asText().contains("."))) {
                        indentLevel = -1;
                    } else {
                        indentLevel = 0;
                    }
                    Map<String, Object> parentContainer = new HashMap<>();
                    params.forEach(param -> addParameterRow(parentContainer, param, indentLevel, isJsonContentType, false));
                    defaultReqExampleMap.putAll(parentContainer);
                } else {
                    content.append("无需请求参数\n");
                }
                content.append("\n");
            });
        }
    }

    private String formatParamType(JsonNode param) {
        String paramType = getOrDefault(param, "type", "").toLowerCase();
        
        // 获取maxLength
        Integer maxLength = null;
        if (param.has("maxLength")) {
            JsonNode maxLengthNode = param.get("maxLength");
            if (maxLengthNode.isNumber()) {
                maxLength = maxLengthNode.asInt();
            }
        }
        
        // 特殊处理数组类型
        if ("array".equals(paramType)) {
            JsonNode items = param.get("items");
            JsonNode ref = param.get("ref");
            if (null != ref && !ref.isNull()) {
                paramType = "object[]";
            } else if (items != null) {
                String itemType = getOrDefault(items, "type", "string").toLowerCase();
                paramType = itemType + "[]";
            } else {
                // 如果没有items属性，默认为string[]
                paramType = "string[]";
            }
        }
        
        // 如果有最大长度限制，添加到类型后面
        if (maxLength != null) {
            return String.format("%s(%d)", paramType, maxLength);
        }
        
        return paramType;
    }

    private void addParameterRow(Map<String, Object> parentContainer, JsonNode param, int indentLevel, boolean isJsonContentType, boolean isResponse) {
        String name = getOrDefault(param, "name", ""), indentName = name;
        String title = getOrDefault(param, "title", "");
        String paramType = formatParamType(param);
        String required = param.has("required") && param.get("required").asBoolean() ? "是" : "否";
        String desc = MdUrlConverterUtils.parseUrlsAndConvert(formatDescription(param.get("desc")), urlConverter);

        // 处理示例值
        String example = MdUrlConverterUtils.parseUrlsAndConvert(getExampleValue(param), urlConverter);

        // 添加缩进
        if (isJsonContentType && indentLevel > 0) {
            StringBuilder indent = new StringBuilder();
            for (int i = 0; i < indentLevel; i++) {
                indent.append("&nbsp;&nbsp;");
            }
            indentName = indent + "└─ " + name;
        }

        // 添加参数行
        if (!isJsonContentType || !(StringUtils.startsWithAny(paramType, "object", "array") && indentLevel < 0)) {
            if (isResponse) {
                content.append(String.format("| %s | %s | %s | %s | %s |\n",
                        indentName, title, paramType, desc, example));
            } else {
                content.append(String.format("| %s | %s | %s | %s | %s | %s |\n",
                        indentName, title, paramType, required, desc, example));
            }
            parentContainer.put(name, example);
        }

        if (!StringUtils.startsWithAny(paramType, "object", "array")) {
            return;
        }

        // 处理嵌套对象
        if (paramType.startsWith("object") && param.has("ref")) {
            handleRefObject(parentContainer, param, name, paramType, indentLevel, isJsonContentType, isResponse);
        }

        // 处理嵌套对象
        if (paramType.startsWith("array") && param.has("ref")) {
            handleRefObject(parentContainer, param, name, paramType, indentLevel, isJsonContentType, isResponse);
        }
    }

    private void handleRefObject(Map<String, Object> parentContainer, JsonNode param, String name, String paramType, int indentLevel,
                                 boolean isJsonContentType, boolean isResponse) {
        String refName = getOrDefault(param, "ref", "");
        
        if (processedRefs.contains(refName)) {
            if (isResponse) {
                content.append(String.format("| %s[循环引用] | 已在上方定义 | %s | 循环引用到 %s | - |\n",
                        name, paramType, refName));
            } else {
                content.append(String.format("| %s[循环引用] | 已在上方定义 | %s | - | 循环引用到 %s | - |\n",
                        name, paramType, refName));
            }
            return;
        }

        processedRefs.add(refName);

        try {
            JsonNode modelJson = fetchModelJson(refName);
            if (modelJson != null && modelJson.isArray()) {
                Map<String, Object> childContainer;
                if (indentLevel > 0) {
                    childContainer = new HashMap<>();
                    if (paramType.startsWith("array") || paramType.endsWith("[]")) {
                        parentContainer.put(name, Collections.singletonList(childContainer));
                    } else {
                        parentContainer.put(name, childContainer);
                    }
                } else {
                    childContainer = parentContainer;
                }

                modelJson.forEach(child ->
                    addParameterRow(childContainer, child, indentLevel + 1, isJsonContentType, isResponse));
            }
        } finally {
            processedRefs.remove(refName);
        }
    }

    private JsonNode fetchModelJson(String refName) {
        // 获取引用模型的逻辑
        if (null != refName) {
            String apiGroup = getOrDefault(apiData, "apiGroup", "");
            try {
                return objectMapper.readTree(modelMap.get(apiGroup + "." + refName));
            } catch (Exception e) {
                log.error("fail to fetchModelJson, apiGroup:{}, model:{}, ex:", apiGroup, refName, e);
            }
        }
        return null;
    }

    private void addRequestExamples() {
        JsonNode reqExamples = apiData.get("reqExamples");
        content.append("### 请求示例\n\n");
        
        if (reqExamples != null && reqExamples.size() > 0 && hasNonEmptyExample(reqExamples)) {
            addExamples(reqExamples);
        } else {
            // 如果没有示例，自动生成HTTP格式报文
            generateHttpRequestExample();
        }

        content.append("\n");
    }

    private void generateHttpRequestExample() {
        String path = getOrDefault(apiData, "path", "");
        String method = getOrDefault(apiData, "method", "POST");
        JsonNode reqParams = apiData.get("reqParams");
        
        if (reqParams == null || reqParams.size() == 0) {
            return;
        }

        reqParams.fields().forEachRemaining(entry -> {
            String contentType = entry.getKey();
            JsonNode params = entry.getValue();

            if (params == null || params.size() == 0) {
                return;
            }

            content.append("#### ").append(contentType).append("\n");
            
            // 根据不同的请求类型生成示例
            if (method.equalsIgnoreCase("GET")) {
                content.append(API_DOC_ATTENTION_URL_ENCODE);
                generateGetExample(path, params);
            } else if (contentType.toLowerCase().contains("json")) {
                generateJsonExample(params);
            } else {
                content.append(API_DOC_ATTENTION_URL_ENCODE);
                generateFormExample(params);
            }
            content.append("\n");
        });
    }

    private void generateGetExample(String path, JsonNode params) {
        StringBuilder queryString = new StringBuilder();
        params.forEach(param -> {
            String name = getOrDefault(param, "name", "");
            String example = getExampleValue(param);
            
            if (queryString.length() > 0) {
                queryString.append("&");
            }
            try {
                queryString.append(URLEncoder.encode(name, "UTF-8"))
                          .append("=")
                          .append(URLEncoder.encode(URLEncoder.encode(example, "UTF-8"), "UTF-8"));
            } catch (UnsupportedEncodingException e) {
                // fallback to non-encoded values
                queryString.append(name).append("=").append(example);
            }
        });
        
        content.append("```\n");
        content.append(path);
        if (queryString.length() > 0) {
            content.append("?").append(queryString);
        }
        content.append("\n```\n");
    }

    private void generateJsonExample(JsonNode params) {
        try {
            content.append("```json\n");
            content.append(objectMapper.writerWithDefaultPrettyPrinter().writeValueAsString(defaultReqExampleMap).replace("<br>", "\n"));
            content.append("\n```\n");
        } catch (Exception e) {
            content.append("```json\n{}\n```\n");
        }
    }

    private void generateFormExample(JsonNode params) {
        StringBuilder formData = new StringBuilder();
        params.forEach(param -> {
            String name = getOrDefault(param, "name", "");
            String example = getExampleValue(param);
            
            if (formData.length() > 0) {
                formData.append("&");
            }
            try {
                formData.append(URLEncoder.encode(name, "UTF-8"))
                       .append("=")
                       .append(URLEncoder.encode(URLEncoder.encode(example, "UTF-8"), "UTF-8"));
            } catch (UnsupportedEncodingException e) {
                // fallback to non-encoded values
                formData.append(name).append("=").append(example);
            }
        });
        
        content.append("```\n");
        content.append(formData);
        content.append("\n```\n");
    }

    private String getExampleValue(JsonNode param) {
        // 首先检查是否有示例值
        if (param.has("example")) {
            String example = param.get("example").asText();
            if (!example.equals("-") && !example.isEmpty()) {
                return formatDescription(example);
            }
        }
        
        // 根据类型生成示例值
        String type = getOrDefault(param, "type", "string").toLowerCase();
        switch (type) {
            case "integer":
            case "long":
                return "100";
            case "number":
            case "float":
            case "double":
                return "10.51";
            case "boolean":
                return "true";
            case "array":
                return "[]";
            case "object":
                return "{}";
            default:
                return "sample_" + getOrDefault(param, "name", "value");
        }
    }

    private void addResponseParameters() {
        JsonNode respParams = apiData.get("respParams");
        if (respParams != null && respParams.size() > 0) {
            content.append("## 响应参数\n\n");

            respParams.fields().forEachRemaining(entry -> {
                String contentType = entry.getKey();
                JsonNode params = entry.getValue();

                content.append("### ").append(contentType).append("\n\n");

                if (params != null && params.size() > 0) {
                    content.append("| 参数名 | 参数说明 | 类型 | 描述 | 示例值 |\n");
                    content.append("| ------ | ------ | ------ | ------ | ------ |\n");

                    boolean isJsonContentType = contentType.toLowerCase().contains("json");

                    int indentLevel;
                    if (isJsonContentType && params.size() == 1 && params.get(0).has("ref") &&
                            (!params.get(0).has("name") || params.get(0).get("name").asText().contains("."))) {
                        indentLevel = -1;
                    } else {
                        indentLevel = 0;
                    }
                    Map<String, Object> parentContainer = new HashMap<>();
                    params.forEach(param -> addParameterRow(parentContainer, param, indentLevel, true, true));
                    defaultRespExampleMap.putAll(parentContainer);
                }
                content.append("\n");
            });
        }
    }

    private void addResponseExamples() {
        JsonNode respExamples = apiData.get("respExamples");
        content.append("### 响应示例\n\n");
        
        if (respExamples != null && respExamples.size() > 0 && hasNonEmptyExample(respExamples)) {
            addExamples(respExamples);
        } else {
            // 如果没有示例，自动生成响应报文
            generateResponseExample();
        }
        content.append("\n");
    }

    private void generateResponseExample() {
        JsonNode respParams = apiData.get("respParams");
        if (respParams == null || respParams.size() == 0) {
            return;
        }

        respParams.fields().forEachRemaining(entry -> {
            String contentType = entry.getKey();
            JsonNode params = entry.getValue();

            if (params == null || params.size() == 0) {
                return;
            }

            content.append("#### ").append(contentType).append("\n");
            
            // 生成JSON格式的响应示例
//            Object respExample = defaultRespExampleMap;
//            if (MapUtils.isEmpty(defaultRespExampleMap)) {
//                ObjectNode jsonBody = objectMapper.createObjectNode();
//                params.forEach(param -> {
//                    String name = getOrDefault(param, "name", "");
//                    String example = getExampleValue(param);
//                    jsonBody.put(name, example);
//                });
//            }
            
            try {
                content.append("```json\n");
                content.append(objectMapper.writerWithDefaultPrettyPrinter().writeValueAsString(defaultRespExampleMap));
                content.append("\n```\n\n");
            } catch (Exception e) {
                content.append("```json\n{}\n```\n\n");
            }
        });
    }

    private void addExamples(JsonNode examples) {
        examples.fields().forEachRemaining(entry -> {
            String name = entry.getKey();
            JsonNode example = entry.getValue();
            
            if (!example.isNull()) {
                content.append("#### ").append(name).append("\n");
                try {
                    String exampleStr = example.isTextual() ? example.asText() : example.toString();
                    if (exampleStr.startsWith("{") || exampleStr.startsWith("[")) {
                        JsonNode jsonNode = objectMapper.readTree(exampleStr);
                        content.append("```json\n")
                              .append(objectMapper.writerWithDefaultPrettyPrinter().writeValueAsString(jsonNode))
                              .append("\n```\n\n");
                    } else {
                        content.append("```\n").append(exampleStr).append("\n```\n\n");
                    }
                } catch (Exception e) {
                    content.append("```\n").append(example.toString()).append("\n```\n\n");
                }
            }
        });
    }

    private void addSampleCodes() {
        JsonNode sampleCodes = apiData.get("sampleCodes");
//        if (sampleCodes != null && sampleCodes.size() > 0) {
//            content.append("## 示例代码\n\n");
//
//            sampleCodes.fields().forEachRemaining(entry -> {
//                String language = entry.getKey();
//                String code = entry.getValue().asText();
//
//                // Skip certain implementations
//                if (("JAVA".equals(language) && sampleCodes.has("JAVA_COMMON")) ||
//                    "PHP".equals(language)) {
//                    return;
//                }
//
//                String languageName = language.split("_")[0];
//                if (StringUtils.isNotEmpty(code)) {
//                    content.append("### ").append(languageName).append("\n");
//                    content.append("```").append(languageName.toLowerCase()).append("\n");
//                    content.append(code).append("\n");
//                    content.append("```\n\n");
//                }
//            });
//        }
        // 只留java
        if (sampleCodes != null && sampleCodes.size() > 0 && sampleCodes.has("JAVA_COMMON")) {
            String code = sampleCodes.get("JAVA_COMMON").asText();
            if (StringUtils.isNotEmpty(code)) {
                content.append("## 示例代码\n\n");
                content.append("### ").append("JAVA").append("\n");
                content.append("```").append("java").append("\n");
                content.append(code).append("\n");
                content.append("```\n\n");
            }
        }

    }

    private String formatDescription(JsonNode descNode) {
        if (descNode == null || descNode.isNull()) {
            return " ";
        }
        String desc = descNode.asText(" ");
        return desc.replace("\n", "<br>");
    }

    private String formatDescription(String originalDesc) {
        if (StringUtils.isBlank(originalDesc)) {
            return " ";
        }
        return originalDesc.replace("\n", "<br>");
    }



    private String getOrDefault(JsonNode node, String field, String defaultValue) {
        return getOrDefault(node, field, defaultValue, false);
    }

    private String getOrDefault(JsonNode node, String field, String defaultValue, boolean format) {
        JsonNode value = node.get(field);
        return value != null && !value.isNull() ? (format ? formatDescription(value) : value.asText()) : defaultValue;
    }

    private boolean hasNonEmptyExample(JsonNode examples) {
        AtomicBoolean hasNonEmpty = new AtomicBoolean(false);
        examples.fields().forEachRemaining(entry -> {
            JsonNode example = entry.getValue();
            if (!example.isNull() && 
                ((example.isTextual() && StringUtils.isNotBlank(example.asText())) || 
                 (!example.isTextual() && example.size() > 0))) {
                hasNonEmpty.set(true);
            }
        });
        return hasNonEmpty.get();
    }

    public String convert() throws IOException {
        String markdown = convertToMarkdown();
        return saveToFile(markdown);
    }

    private String saveToFile(String markdown) throws IOException {
        Path outputPath = Paths.get(outputDir);
        Files.createDirectories(outputPath);

        String filename = Paths.get(jsonFilePath).getFileName().toString().replace(".json", ".md");
        Path outputFilePath = outputPath.resolve(filename);

        Files.write(outputFilePath, markdown.getBytes());
        return outputFilePath.toString();
    }

    public static void main(String[] args) {
        if (args.length < 1) {
            System.out.println("Usage: java ApiMdUtils <json_file_path> [output_dir]");
            System.exit(1);
        }

        String jsonFilePath = args[0];
        String outputDir = args.length > 1 ? args[1] : null;

        try {
            ApiMdUtils converter = new ApiMdUtils(jsonFilePath, outputDir);
            String outputFile = converter.convert();
            System.out.println("Successfully converted to Markdown. Output file: " + outputFile);
        } catch (Exception e) {
            System.err.println("Conversion failed: " + e.getMessage());
            e.printStackTrace();
            System.exit(1);
        }
    }

}