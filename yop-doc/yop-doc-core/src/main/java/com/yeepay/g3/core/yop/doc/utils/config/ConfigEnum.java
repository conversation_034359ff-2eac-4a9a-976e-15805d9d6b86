/**
 * Copyright: Copyright (c)2011
 * Company: 易宝支付(YeePay)
 */
package com.yeepay.g3.core.yop.doc.utils.config;

import com.google.common.collect.Maps;
import com.yeepay.boot.components.config.ConfigKey;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.Map;

import static com.yeepay.g3.core.yop.doc.utils.YopConstant.*;

/**
 * <p>Title: 配置信息</p>
 * <p>Description: 描述</p>
 * <p>Copyright: Copyright (c)2011</p>
 * <p>Company: 易宝支付(YeePay)</p>
 *
 * <AUTHOR>
 * @version 0.1, 14-5-18 17:35
 */
public enum ConfigEnum implements ConfigKey {

    /**
     * 敏感数据中，数据库标注所占比重
     */
    YOP_PARAM_SENSITIVE_DB_WEIGHT("YOP_PARAM_SENSITIVE_DB_WEIGHT", 0.0),

    YOP_DOC_SEARCH_NUM("YOP_DOC_SEARCH_NUM", 100),
    YOP_DOC_SEARCH_CONFIG("YOP_DOC_SEARCH_CONFIG", new HashMap<String, String>() {
        {
            put(SEARCH_MAX_NUM, "100");
            put(SEARCH_KEY_WORD_MAX_LENGTH, "30");
        }
    }),

    YOP_DOC_PREFIX("YOP_DOC_PREFIX", "/docs"),

    YOP_DOC_IGNORE_ERROR_CODE_PREFIX("YOP_DOC_IGNORE_ERROR_CODE_PREFIX", new ArrayList<String>() {
        {
            add("sys.internal");
        }
    }),

    YOP_CEPH_CONFIG("YOP_CEPH_CONFIG", new HashMap<String, String>(){
            {
                put(CEPH_BUCKET_NAME, "yop_doc");
                put(CEPH_YCS_URL, "qak8s.iaas.yp:30231");
                put(CEPH_TOKEN, "7efac920705d0d0a54d531cfefcd9e6ab118e1b3a9314e13b08dd0193ea62b5b882c39727bbba152c71723760eacd09df96386c8da28c891a728ab4602e56c5ff8172575c1cf25ff80fff35e396714f4");
                put(CEPH_BATCH_POST_ADDRESS, "http://qak8s.iaas.yp:30231/api/v1/storage/buckets/");
                put(CEPH_STATIC_URL, "https://qastaticres.yeepay.com/");
                put(CEPH_NGINX_ACCESS_PREFIX, "https://qaopen.yeepay.com");
            }
    }),

    YOP_DOC_PLATFORM_ERROR_CODE_PAGE("YOP_DOC_PLATFORM_ERROR_CODE_PAGE", new HashMap<String, String>() {
        {
            put("docNo", "platform");
            put("pagePath", "sdk_guide/error_code");
        }
    }),
    YOP_DOC_LINK_VALIDATE_SWITCH("YOP_DOC_LINK_VALIDATE_SWITCH", true),

    YOP_DOC_I18N_SUPPORT_LANGS("YOP_DOC_I18N_SUPPORT_LANGS", new ArrayList<String>() {
        {
            add("en_US");
        }
    }),

    YOP_DOC_API_CONTENT_TEMPLATE("YOP_DOC_API_CONTENT_TEMPLATE", new HashMap<String, String>() {
        {
            put("blocks", "[{\"title\":\"应用场景\",\"data\":\"## 应用场景\\n//该接口和服务主要的应用场景是什么（eg：谁可以通过该服务做什么获取什么样的结果）// \\n//需要产品自行编辑//\",\"type\":\"text\"},{\"title\":\"调用说明\",\"data\":\"## 调用说明\\n//调用该接口和服务需要有哪些特殊的要点和注意事项//\\n//需要产品自行编辑//\",\"type\":\"text\"}]");
        }
    }),
    YOP_ERROR_CODES("YOP_ERROR_CODES", new HashMap<String, String>() {{
        put("40020", "服务不可用");
        put("40021", "授权权限不足");
        put("40029", "访问受限");
        put("40041", "缺少必填参数");
        put("40042", "非法的参数");
        put("40044", "业务处理失败");
        put("40047", "鉴权认证失败");
        put("40049", "SDK 故障");
        put("50001", "内部异常");
    }}),

    YOP_PORTAL_API_CLASSIC("YOP_PORTAL_API_CLASSIC", new HashMap<String, String>() {{
        put("PAY", "支付服务");
        put("HISTORY", "历史接口");
    }}),

    //commonRefType,commonRefId,in,name,type,required,maxLength,desc,example
    YOP_API_COMMON_DEFINE_CONFIG("YOP_API_COMMON_DEFINE_CONFIG", new ArrayList<String>() {
        {
            add("REQ##ALL##header##authorization##string##true##-##鉴权认证串##-");
            add("REQ##ALL##header##x-yop-request-id##string##true##-##请求唯一标识，每笔请求不同，推荐使用 UUID##-");
            add("REQ##ALL##header##x-yop-appkey##string##true##-##应用标识##-");
            add("REQ##ALL##header##content-type##string##true##-##例如：application/x-www-form-urlencoded; charset=UTF-8##-");
            add("REQ##ALL##header##x-yop-sdk-langs##string##true##-##sdk 语言(如：java/python/go)##-");
            add("REQ##ALL##header##x-yop-sdk-version##string##true##-##sdk 版本号，自实现目前统一使用 4.0.0##-");
            add("REQ##ALL##header##user-agent##string##false##-##将 sdk 语言，sdk 版本，操作系统类型，操作系统版本，虚拟机版本，虚拟机类型，sdk 运行语言版本，操作系统语言，操作系统区域用\"/\"连接##-");
            add("REQ##ALL##header##x-yop-session-id##string##false##-##请求会话标识，应用重启前只需要初始化一次，推荐使用 UUID##-");
            add("RESP##ALL##header##x-yop-request-id##string##true##-##请求 ID##-");
            add("RESP##ALL##header##x-yop-sign##string##true##-##响应体签名##-");
            add("RESP##FILE_DOWNLOAD##header##Content-Disposition##string##true##-##附件信息(名称)##-");
            add("RESP##FILE_DOWNLOAD##header##x-yop-hash-crc64ecma##string##true##-##文件crc64校验值##-");
            add("RESP##FILE_DOWNLOAD##body##-##binary##true##-##文件流##-");
        }
    }),
    /**
     * 遗留的yos download api
     */
    YOP_WEB_DOWNLOAD_APIS("YOP_WEB_DOWNLOAD_APIS", new ArrayList<String>(){
        {
            add("/yos/v1.0/std/bill/cashdaydownload");
            add("/yos/v1.0/std/bill/dividedaydownload");
            add("/yos/v1.0/std/bill/remitdaydownload");
            add("/yos/v1.0/std/bill/tradedaydownload");
        }
    }),
    /**
     * 忽略环境
     */
    YOP_DOC_LEADER_IGNORE_ENV("YOP_DOC_LEADER_IGNORE_ENV", new ArrayList<String>(){
        {
            add("NC");
        }
    }),
    /**
     * 控制api的请求方法(多个请求方式时，默认展示POST，在该配置中的话，展示GET)
     */
    YOP_API_HTTP_GET_APIS("YOP_API_HTTP_GET_APIS", new ArrayList<String>(){
        {
            add("nothing");
        }
    }),

    /**
     * doc super admins
     */
    DOC_SUPER_ADMIN("DOC_SUPER_ADMIN", new ArrayList<String>() {
        {
            add("PLATFORM:admin");
            add("PLATFORM:op");
            add("PLATFORM:pm");
        }
    }),

    YOP_SECURITY_REQ_DEPRECATED("YOP_SECURITY_REQ_DEPRECATED", new ArrayList<String>() {{
        add("YOP-HMAC-AES128");
        add("YOP-HMAC-AES256");
    }}),

    YOP_IDEMPOTENT_OPTION_DOC_TEMPLATE("YOP_IDEMPOTENT_OPTION_DOC_TEMPLATE", "该接口支持幂等请求，具体规则如下：<br>" +
            "判定原则：{{#useAllParams}}所有{{/useAllParams}}请求参数{{params}}{{#useMultiParams}}联合{{/useMultiParams}}作为唯一标识，值相同则认为是重复请求<br>" +
            "处理方式：当判定为重复请求时，不再重复处理, {{#hasBizError}}返回异常，错误码为{{bizErrorCode}}{{/hasBizError}}{{^hasBizError}}正常返回(详见响应参数){{/hasBizError}}"),
    /**
     * 文档中心页面开关
     */
    YOP_DOC_PAGE_SWITCH("YOP_DOC_PAGE_SWITCH", new HashMap<String, String>() {
        {
            put("platform-doc", "-9999");
        }
    }),

    YOP_DOC_APIFOX_CONFIG("YOP_DOC_APIFOX_CONFIG", new HashMap<String, String>() {
        {
            put("enable", "false");
            put("projectId", "3155515");
            put("account", "xxx");
            put("password", "xxx");
            put("tokenUrlTemplate", "https://api.apifox.com/api/v1/login?locale=zh-CN");
            put("dataUrlTemplate", "https://api.apifox.com/api/v1/export-data?__xAuthorization=%s&__xProjectId=%s&locale=zh-CN");
            put("debugUrlTemplate", "https://apifox.com/apidoc/project-$projectId/api-$apiFoxId");
            put("debugUrlAllTemplate", "https://apifox.com/apidoc/project-{{projectId}}");
            put("accessToken", "");
            put("apiJsonPathExpression", "$.apiCollection[*].items[*].items[*].items[*].api");
        }
    }),
    YOP_MD_DOC_ACCESS_PREFIX("YOP_MD_DOC_ACCESS_PREFIX", "https://open.yeepay.com/docs-v3"),
    YOP_DOC_CENTER_ACCESS_HOST("YOP_DOC_CENTER_ACCESS_HOST", "open.yeepay.com"),
    ;

    private static Map<String, ConfigEnum> valueMap = Maps.newHashMap();

    private String configKey;
    private Object defaultValue;

    static {
        for (ConfigEnum item : ConfigEnum.values()) {
            valueMap.put(item.configKey, item);
        }
    }

    ConfigEnum(String configKey, Object defaultValue) {
        this.configKey = configKey;
        this.defaultValue = defaultValue;
    }

    public String getConfigKey() {
        return configKey;
    }

    public Object getDefaultValue() {
        return defaultValue;
    }

}
