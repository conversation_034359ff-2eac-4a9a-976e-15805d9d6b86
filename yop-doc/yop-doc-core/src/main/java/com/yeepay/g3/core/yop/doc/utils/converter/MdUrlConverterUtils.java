/*
 * Copyright: Copyright (c)2011
 * Company: 易宝支付(YeePay)
 */

package com.yeepay.g3.core.yop.doc.utils.converter;

import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * title: <br>
 * description: 描述<br>
 * Copyright: Copyright (c)2014<br>
 * Company: 易宝支付(YeePay)<br>
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2025/6/20
 */
public class MdUrlConverterUtils {

    private static final Pattern MD_PATTERN = Pattern.compile("\\[([^\\]]+)\\]\\(([^\\)]+)\\)");
    private static final Pattern A_TAG_PATTERN = Pattern.compile("<a\\s+[^>]*href=\\\"([^\\\"]+)\\\"[^>]*>(.*?)</a>", Pattern.CASE_INSENSITIVE);

    public static String parseUrlsAndConvert(String text, UrlConverter urlConverter) {
        if (text == null || text.isEmpty() || urlConverter == null) {
            return text;
        }
        StringBuffer sb = new StringBuffer();
        Matcher matcher = MD_PATTERN.matcher(text);
        while (matcher.find()) {
            String linkText = matcher.group(1);
            String url = matcher.group(2);
            String newUrl = urlConverter.convert(url);
            String replacement = "[" + linkText + "](" + newUrl + ")";
            matcher.appendReplacement(sb, java.util.regex.Matcher.quoteReplacement(replacement));
        }
        matcher.appendTail(sb);
        String result = sb.toString();

        // 处理a标签
        sb = new StringBuffer();
        matcher = A_TAG_PATTERN.matcher(result);
        while (matcher.find()) {
            String url = matcher.group(1);
            String linkText = matcher.group(2);
            String newUrl = urlConverter.convert(url);
            String replacement = "<a href=\"" + newUrl + "\">" + linkText + "</a>";
            matcher.appendReplacement(sb, java.util.regex.Matcher.quoteReplacement(replacement));
        }
        matcher.appendTail(sb);
        return sb.toString();
    }

}
