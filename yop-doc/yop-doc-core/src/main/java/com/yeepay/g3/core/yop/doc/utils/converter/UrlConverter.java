/*
 * Copyright: Copyright (c)2011
 * Company: 易宝支付(YeePay)
 */

package com.yeepay.g3.core.yop.doc.utils.converter;

/**
 * title: <br>
 * description: 描述<br>
 * Copyright: Copyright (c)2014<br>
 * Company: 易宝支付(YeePay)<br>
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2025/6/20
 */
public interface UrlConverter {

    /**
     * Converts the given URL to a different format or structure.
     *
     * @param originalUrl the original URL to be converted
     * @return the converted URL as a String
     */
    String convert(String originalUrl);

    class DefaultUrlConverter implements UrlConverter {
        @Override
        public String convert(String originalUrl) {
            return originalUrl;
        }
    }
}
