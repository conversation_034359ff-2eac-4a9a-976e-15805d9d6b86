errcode.attention=Note: for developers who do not use the platform to provide the official SDK, please add x-yop-sdk-langs=development language (such as go, python, PHP, etc.) and x-yop-sdk-version=3.0.0 in the request header. The Yop gateway will return the new version error code with more explicit meaning.
errcode.title=ErrorCode
errcode.desc=Description
errcode.sub.title=SubErrorCode
errcode.sub.desc=SubErrorMsg
errcode.sub.outersolution=Solution
errcode.platform=Platform ErrorCode
errcode.platform.deprecated=Platform ErrorCode(deprecated)
errcode.old.99001001=The app is invalid or does not exist. Please confirm that the appkey is correct and the app status is normal
errcode.old.99001002=The service is invalid or does not exist. Please confirm whether the service address and version are correct according to the API document
errcode.old.99001003=Service not available, service temporarily disabled or offline, please pay attention to API platform announcement
errcode.old.99001004=The application permission is insufficient and illegal. Please make sure that the API permission has been opened
errcode.old.99001005=HTTP method not allowed
errcode.old.99001006=The app lacks the necessary parameters
errcode.old.99001007=Invalid app parameters, incorrect format, illegal value, out of bounds, etc
errcode.old.99001008=Invalid app signature
errcode.old.99001009=App message decryption failed
errcode.old.99001010=App encoding error, please use UTF-8 to encode the request parameter value
errcode.old.99001013=The app call exceeded the maximum processing time in milliseconds
errcode.old.99001018=App file upload failed
errcode.old.99001019=Wrong format of app file
errcode.old.99001021=Application of non whitelist IP
errcode.old.99001026=RSA key error
errcode.old.99999000=The server system is abnormal, please try again later
errcode.old.99100001=Parameter is not allowed to be null
errcode.old.99100002=Parameter cannot be empty
errcode.old.99100003=Parameter format does not match
errcode.old.99100004=Parameter exceeds maximum length
errcode.old.99100005=Parameter insufficient minimum length
errcode.old.99100006=The parameter is greater than the maximum value
errcode.old.99100007=The parameter is less than the minimum value
errcode.old.99100008=Illegal parameter email address
errcode.old.99100009=Illegal parameter mobile number
errcode.old.99100010=Illegal parameter URL format
errcode.old.99100011=Parameter is not a valid integer
errcode.old.99100012=Parameter is not a valid number


