<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yeepay.g3.core.yop.doc.repository.mysql.doc.AttachmentRepository">
    <resultMap type="com.yeepay.g3.core.yop.doc.entity.AttachmentEntity" id="entity">
        <id property="id" column="id"/>
        <result property="fileId" column="file_id"/>
        <result property="bizCode" column="biz_code"/>
        <result property="fileName" column="file_name"/>
        <result property="fileType" column="file_type"/>
        <result property="storeType" column="store_type"/>
        <result property="storePath" column="store_path"/>
        <result property="version" column="version"/>
        <result property="createdDate" column="created_datetime"/>
        <result property="lastModifiedDate" column="last_modified_datetime"/>
    </resultMap>

    <insert id="save" parameterType="com.yeepay.g3.core.yop.doc.entity.AttachmentEntity" useGeneratedKeys="true" keyProperty="id">
        INSERT INTO tbl_attachment(file_id,biz_code,file_name,file_type,store_type,store_path,version,created_datetime,last_modified_datetime)
          VALUES (#{fileId},#{bizCode},#{fileName},#{fileType},#{storeType},#{storePath},#{version},CURRENT_TIMESTAMP,CURRENT_TIMESTAMP)
    </insert>

    <select id="findOne" parameterType="java.lang.Long" resultMap="entity">
        SELECT * FROM tbl_attachment WHERE id = #{id}
    </select>

    <select id="findByFileId" parameterType="java.lang.String" resultMap="entity">
        SELECT * FROM tbl_attachment WHERE file_id = #{fileId} LIMIT 1
    </select>

    <select id="findByIds" parameterType="java.util.List" resultMap="entity">
        SELECT * FROM tbl_attachment WHERE id IN
        <foreach collection="ids" open="(" item="item" separator="," close=")">
            #{item}
        </foreach>
    </select>

    <update id="update" parameterType="com.yeepay.g3.core.yop.doc.entity.AttachmentEntity">
        UPDATE tbl_attachment
        <set>
            <if test="null != fileName and '' != fileName">
                file_name = #{fileName},
            </if>
            <if test="null != storePath and '' != storePath">
                store_path = #{storePath},
            </if>
            <if test="null != fileType">
                file_type = #{fileType},
            </if>
            version = version + 1
        </set>
        WHERE id = #{id} AND version = #{version}
    </update>

    <delete id="deleteById" parameterType="java.lang.Long">
        DELETE FROM tbl_attachment WHERE id = #{id}
    </delete>

    <delete id="delete" parameterType="com.yeepay.g3.core.yop.doc.entity.AttachmentEntity">
        DELETE FROM tbl_attachment WHERE id = #{id} AND version = #{version}
    </delete>

    <delete id="batchDeleteById" parameterType="java.util.List">
        DELETE FROM tbl_attachment WHERE id IN
        <foreach collection="ids" open="(" item="item" separator="," close=")">
            #{item}
        </foreach>
    </delete>
</mapper>