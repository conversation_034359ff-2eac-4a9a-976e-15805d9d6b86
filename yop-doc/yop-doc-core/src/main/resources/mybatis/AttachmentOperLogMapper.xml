<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yeepay.g3.core.yop.doc.repository.mysql.doc.AttachmentOperLogRepository">
    <resultMap type="com.yeepay.g3.core.yop.doc.entity.AttachmentOperLogEntity" id="entity">
        <id property="id" column="id"/>
        <result property="fileId" column="file_id"/>
        <result property="version" column="version"/>
        <result property="operator" column="operator"/>
        <result property="operateType" column="operate_type"/>
        <result property="bakValue" column="bak_value"/>
        <result property="createdDate" column="created_datetime"/>
    </resultMap>

    <insert id="save" parameterType="com.yeepay.g3.core.yop.doc.entity.AttachmentOperLogEntity" keyProperty="id" useGeneratedKeys="true">
      INSERT INTO tbl_attachment_oper_log(file_id,version,operator,operate_type,bak_value,created_datetime)
        VALUE (#{fileId},#{version},#{operator},#{operateType},#{bakValue},CURRENT_TIMESTAMP)
    </insert>

    <insert id="batchSave" parameterType="java.util.List">
      INSERT INTO tbl_attachment_oper_log(file_id,version,operator,operate_type,bak_value,created_datetime)
        VALUES
        <foreach collection="list" item="item" open="" close="" separator=",">
            (#{item.fileId},#{item.version},#{item.operator},#{item.operateType},#{item.bakValue},CURRENT_TIMESTAMP)
        </foreach>
    </insert>
</mapper>