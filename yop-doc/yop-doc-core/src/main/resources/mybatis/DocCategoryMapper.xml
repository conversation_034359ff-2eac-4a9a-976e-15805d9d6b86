<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yeepay.g3.core.yop.doc.repository.mysql.doc.DocCategoryRepository">

    <resultMap type="com.yeepay.g3.core.yop.doc.entity.DocCategoryEntity" id="docCategoryMap">
        <id property="id" column="id"/>
        <result property="pid" column="pid"/>
        <result property="scope" column="scope"/>
        <result property="type" column="type"/>
        <result property="code" column="code"/>
        <result property="name" column="name"/>
        <result property="desc" column="desc"/>
        <result property="seq" column="seq"/>
        <result property="version" column="version"/>
        <result property="createdDate" column="created_datetime"/>
        <result property="lastModifiedDate" column="last_modified_datetime"/>
    </resultMap>

    <insert id="save" parameterType="com.yeepay.g3.core.yop.doc.entity.DocCategoryEntity" useGeneratedKeys="true" keyProperty="id">
        INSERT INTO tbl_doc_category(`pid`,`scope`,`type`,`code`,`name`,`desc`,`seq`,version,created_datetime,last_modified_datetime)
          VALUES (#{pid},#{scope},#{type},#{code},#{name},#{desc},#{seq},#{version},CURRENT_TIMESTAMP,CURRENT_TIMESTAMP)
    </insert>

    <select id="findOne" parameterType="java.lang.Long" resultMap="docCategoryMap">
        SELECT * FROM tbl_doc_category WHERE id = #{id}
    </select>

    <update id="update" parameterType="com.yeepay.g3.core.yop.doc.entity.DocCategoryEntity">
        UPDATE tbl_doc_category
        <set>
            <if test="null != pid">
                pid = #{pid},
            </if>
            <if test="null != name and '' != name">
                `name` = #{name},
            </if>
            <if test="null != desc and '' != desc">
                `desc` = #{desc},
            </if>
            <if test="null != scope and '' != scope">
                `scope` = #{scope},
            </if>
            <if test="null != seq">
                `seq` = #{seq},
            </if>
            version = version + 1
        </set>
        WHERE id = #{id} AND version = #{version}
    </update>

    <delete id="delete" parameterType="java.lang.Long">
        DELETE FROM tbl_doc_category WHERE id = #{id}
    </delete>

    <select id="exists" parameterType="java.lang.Long" resultType="java.lang.Long">
        SELECT id FROM tbl_doc_category WHERE id = #{id}
    </select>

    <select id="existsScopeAndName" parameterType="java.util.Map" resultType="java.lang.Long">
        SELECT id FROM tbl_doc_category WHERE `scope` = #{scope} and `name` = #{name}
    </select>

    <select id="existsDocCategoryCode" parameterType="java.util.Map" resultType="java.lang.Long">
        SELECT count(*) FROM tbl_doc_category WHERE `type` = #{type} and `code` = #{code}
    </select>

    <select id="existsDocCategory" resultType="java.lang.Long">
        SELECT count(*)
        FROM tbl_doc_category
        WHERE `type` = #{type}
          and `code` = #{code}
          and `scope` = #{scope}
          and pid = #{pid}
    </select>

    <select id="findByScopeAndName" resultMap="docCategoryMap">
        SELECT * FROM tbl_doc_category WHERE `scope` = #{scope} and `name` = #{name}
    </select>

    <select id="findMaxSeq" resultType="java.lang.Integer">
        SELECT max(seq)
        FROM tbl_doc_category
        WHERE `scope` = #{scope}
          and pid = #{pid}
    </select>

    <select id="findPreSibling" resultMap="docCategoryMap">
        select b.id,b.pid,b.seq,b.code,b.name,b.`desc` from tbl_doc_category a
            inner join tbl_doc_category b on a.pid = b.pid
        where a.id = #{id} and a.seq > b.seq order by b.seq desc limit 1
    </select>

    <select id="findNextSibling" resultMap="docCategoryMap">
        select b.id,b.pid,b.seq,b.code,b.name,b.`desc` from tbl_doc_category a
            inner join tbl_doc_category b on a.pid = b.pid
        where a.id = #{id} and b.seq > a.seq order by b.seq asc limit 1
    </select>

    <select id="findTailSiblings" resultMap="docCategoryMap">
        select a.id,a.pid,a.seq,a.code,a.name,a.`desc` from tbl_doc_category a
        where a.pid = #{pid} and a.seq >= #{seq}
        order by a.seq asc
    </select>

    <select id="findDirectSons" resultMap="docCategoryMap">
        select a.id, a.pid, a.seq, a.code, a.name, a.`desc`
        from tbl_doc_category a
        where a.pid = #{pid}
          and a.type = #{type}
        order by a.seq asc
    </select>

    <select id="findDirectSonsWithCodes" resultMap="docCategoryMap">
        select a.id,a.pid,a.seq,a.code,a.name,a.`desc` from tbl_doc_category a
        where a.pid = #{pid}
          and a.type = #{type}
          and a.scope = #{scope}
        order by a.seq asc
    </select>

    <select id="findByScope" resultMap="docCategoryMap">
        select a.id,a.pid,a.seq,a.code,a.name,a.`desc` from tbl_doc_category a
        where a.`scope` = #{scope}
        order by a.seq asc
    </select>

    <select id="findByScopeAndType" resultMap="docCategoryMap">
        select a.id,a.pid,a.seq,a.code,a.name,a.`desc` from tbl_doc_category a
        where a.`scope` = #{scope} and a.type = #{type}
        order by a.seq asc
    </select>

    <update id="batchUpdateSeqById" parameterType="java.util.List">
        <foreach collection="list" item="item">
            update tbl_doc_category
            set seq = #{item.seq}
            where id = #{item.id};
        </foreach>
    </update>

    <select id="findByIds" parameterType="java.util.List" resultMap="docCategoryMap">
        select * from tbl_doc_category where id in
        <foreach collection="list" item="item" index="index" open="(" close=")" separator=",">
            #{item}
        </foreach>
    </select>

    <select id="existsScopeAndNameAndPid" resultType="java.lang.Long">
        SELECT id
        FROM tbl_doc_category
        WHERE `scope` = #{scope}
          and `name` = #{name}
          and pid = #{pid}
    </select>

</mapper>
