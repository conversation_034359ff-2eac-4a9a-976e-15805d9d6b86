<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yeepay.g3.core.yop.doc.repository.mysql.doc.DocCategoryRelateRepository">

    <resultMap type="com.yeepay.g3.core.yop.doc.entity.DocCategoryRelateEntity" id="entity">
        <id property="id" column="id"/>
        <result property="docNo" column="doc_no"/>
        <result property="categoryId" column="category_id"/>
        <result property="seq" column="seq"/>
        <result property="createdDate" column="created_datetime"/>
    </resultMap>

    <resultMap id="entityWithCategory" type="com.yeepay.g3.core.yop.doc.entity.DocCategoryRelateEntity" extends="entity">
        <result property="categoryCode" column="category_code"/>
        <result property="categoryName" column="category_name"/>
    </resultMap>

    <insert id="batchSave" parameterType="java.util.List">
        insert into tbl_doc_category_relate(doc_no, category_id, seq, created_datetime) values
        <foreach collection="list" item="item" separator=",">
            (#{item.docNo}, #{item.categoryId}, #{item.seq}, CURRENT_TIMESTAMP)
        </foreach>
    </insert>

    <delete id="deleteByDoc">
        delete from tbl_doc_category_relate where doc_no = #{docNo}
    </delete>

    <select id="findByDocNo" resultMap="entity">
        select * from tbl_doc_category_relate where doc_no = #{docNo}
    </select>

    <select id="findByDocNoWithCategory" resultMap="entityWithCategory">
        select a.*,b.code as category_code, b.name as category_name from tbl_doc_category_relate a
        inner join tbl_doc_category b on a.category_id = b.id
        where a.doc_no = #{docNo}
    </select>

    <delete id="batchDelete" parameterType="java.util.List">
        delete from tbl_doc_category_relate where id in
        <foreach collection="list" item="item" separator="," open="(" close=")">
            #{item.id}
        </foreach>
    </delete>

    <select id="findMaxSeqInCategory" resultType="java.lang.Integer">
        select max(seq) from tbl_doc_category_relate where category_id = #{categoryId}
    </select>

    <update id="batchUpdateSeqById">
        <foreach collection="list" item="item">
            update tbl_doc_category_relate set seq = #{item.seq} where id = #{item.id};
        </foreach>
    </update>

    <select id="existsCategoryRef" resultType="java.lang.Long">
        select count(*) from tbl_doc_category_relate a
        where a.category_id = #{categoryId}
    </select>

</mapper>