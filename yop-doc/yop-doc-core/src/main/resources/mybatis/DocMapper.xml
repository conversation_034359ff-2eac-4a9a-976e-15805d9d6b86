<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yeepay.g3.core.yop.doc.repository.mysql.doc.DocRepository">

    <resultMap type="com.yeepay.g3.core.yop.doc.entity.v2.DocEntity" id="docMap">
        <id property="id" column="id"/>
        <result property="docNo" column="doc_no"/>
        <result property="title" column="title"/>
        <result property="type" column="type"/>
        <result property="status" column="status"/>
        <result property="visible" column="visible"/>
        <result property="desc" column="desc"/>
        <result property="logo" column="logo"/>
        <result property="docVersion" column="doc_version"/>
        <result property="seq" column="seq"/>
        <result property="display" column="display"/>
        <result property="version" column="version"/>
        <result property="createdDate" column="created_datetime"/>
        <result property="lastModifiedDate" column="last_modified_datetime"/>
        <collection property="spCodes" ofType="String">
            <id column="sp_code"/>
        </collection>
    </resultMap>

    <resultMap id="docWithCategoryMap" type="com.yeepay.g3.core.yop.doc.entity.v2.DocEntity" extends="docMap">
        <collection property="categories" ofType="com.yeepay.g3.core.yop.doc.entity.DocCategoryRelateEntity">
            <id column="doc_category_relate_id" property="id"/>
            <result column="relate_category_id" property="categoryId"/>
            <result column="relate_category_code" property="categoryCode"/>
            <result column="relate_seq" property="seq"/>
            <result column="relate_parent_category_name" property="pCategoryCode"/>
        </collection>
    </resultMap>

    <insert id="save" parameterType="com.yeepay.g3.core.yop.doc.entity.v2.DocEntity">
        insert into tbl_doc(`doc_no`, `product_code`, `title`, `type`, `status`, `visible`, `desc`, logo, seq, category, display, doc_version, created_datetime, last_modified_datetime, version)
            values (#{docNo}, #{productCode}, #{title}, #{type}, #{status}, #{visible}, #{desc}, #{logo}, #{seq}, #{category}, #{display}, #{docVersion}, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, 0)
    </insert>

    <update id="update" parameterType="com.yeepay.g3.core.yop.doc.entity.v2.DocEntity">
        update tbl_doc
            <set>
                <if test="null != docNo">
                    doc_no = #{docNo},
                </if>
                <if test="null != title">
                    `title` = #{title},
                </if>
                <if test="null != type">
                    type = #{type},
                </if>
                <if test="null != status">
                    `status` = #{status},
                </if>
                <if test="null != visible">
                    `visible` = #{visible},
                </if>
                <if test="null != desc">
                    `desc` = #{desc},
                </if>
                <if test="null != logo">
                    logo = #{logo},
                </if>
                <if test="null != docVersion">
                    doc_version = #{docVersion},
                </if>
                <if test="null != seq">
                    seq = #{seq},
                </if>
                <if test="null != display">
                    display = #{display},
                </if>
                version = version + 1, last_modified_datetime = CURRENT_TIMESTAMP
            </set>
        where id = #{id} and version = #{version}
    </update>

    <update id="updateDocStatus" parameterType="com.yeepay.g3.core.yop.doc.entity.v2.DocEntity">
        update tbl_doc
            set `status` = #{status}
        where id = #{id} and version = #{version}
    </update>

    <update id="logicDelete" parameterType="com.yeepay.g3.core.yop.doc.entity.v2.DocEntity">
        update tbl_doc
            <set>
                <if test="null != status">
                    `status` = #{status},
                </if>
                display = 0, last_modified_datetime = CURRENT_TIMESTAMP
            </set>
        where id = #{id} and version = #{version}
    </update>

    <update id="logicRecover" parameterType="com.yeepay.g3.core.yop.doc.entity.v2.DocEntity">
        update tbl_doc
            <set>
                <if test="null != status">
                    `status` = #{status},
                </if>
                display = 1, last_modified_datetime = CURRENT_TIMESTAMP
            </set>
        where id = #{id} and version = #{version}
    </update>

    <select id="findAll" resultMap="docMap">
        select a.*,b.sp_code from tbl_doc a inner join tbl_doc_sp_relate b on a.doc_no = b.doc_no
    </select>

    <select id="findOne" resultMap="docMap" parameterType="java.lang.Long">
        select a.*,b.sp_code from tbl_doc a inner join tbl_doc_sp_relate b on a.doc_no = b.doc_no where a.id = #{id}
    </select>

    <select id="findOneByDocNo" resultMap="docMap" parameterType="java.lang.String">
        select a.*,b.sp_code from tbl_doc a inner join tbl_doc_sp_relate b on a.doc_no = b.doc_no where a.doc_no = #{docNo}
    </select>

    <select id="findOneByDocNoWithCategory" resultMap="docWithCategoryMap" parameterType="java.lang.String">
        select a.*,d.sp_code,
               b.id as doc_category_relate_id,
               b.category_id as relate_category_id,
               c.code as relate_category_code,
               b.seq as relate_seq
        from tbl_doc a inner join tbl_doc_sp_relate d on a.doc_no = d.doc_no
            left join tbl_doc_category_relate b on a.doc_no = b.doc_no
            left join tbl_doc_category c on b.category_id = c.id
        where a.doc_no = #{docNo}
    </select>

    <select id="findDocByProductCode" resultMap="docMap" parameterType="java.lang.String">
        select a.*,c.sp_code from tbl_doc a inner join tbl_doc_sp_relate c on a.doc_no = c.doc_no
        inner join tbl_doc_product_relate b on a.doc_no = b.doc_no
        where b.product_code = #{productCode}
    </select>

    <select id="findPublished" resultMap="docMap">
        select a.*,b.sp_code from tbl_doc a inner join tbl_doc_sp_relate b on a.doc_no = b.doc_no
        where a.display = 1 and a.status not in ('DRAFT', 'DELETED') and a.`visible` = 'PUBLIC'
    </select>

    <delete id="delete">
        delete from tbl_doc where id = #{id}
    </delete>

    <select id="findPublicPublishedProduct" resultMap="docMap">
        select a.*,b.sp_code from tbl_doc a inner join tbl_doc_sp_relate b on a.doc_no = b.doc_no
        where a.`status` in ('PUBLISHED','EDIT') and a.display = 1 and a.`visible` = 'PUBLIC' and a.type = 'PRODUCT'
        order by a.seq asc
    </select>

    <select id="findPublishedProductDoc" resultMap="docMap">
        select a.*,c.sp_code from tbl_doc a inner join tbl_doc_sp_relate c on a.doc_no = c.doc_no
               inner join tbl_doc_product_relate b on a.doc_no = b.doc_no
        where a.`status` in ('PUBLISHED','EDIT') and a.display = 1 and a.`type` = 'PRODUCT'
            and b.product_code = #{productCode}
        order by a.seq asc
    </select>

    <select id="findSelfProductDoc" resultMap="docMap">
        select a.*,d.sp_code from tbl_doc a inner join tbl_doc_sp_relate d on a.doc_no = d.doc_no
        where a.doc_no in (select b.doc_no from tbl_doc_product_relate b where b.product_code = #{productCode})
          and a.`type` = 'PRODUCT'
          and (select count(*) from tbl_doc_product_relate c where c.doc_no = a.doc_no) = 1
        limit 1
    </select>

    <select id="findPublishedProductDocWithCategory" resultMap="docWithCategoryMap">
        select a.*,d.sp_code,
               b.id as doc_category_relate_id,
               b.category_id as relate_category_id,
               c.code as relate_category_code,
               b.seq as relate_seq
        from tbl_doc a
            inner join tbl_doc_category_relate b on a.doc_no = b.doc_no
            inner join tbl_doc_category c on b.category_id = c.id
            inner join tbl_doc_sp_relate d on a.doc_no = d.doc_no
        where a.`status` in ('PUBLISHED','EDIT') and a.display = 1 and a.`visible` = 'PUBLIC' and a.type = 'PRODUCT'
    </select>
    <select id="findPublishedProductDocWithCategoryAndCode"
            resultMap="docWithCategoryMap">
        select a.*,d.sp_code,
        b.id as doc_category_relate_id,
        b.category_id as relate_category_id,
        c.code as relate_category_code,
               b.seq  as relate_seq,
               e.code as relate_parent_category_name
        from tbl_doc a
        inner join tbl_doc_category_relate b on a.doc_no = b.doc_no
        inner join tbl_doc_category c on b.category_id = c.id
        inner join tbl_doc_sp_relate d on a.doc_no = d.doc_no
        inner join tbl_doc_category e on e.id = c.pid
        where a.`status` in ('PUBLISHED','EDIT')
        and a.display = 1
        and a.`visible` = 'PUBLIC'
        and a.type = 'PRODUCT'
          and e.scope = #{scope}
    </select>

    <delete id="deleteByDocNo">
        delete from tbl_doc where doc_no = #{docNo}
    </delete>

</mapper>
