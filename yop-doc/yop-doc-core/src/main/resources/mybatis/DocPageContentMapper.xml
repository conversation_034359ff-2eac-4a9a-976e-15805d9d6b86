<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yeepay.g3.core.yop.doc.repository.mysql.doc.DocPageContentRepository">

    <resultMap type="com.yeepay.g3.core.yop.doc.entity.v2.DocPageContentEntity" id="docPageContentMap">
        <id property="id" column="id"/>
        <result property="pageId" column="page_id"/>
        <result property="content" column="content"/>
    </resultMap>

    <insert id="save" parameterType="com.yeepay.g3.core.yop.doc.entity.v2.DocPageContentEntity" keyProperty="id" useGeneratedKeys="true">
        insert into tbl_doc_page_content(page_id, content)
            values (#{pageId}, #{content})
    </insert>

    <insert id="batchSave" parameterType="java.util.List">
        insert into tbl_doc_page_content(page_id, content)
            values
            <foreach collection="list" item="item" separator=",">
                (#{item.pageId}, #{item.content})
            </foreach>
    </insert>

    <update id="update" parameterType="com.yeepay.g3.core.yop.doc.entity.v2.DocPageContentEntity">
        update tbl_doc_page_content set content = #{content}
        where id = #{id}
    </update>

    <select id="checkByPageId" resultMap="docPageContentMap">
        select id,page_id from tbl_doc_page_content where page_id = #{pageId}
    </select>

    <delete id="batchDeleteByPageId" parameterType="java.util.List">
        delete from tbl_doc_page_content
        where page_id in
        <foreach collection="list" item="item" separator="," open="(" close=")">
            #{item}
        </foreach>
    </delete>

    <update id="updateContentByPageId" parameterType="java.util.Map">
        update tbl_doc_page_content set content = #{content}
        where page_id = #{pageId}
    </update>

    <update id="replaceAll" parameterType="java.util.Map">
        update tbl_doc_page_content a set a.content = replace(a.content, #{source}, #{target})
        <if test="null != docNo">
            where a.page_id in (select c.id from tbl_doc b inner join tbl_doc_page c on b.doc_no = c.doc_no and b.doc_no = #{docNo})
        </if>
    </update>

    <delete id="deleteByDocNo">
        delete from tbl_doc_page_content where page_id in (select id from tbl_doc_page where doc_no = #{docNo})
    </delete>

</mapper>
