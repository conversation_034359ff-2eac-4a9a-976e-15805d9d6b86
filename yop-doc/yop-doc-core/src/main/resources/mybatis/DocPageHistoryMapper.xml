<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yeepay.g3.core.yop.doc.repository.mysql.doc.DocPageHistoryRepository">

    <resultMap type="com.yeepay.g3.core.yop.doc.entity.v2.DocPageHistoryEntity" id="docPageHistoryMap">
        <id property="id" column="id"/>
        <result property="docNo" column="doc_no"/>
        <result property="pageNo" column="page_no"/>
        <result property="pageId" column="page_id"/>
        <result property="pagePid" column="page_pid"/>
        <result property="pageType" column="page_type"/>
        <result property="title" column="title"/>
        <result property="content" column="content"/>
        <result property="operType" column="oper_type"/>
        <result property="operator" column="operator"/>
        <result property="version" column="version"/>
        <result property="docVersion" column="doc_version"/>
        <result property="createdDate" column="created_datetime"/>
    </resultMap>

    <insert id="save" parameterType="com.yeepay.g3.core.yop.doc.entity.v2.DocPageHistoryEntity" keyProperty="id" useGeneratedKeys="true">
        insert into tbl_doc_page_history(`doc_no`, page_no, page_pid, `title`, page_id, oper_type, operator, content, `version`, doc_version, created_datetime)
            values (#{docNo}, #{pageNo}, #{pagePid}, #{title}, #{pageId}, #{operType}, #{operator}, #{content}, #{version}, #{docVersion}, CURRENT_TIMESTAMP)
    </insert>

    <insert id="batchSave" parameterType="java.util.List">
        insert into tbl_doc_page_history(`doc_no`, page_no, page_pid, `title`, page_id, oper_type, operator, content, `version`, doc_version, created_datetime)
            values
                <foreach collection="list" item="item" separator=",">
                    (#{item.docNo}, #{item.pageNo}, #{item.pagePid}, #{item.title}, #{item.pageId},#{item.operType}, #{item.operator}, #{item.content}, #{item.version}, #{item.docVersion}, CURRENT_TIMESTAMP)
                </foreach>
    </insert>

    <select id="findOneWithContent" resultMap="docPageHistoryMap">
        select ph.*,p.page_type from tbl_doc_page_history ph inner join tbl_doc_page p on ph.page_id = p.id
        where ph.id = #{id}
    </select>

    <select id="findOneWithContentByDocVersion" resultMap="docPageHistoryMap">
        select ph.*,p.page_type
        from tbl_doc_page_history ph inner join tbl_doc_page p on ph.page_id = p.id
        where ph.doc_no = #{docNo}
          and ph.page_no = #{pageNo}
          and ph.doc_version = #{docVersion}
    </select>

    <select id="findOne" resultMap="docPageHistoryMap">
        select ph.id, ph.doc_no, ph.page_no, ph.page_pid, ph.title, ph.oper_type, ph.page_id, ph.operator, ph.`version`, ph.doc_version, ph.created_datetime, p.page_type
        from tbl_doc_page_history ph inner join tbl_doc_page p on ph.page_id = p.id
        where ph.id = #{id}
    </select>

    <select id="findByDocNoAndDocVersion" resultMap="docPageHistoryMap">
        select ph.*,p.page_type
        from tbl_doc_page_history ph inner join tbl_doc_page p on ph.page_id = p.id
        where ph.doc_no = #{docNo}
          and ph.doc_version = #{docVersion}
    </select>

    <select id="findByDocNo" resultMap="docPageHistoryMap">
        select ph.*,p.page_type
        from tbl_doc_page_history ph inner join tbl_doc_page p on ph.page_id = p.id
        where ph.doc_no = #{docNo}
    </select>

    <update id="updateContentById" parameterType="java.util.Map">
        update tbl_doc_page_history set content = #{content}
        where id = #{id}
    </update>

    <delete id="deleteByDocNo">
        delete from tbl_doc_page_history where doc_no = #{docNo}
    </delete>

    <delete id="batchDeleteByPageId" parameterType="java.util.List">
        delete from tbl_doc_page_history
        where page_id in
        <foreach collection="list" item="item" separator="," open="(" close=")">
            #{item}
        </foreach>
    </delete>

</mapper>
