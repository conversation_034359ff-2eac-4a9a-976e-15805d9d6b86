<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yeepay.g3.core.yop.doc.repository.mysql.doc.DocPageRepository">

    <resultMap type="com.yeepay.g3.core.yop.doc.entity.v2.DocPageEntity" id="docPageMap">
        <id property="id" column="id"/>
        <result property="docNo" column="doc_no"/>
        <result property="pageNo" column="page_no"/>
        <result property="parentPageNo" column="parent_page_no"/>
        <result property="pid" column="pid"/>
        <result property="categoryId" column="category_id"/>
        <result property="title" column="title"/>
        <result property="pageType" column="page_type"/>
        <result property="pageVisible" column="page_visible"/>
        <result property="depth" column="depth"/>
        <result property="seq" column="seq"/>
        <result property="pageControl" column="page_control"/>
        <result property="display" column="display"/>
        <result property="content" column="content"/>
        <result property="refId" column="ref_id"/>
        <result property="refUrl" column="ref_url"/>
        <result property="version" column="version"/>
        <result property="createdDate" column="created_datetime"/>
        <result property="lastModifiedDate" column="last_modified_datetime"/>
        <association property="category" javaType="com.yeepay.g3.facade.yop.doc.dto.DocCategory">
            <id property="id" column="category_id"/>
            <result property="pid" column="category_pid"/>
            <result property="code" column="category_code"/>
            <result property="name" column="category_name"/>
            <result property="desc" column="category_desc"/>
            <result property="seq" column="category_seq"/>
        </association>
    </resultMap>

    <insert id="save" parameterType="com.yeepay.g3.core.yop.doc.entity.v2.DocPageEntity" keyProperty="id" useGeneratedKeys="true">
        insert into tbl_doc_page(`doc_no`, page_no, pid, category_id, `title`, page_type, `page_visible`, depth, `seq`, `page_control`,`display`, ref_id, ref_url, created_datetime, last_modified_datetime, version)
            values (#{docNo}, #{pageNo}, #{pid}, #{categoryId}, #{title}, #{pageType}, #{pageVisible}, #{depth}, #{seq},  #{pageControl},#{display}, #{refId}, #{refUrl}, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, 0)
    </insert>

    <insert id="saveWithId" parameterType="com.yeepay.g3.core.yop.doc.entity.v2.DocPageEntity" keyProperty="id" useGeneratedKeys="true">
        insert into tbl_doc_page(id, `doc_no`, page_no, pid, `title`, page_type, `page_visible`, depth, `seq`, `page_control`,`display`, ref_id, ref_url, created_datetime, last_modified_datetime, version)
            values (#{id}, #{docNo}, #{pageNo}, #{pid}, #{title}, #{pageType}, #{pageVisible}, #{depth}, #{seq},  #{pageControl},#{display}, #{refId}, #{refUrl}, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, 0)
    </insert>

    <insert id="batchSave" parameterType="java.util.List">
        insert into tbl_doc_page(`doc_no`, page_no, pid, `title`, page_type, `page_visible`, depth, `seq`, `page_control`, `display`, ref_id, ref_url, created_datetime, last_modified_datetime, version)
            values
            <foreach collection="list" item="item" separator="," >
                (#{item.docNo}, #{item.pageNo}, #{item.pid}, #{item.title}, #{item.pageType}, #{item.pageVisible}, #{item.depth}, #{item.seq}, #{item.pageControl},#{item.display},#{item.refId}, #{item.refUrl}, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, 0)
            </foreach>
    </insert>

    <select id="findByExample" resultMap="docPageMap" parameterType="com.yeepay.g3.core.yop.doc.entity.example.DocPageExample">
        select a.*
             <if test="withParentPageNo">
                 ,ifnull(b.page_no, 'root') as parent_page_no
             </if>
             <if test="withContent">
                 , c.content
             </if>
             <if test="withCategory">
                 , d.pid as category_pid, d.code as category_code,d.name as category_name, d.desc as category_desc, d.seq as category_seq
             </if>
            from tbl_doc_page a
            <if test="withParentPageNo">
                left join tbl_doc_page b on a.pid = b.id
            </if>
            <if test="withContent">
                left join tbl_doc_page_content c on a.id = c.page_id
            </if>
            <if test="withCategory">
                left join tbl_doc_category d on d.id = a.category_id
            </if>
        where 1 = 1
            <if test="null != id">
                and a.id = #{id}
            </if>
            <if test="null != docNo">
                and a.doc_no = #{docNo}
            </if>
            <if test="null != pageNo">
                and a.page_no = #{pageNo}
            </if>
            <if test="null != pid">
                and a.pid = #{pid}
            </if>
            <if test="null != pageType">
                and a.page_type = #{pageType}
            </if>
            <if test="null != display">
                and a.display = #{display}
            </if>
            <if test="null != visibles and visibles.size != 0">
                and a.page_visible in
                <foreach collection="visibles" item="item" open="(" close=")" separator=",">
                    #{item}
                </foreach>
            </if>
            <if test="null != refIds and refIds.size != 0">
                and a.ref_id in
                <foreach collection="refIds" item="item" open="(" close=")" separator=",">
                    #{item}
                </foreach>
            </if>
            <if test="null != pageNos and pageNos.size != 0">
                and a.page_no in
                <foreach collection="pageNos" item="item" open="(" close=")" separator=",">
                    #{item}
                </foreach>
            </if>
        order by a.`seq` asc
    </select>

    <select id="findPublishedWithContent" resultMap="docPageMap">
        select dp.*, dpc.content
        from tbl_doc_page_history dpc
                 left join tbl_doc_page dp on dpc.page_id = dp.id
        where dpc.doc_no = #{docNo}
          and dpc.doc_version = #{docVersion}
        order by dp.`seq` asc
    </select>

    <select id="findLastSonByPid" resultMap="docPageMap">
        select a.* from tbl_doc_page a
        where a.pid = #{pid}
        order by a.`seq` desc limit 1
    </select>

    <delete id="batchDeleteById" parameterType="java.util.List">
        delete from tbl_doc_page
        where id in
        <foreach collection="list" item="item" separator="," open="(" close=")">
            #{item}
        </foreach>
    </delete>

    <update id="update" parameterType="com.yeepay.g3.core.yop.doc.entity.v2.DocPageEntity">
        UPDATE tbl_doc_page
        <set>
            <if test="null != pageNo and '' != pageNo">
                page_no = #{pageNo},
            </if>
            <if test="null != pid">
                pid = #{pid},
            </if>
            <if test="null != title and '' != title">
                title = #{title},
            </if>
            <if test="null != pageVisible">
                page_visible = #{pageVisible},
            </if>
            <if test="null != depth">
                depth = #{depth},
            </if>
            <if test="null != seq">
                seq = #{seq},
            </if>
            <if test="null != display">
                display = #{display},
            </if>
            ref_id = #{refId}, ref_url = #{refUrl}, version = version + 1, last_modified_datetime = CURRENT_TIMESTAMP
        </set>
        WHERE id = #{id} AND version = #{version}
    </update>

    <update id="updateLocation" parameterType="com.yeepay.g3.core.yop.doc.entity.v2.DocPageEntity">
        update tbl_doc_page
            <set>
                <if test="null != seq">
                    seq = #{seq},
                </if>
                <if test="null != pid">
                    pid = #{pid},
                </if>
                <if test="null != depth">
                    depth = #{depth}
                </if>
            </set>
        where id = #{id}
    </update>

    <select id="findPreSibling" resultMap="docPageMap">
        select b.* from tbl_doc_page a
            inner join tbl_doc_page b on a.pid = b.pid and ifnull(a.category_id, 0) = ifnull(b.category_id, 0)
        where a.id = #{id} and a.seq > b.seq order by b.`seq` desc limit 1
    </select>

    <select id="findNextSibling" resultMap="docPageMap">
        select b.* from tbl_doc_page a
            inner join tbl_doc_page b on a.pid = b.pid and ifnull(a.category_id, 0) = ifnull(b.category_id, 0)
        where a.id = #{id} and b.seq > a.seq order by b.`seq` asc limit 1
    </select>

    <select id="findTailSiblings" resultMap="docPageMap">
        select a.* from tbl_doc_page a
        where a.pid = #{pid} and
              <if test="null == categoryId">
                  a.category_id is null
              </if>
              <if test="null != categoryId">
                  a.category_id = #{categoryId}
              </if>
              and a.`seq` >= #{seq}
        order by a.`seq` asc
    </select>

    <update id="batchUpdateSeqById" parameterType="java.util.List">
        <foreach collection="list" item="item">
            update tbl_doc_page
            <set>
                <if test="null != item.seq">`seq` = #{item.seq},</if>
                <if test="null != item.title and '' != item.title">
                    title = #{item.title},
                </if>
                <if test="null != item.pageControl">
                    page_control = #{item.pageControl}
                </if>
            </set>
            where id = #{item.id};
        </foreach>
    </update>

    <update id="batchUpdateVisible" parameterType="java.util.List">
        <foreach collection="list" item="item">
            update tbl_doc_page
            <set>
                <if test="null != item.display">
                    `display` = #{item.display},
                </if>
                <if test="null != item.pageVisible">
                    page_visible = #{item.pageVisible}
                </if>
            </set>
            where id = #{item.id};
        </foreach>
    </update>

    <delete id="batchDeleteDirectSons">
        delete from tbl_doc_page where pid = #{pid}
    </delete>

    <update id="updateVisible" parameterType="com.yeepay.g3.core.yop.doc.entity.v2.DocPageEntity">
        update tbl_doc_page
        <set>
            <if test="null != pageVisible">
                page_visible = #{pageVisible},
            </if>
            <if test="null != display">
                display = #{display}
            </if>
        </set>
        where id = #{id}
    </update>

    <update id="batchRemoveRefsById" parameterType="java.util.List">
        update tbl_doc_page set ref_id = null
        where id in
        <foreach collection="list" item="item" separator="," open="(" close=")">
            #{item}
        </foreach>
    </update>

    <update id="batchUpdatePageControl" parameterType="java.util.Map">
        update tbl_doc_page
        set page_control = #{pageControl}
        where id <foreach collection="ids" item="item" open="in (" close=")" separator=",">
        #{item}
    </foreach>
    </update>

    <update id="batchUpdatePageSeq" parameterType="java.util.Map">
        update tbl_doc_page
        set seq = #{seq}
        where id <foreach collection="ids" item="item" open="in (" close=")" separator=",">
        #{item}
    </foreach>
    </update>

    <select id="existsCategoryRef" resultType="java.lang.Long">
        select count(*) from tbl_doc_page a
        where a.category_id = #{categoryId}
    </select>

    <update id="batchUpdateApiCategory" parameterType="java.util.Map">
        update tbl_doc_page
        set category_id = #{categoryId}
        where id <foreach collection="ids" item="item" open="in (" close=")" separator=",">
        #{item}
      </foreach>
    </update>

    <delete id="deleteByDocNo">
        delete from tbl_doc_page where doc_no = #{docNo}
    </delete>

    <select id="findByPageNoPath" resultMap="docPageMap">
        SELECT p.*, pc.content
        FROM tbl_doc_page p
        INNER JOIN (
            SELECT p1.id
            FROM tbl_doc_page p1
            LEFT JOIN tbl_doc_page p2 ON p1.pid = p2.id
            LEFT JOIN tbl_doc_page p3 ON p2.pid = p3.id
            WHERE p1.page_no = #{currentPageNo}
              AND p1.doc_no = #{docNo}
              <choose>
                <when test="parentPageNo != null and parentPageNo != ''">
                  AND p2.page_no = #{parentPageNo}
                </when>
                <otherwise>
                  AND p2.id is null
                </otherwise>
              </choose>
              <choose>
                <when test="grandPageNo != null and grandPageNo != ''">
                  AND p3.page_no = #{grandPageNo}
                </when>
                <otherwise>
                  AND p3.id is null
                </otherwise>
              </choose>
        ) t ON t.id = p.id
        INNER JOIN tbl_doc_page_content pc ON pc.page_id = p.id
    </select>

</mapper>
