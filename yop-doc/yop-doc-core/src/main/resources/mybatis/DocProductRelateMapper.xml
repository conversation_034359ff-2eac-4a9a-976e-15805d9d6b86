<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yeepay.g3.core.yop.doc.repository.mysql.doc.DocProductRelateRepository">

    <resultMap type="com.yeepay.g3.core.yop.doc.entity.DocProductRelateEntity" id="docProductRelateMap">
        <id property="id" column="id"/>
        <result property="docNo" column="doc_no"/>
        <result property="productCode" column="product_code"/>
        <result property="createdDate" column="created_datetime"/>
    </resultMap>

    <insert id="batchSave" parameterType="java.util.List">
        insert into tbl_doc_product_relate(doc_no, product_code, created_datetime) values
        <foreach collection="list" item="item" separator=",">
            (#{item.docNo}, #{item.productCode}, CURRENT_TIMESTAMP)
        </foreach>
    </insert>

    <select id="findByDocNo" resultMap="docProductRelateMap">
        select * from tbl_doc_product_relate
        where doc_no = #{docNo} order by created_datetime asc
    </select>

    <delete id="batchDelete" parameterType="java.util.List">
        delete from tbl_doc_product_relate where id in
        <foreach collection="list" item="item" separator="," open="(" close=")">
            #{item.id}
        </foreach>
    </delete>

    <delete id="deleteByDocNo">
        delete from tbl_doc_product_relate where doc_no = #{docNo}
    </delete>

    <select id="findByProductCodes" parameterType="java.util.List" resultMap="docProductRelateMap">
        select *
        from tbl_doc_product_relate
        where product_code in
        <foreach collection="dockingProductCodes" item="item" separator="," open="(" close=")">
            #{item}
        </foreach>
        order by created_datetime asc
    </select>

</mapper>