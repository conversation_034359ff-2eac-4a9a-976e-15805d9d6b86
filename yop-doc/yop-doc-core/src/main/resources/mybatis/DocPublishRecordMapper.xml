<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yeepay.g3.core.yop.doc.repository.mysql.doc.DocPublishRecordRepository">

    <resultMap type="com.yeepay.g3.core.yop.doc.entity.v2.DocPublishRecordEntity" id="docPublishRecordMap">
        <id property="id" column="id"/>
        <result property="docNo" column="doc_no"/>
        <result property="docTitle" column="doc_title"/>
        <result property="docVersion" column="doc_version"/>
        <result property="operType" column="oper_type"/>
        <result property="operator" column="operator"/>
        <result property="createdDate" column="created_datetime"/>
    </resultMap>

    <insert id="save" parameterType="com.yeepay.g3.core.yop.doc.entity.v2.DocPublishRecordEntity">
        insert into tbl_doc_publish_record(doc_no,doc_title,doc_version,oper_type,operator,created_datetime)
            values (#{docNo}, #{docTitle}, #{docVersion}, #{operType}, #{operator}, CURRENT_TIMESTAMP)
    </insert>

    <select id="findByExample" resultMap="docPublishRecordMap" parameterType="com.yeepay.g3.core.yop.doc.entity.example.DocPublishRecordExample">
        select * from tbl_doc_publish_record
        <where>
            <if test="null != id">id = #{id}</if>
            <if test="null != docNo">and doc_no = #{docNo}</if>
            <if test="null != docTitle">and doc_title = #{docTitle}</if>
            <if test="null != docVersion">and doc_version = #{docVersion}</if>
            <if test="null != operator">and operator = #{operator}</if>
        </where>
    </select>

    <delete id="batchDeleteById" parameterType="java.util.List">
        DELETE FROM tbl_doc_publish_record WHERE id IN
        <foreach collection="list" open="(" item="item" separator="," close=")">
            #{item}
        </foreach>
    </delete>

    <delete id="deleteByDocNo">
        delete from tbl_doc_publish_record where doc_no = #{docNo}
    </delete>

</mapper>