<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yeepay.g3.core.yop.doc.repository.mysql.doc.DocSpRepository">

    <insert id="batchSave">
        insert into tbl_doc_sp_relate(`doc_no`, `sp_code`) values
        <foreach collection="spCodes" item="item" separator=",">
            (#{docNo}, #{item})
        </foreach>
    </insert>

    <delete id="deleteByDocNo">
        delete from tbl_doc_sp_relate where doc_no = #{docNo}
    </delete>

</mapper>
