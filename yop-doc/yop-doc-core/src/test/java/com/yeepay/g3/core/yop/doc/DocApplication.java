package com.yeepay.g3.core.yop.doc;

import com.yeepay.boot.components.config.utils.ConfigUtils;
import com.yeepay.g3.utils.rmi.RemoteServiceFactory;
import com.yeepay.springframework.boot.annotation.EnableSoa;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.actuate.autoconfigure.metrics.data.RepositoryMetricsAutoConfiguration;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.autoconfigure.amqp.RabbitAutoConfiguration;
import org.springframework.boot.autoconfigure.data.web.SpringDataWebAutoConfiguration;
import org.springframework.boot.autoconfigure.jdbc.DataSourceAutoConfiguration;
import org.springframework.boot.web.servlet.ServletComponentScan;
import org.springframework.context.annotation.ImportResource;
import org.springframework.context.annotation.PropertySource;

/**
 * title: <br/>
 * description: <br/>
 * Copyright: Copyright (c) 2016<br/>
 * Company: 易宝支付(YeePay)<br/>
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 18/3/16 19:27
 */
@ServletComponentScan("com.yeepay.g3.core.yop.*.config")
@SpringBootApplication(exclude = {
        DataSourceAutoConfiguration.class,
        // spring-data版本太低
        RepositoryMetricsAutoConfiguration.class,
        SpringDataWebAutoConfiguration.class,
        RabbitAutoConfiguration.class
}, scanBasePackages = {
        "com.yeepay.g3.core.yop.doc",
        "com.yeepay.janus.infrastructure"
})
@ImportResource(locations = {"classpath:yop-doc-core-spring/applicationContext-yop-doc-core.xml",})
@PropertySource(value = {"classpath:runtimecfg/redis.properties"})
@EnableSoa
public class DocApplication {

    public static void main(String[] args) throws Exception {
        try {
            ConfigUtils.init();
        } catch (Exception e) {
            throw new RuntimeException("init config error", e);
        }
        SpringApplication.run(DocApplication.class, args);
    }

}
