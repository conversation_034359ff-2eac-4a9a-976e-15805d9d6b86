/*
 * Copyright: Copyright (c)2011
 * Company: 易宝支付(YeePay)
 */

package com.yeepay.g3.core.yop.doc;

import com.google.gson.Gson;

/**
 * title: <br>
 * description: 描述<br>
 * Copyright: Copyright (c)2014<br>
 * Company: 易宝支付(YeePay)<br>
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2025/1/13
 */
public class LocalTest {

    public static void main(String[] args) {
        System.out.println(new Gson().fromJson("{\"schemaVersion\":1,\"version\":\"1.0.0\",\"type\":\"data_connector\",\"extraData\":{\"disabledPeriodicSync\":false,\"dataSourceConfigUiUri\":\"https://ext.baseopendev.com/ext/data-sync-fe-demo/c70fa2864a002386423f26411f21a3c674bc2f9c/index.html\",\"initHeight\":300,\"initWidth\":520},\"protocol\":{\"type\":\"http\",\"httpProtocol\":{\"uris\":[{\"type\":\"tableMeta\",\"uri\":\"/api/table-meta\"},{\"type\":\"records\",\"uri\":\"/api/records\"}]}}}", Object.class));
    }
}
