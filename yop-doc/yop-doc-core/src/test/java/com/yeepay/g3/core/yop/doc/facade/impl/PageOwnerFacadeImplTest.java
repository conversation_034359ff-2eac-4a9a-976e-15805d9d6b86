package com.yeepay.g3.core.yop.doc.facade.impl;

import com.yeepay.g3.core.yop.doc.service.PageOwnerService;
import com.yeepay.g3.facade.yop.sys.dto.OperatorDTO;
import com.yeepay.janus.facade.dto.PageOwnerUpdateDTO;
import com.yeepay.janus.facade.dto.data.OperDTO;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.ArgumentCaptor;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;

import java.util.Arrays;
import java.util.Collections;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;

class PageOwnerFacadeImplTest {
    @Mock
    private PageOwnerService pageOwnerService;

    @InjectMocks
    private PageOwnerFacadeImpl pageOwnerFacade;

    @BeforeEach
    void setUp() {
        MockitoAnnotations.openMocks(this);
    }

    @Test
    void testUpdatePageOwners_Normal() {
        PageOwnerUpdateDTO dto = new PageOwnerUpdateDTO();
        dto.setPageId(123L);
        dto.setOwnerIds(Arrays.asList("u1", "u2"));
        OperDTO oper = new OperDTO();
        oper.setOperator("admin");
        dto.setOperDTO(oper);

        pageOwnerFacade.updatePageOwners(dto);

        ArgumentCaptor<Long> pageIdCaptor = ArgumentCaptor.forClass(Long.class);
        ArgumentCaptor<List<String>> ownerIdsCaptor = ArgumentCaptor.forClass(List.class);
        ArgumentCaptor<List<String>> ownerNamesCaptor = ArgumentCaptor.forClass(List.class);
        ArgumentCaptor<String> operatorCaptor = ArgumentCaptor.forClass(String.class);
        verify(pageOwnerService).setPageOwners(pageIdCaptor.capture(), ownerIdsCaptor.capture(), ownerNamesCaptor.capture(), operatorCaptor.capture());
        assertEquals(123L, pageIdCaptor.getValue());
        assertEquals(Arrays.asList("u1", "u2"), ownerIdsCaptor.getValue());
        assertEquals(Arrays.asList("u1", "u2"), ownerNamesCaptor.getValue());
        assertEquals("admin", operatorCaptor.getValue());
    }

    @Test
    void testUpdatePageOwners_ClearOwners() {
        PageOwnerUpdateDTO dto = new PageOwnerUpdateDTO();
        dto.setPageId(456L);
        dto.setOwnerIds(Collections.emptyList());
        dto.setOperDTO(null);

        pageOwnerFacade.updatePageOwners(dto);
        verify(pageOwnerService).setPageOwners(eq(456L), eq(Collections.emptyList()), eq(Collections.emptyList()), eq("system"));
    }

    @Test
    void testUpdatePageOwners_InvalidPageId() {
        PageOwnerUpdateDTO dto = new PageOwnerUpdateDTO();
        dto.setPageId(0L);
        dto.setOwnerIds(Arrays.asList("u1"));
        dto.setOperDTO(null);
        assertThrows(IllegalArgumentException.class, () -> pageOwnerFacade.updatePageOwners(dto));
    }

    @Test
    void testUpdatePageOwners_NullDTO() {
        assertThrows(IllegalArgumentException.class, () -> pageOwnerFacade.updatePageOwners(null));
    }

    @Test
    void testUpdatePageOwners_NullOwnerId() {
        PageOwnerUpdateDTO dto = new PageOwnerUpdateDTO();
        dto.setPageId(1L);
        dto.setOwnerIds(Arrays.asList("", null));
        dto.setOperDTO(null);
        assertThrows(IllegalArgumentException.class, () -> pageOwnerFacade.updatePageOwners(dto));
    }
}

