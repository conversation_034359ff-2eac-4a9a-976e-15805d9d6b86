/*
 * Copyright: Copyright (c)2014
 * Company: 易宝支付(YeePay)
 */
package com.yeepay.g3.core.yop.doc.generator;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.common.collect.Maps;
import com.yeepay.boot.components.config.utils.ConfigUtils;
import com.yeepay.g3.core.yop.doc.dto.api.ApiDefinition;
import com.yeepay.g3.core.yop.doc.dto.api.ApiDocItem;
import com.yeepay.g3.core.yop.doc.utils.ApiMdUtils;
import com.yeepay.g3.core.yop.doc.utils.SwaggerUtil;
import com.yeepay.g3.core.yop.doc.utils.swagger.SwaggerExporter;
import com.yeepay.g3.core.yop.doc.utils.swagger.impl.ApiFoxSwaggerOptions;
import com.yeepay.g3.facade.yop.api.dto.ApiDefineDTO;
import com.yeepay.g3.facade.yop.api.enums.ApiStatusEnum;
import com.yeepay.g3.facade.yop.api.facade.OldApiMgrFacade;
import com.yeepay.g3.facade.yop.codegen.dto.ApiDefineSampleCodeGenerateRequest;
import com.yeepay.g3.facade.yop.codegen.dto.ApiSampleCodeGenerateRequest;
import com.yeepay.g3.facade.yop.codegen.dto.ApiSampleCodes;
import com.yeepay.g3.facade.yop.codegen.enumtype.LangEnum;
import com.yeepay.g3.facade.yop.codegen.enumtype.SampleCodePlusEnum;
import com.yeepay.g3.facade.yop.codegen.facade.SampleCodeGenerateFacade;
import com.yeepay.g3.facade.yop.sys.dto.*;
import com.yeepay.g3.facade.yop.sys.enums.SecurityReqTypeEnum;
import com.yeepay.g3.facade.yop.sys.enums.SpiStatusEnum;
import com.yeepay.g3.facade.yop.sys.facade.*;
import com.yeepay.g3.utils.config.ConfigurationUtils;
import com.yeepay.g3.utils.rmi.RemoteServiceFactory;
import com.yeepay.g3.utils.rmi.RemotingProtocol;
import io.swagger.v3.core.util.Json;
import io.swagger.v3.oas.models.OpenAPI;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.junit.Before;
import org.junit.Test;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.IOException;
import java.util.*;
import java.util.stream.Collectors;

/**
 * title: <br>
 * description: 描述<br>
 * Copyright: Copyright (c)2014<br>
 * Company: 易宝支付(YeePay)<br>
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2023/3/24
 */
public class ApiDefinitionGeneratorTest {
    private static final Logger LOGGER = LoggerFactory.getLogger(ApiDefinitionGeneratorTest.class);

    private OldApiMgrFacade oldApiMgrFacade;
    private ApiGroupFacade apiGroupFacade;
    private ApiMgrFacade apiMgrFacade;
    private ApiPublishQueryFacade apiPublishQueryFacade;
    private SpiQueryFacade spiQueryFacade;
    private SecurityReqQueryFacade securityReqQueryFacade;
    private SecurityReqMgrFacade securityReqMgrFacade;
    private ModelQueryFacade modelQueryFacade;
    private ApiErrcodeFacade apiErrcodeFacade;
    private SampleCodeGenerateFacade sampleCodeGenerateFacade;
    private ObjectMapper objectMapper = new ObjectMapper();

    @Before
    public void init() throws Exception {
        System.setProperty("dubbo.application.name", "test");
        System.setProperty("dubbo.registry.address", "zookeeperx://zk.bass.3g:2181");
        ConfigUtils.init();
        ConfigurationUtils.init();
        RemoteServiceFactory.init();
        oldApiMgrFacade = RemoteServiceFactory.getService(
//                "http://localhost:8081/hessian", RemotingProtocol.HESSIAN,
                "http://ycenc.yeepay.com:30290/hessian", RemotingProtocol.HESSIAN,
                OldApiMgrFacade.class);

        apiGroupFacade = RemoteServiceFactory.getService(
//                "http://localhost:8081/hessian", RemotingProtocol.HESSIAN,
                "http://ycenc.yeepay.com:30290/hessian", RemotingProtocol.HESSIAN,
                ApiGroupFacade.class);

        apiMgrFacade = RemoteServiceFactory.getService(
//                "http://localhost:8081/hessian", RemotingProtocol.HESSIAN,
                "http://ycenc.yeepay.com:30290/hessian", RemotingProtocol.HESSIAN,
                ApiMgrFacade.class);

        apiPublishQueryFacade = RemoteServiceFactory.getService(
//                "http://localhost:8081/hessian", RemotingProtocol.HESSIAN,
                "http://ycenc.yeepay.com:30290/hessian", RemotingProtocol.HESSIAN,
                ApiPublishQueryFacade.class);

        spiQueryFacade = RemoteServiceFactory.getService(
//                "http://localhost:8081/hessian", RemotingProtocol.HESSIAN,
                "http://ycenc.yeepay.com:30290/hessian", RemotingProtocol.HESSIAN,
                SpiQueryFacade.class);

        securityReqQueryFacade = RemoteServiceFactory.getService(
//                "http://localhost:8081/hessian", RemotingProtocol.HESSIAN,
                "http://ycenc.yeepay.com:30290/hessian", RemotingProtocol.HESSIAN,
                SecurityReqQueryFacade.class);

        securityReqMgrFacade = RemoteServiceFactory.getService(
//                "http://localhost:8081/hessian", RemotingProtocol.HESSIAN,
                "http://ycenc.yeepay.com:30290/hessian", RemotingProtocol.HESSIAN,
                SecurityReqMgrFacade.class);

        modelQueryFacade = RemoteServiceFactory.getService(
//                "http://localhost:8081/hessian", RemotingProtocol.HESSIAN,
                "http://ycenc.yeepay.com:30290/hessian", RemotingProtocol.HESSIAN,
                ModelQueryFacade.class);

        apiErrcodeFacade = RemoteServiceFactory.getService(
//                "http://localhost:8081/hessian", RemotingProtocol.HESSIAN,
                "http://ycenc.yeepay.com:30290/hessian", RemotingProtocol.HESSIAN,
                ApiErrcodeFacade.class);

        sampleCodeGenerateFacade = RemoteServiceFactory.getService(
//                "http://localhost:8081/hessian", RemotingProtocol.HESSIAN,
                "http://ycenc.yeepay.com:30837/yop-doc-center-hessian/hessian", RemotingProtocol.HESSIAN,
                SampleCodeGenerateFacade.class);
    }

    @Test
    public void testV1() throws JsonProcessingException {
        ApiDocItem api = new ApiDocItem();
        api.setApiGroup("account");
        api.setSpiCount(0);
        api.setApiId("65e8e37b885944798eac95ac1650d8b5");
        api.setDesc("");
        api.setMethod("GET");
        api.setPath("/rest/v1.0/account/balance/query");
        api.setApiVersion("V1");
        api.setLastModifiedDate(new Date());
        api.setTitle("");
//        api.setApiType("");
        final ApiDefineDTO apiDefine = oldApiMgrFacade.findApiByUri(api.getPath());
        final ApiGroupDTO apiGroup = apiGroupFacade.findByApiGroupCode(apiDefine.getApiGroup());
        ApiDefinition apiDefinition = ApiDefinition.fromV1Api(api, apiDefine, null, null, "");
        assert null != apiDefinition;
    }

    @Test
    public void testV1Remote() throws JsonProcessingException {
        String apiUri = "/rest/v2.0/mer/register/contribute/merchant";
        final ApiDefineDTO apiDefine = oldApiMgrFacade.findApiByUri(apiUri);
        final ApiGroupDTO apiGroup = apiGroupFacade.findByApiGroupCode(apiDefine.getApiGroup());
        ApiDTO apiDTO = apiMgrFacade.findByPath(apiUri);

        ApiDocItem api = new ApiDocItem();
        api.setApiGroup(apiDefine.getApiGroup());
        api.setSpiCount(0);
        api.setApiId(apiDTO.getApiId());
        api.setDesc(apiDefine.getDescription());
        api.setMethod(apiDefine.getHttpMethod()[0].name());
        api.setPath(apiUri);
        api.setApiVersion("V1");
        api.setLastModifiedDate(apiDefine.getLastModifyTime());
        api.setTitle(apiDefine.getApiTitle());
        api.setApiType(apiDefine.getApiType().getValue());

        List<SecurityReqDTO> securities = securityReqMgrFacade.findByTypeAndValue(SecurityReqTypeEnum.API, apiDefine.getApiUri());
        if (CollectionUtils.isEmpty(securities)) {
            securities = apiGroup.getSecurity();
        }

        Map<String, String> sampleCodesMap = generateSampleCode(apiGroup, apiDefine);
        ApiDefinition apiDefinition = ApiDefinition.fromV1Api(api, apiDefine, securities, sampleCodesMap, "");
        Map<String, Set<String>> rootModelNames = new HashMap<>();
        List<SpiDTO> spis = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(apiDTO.getCallbacks())) {
            apiDTO.getCallbacks().forEach(callback -> {
                SpiDTO spiDTO = spiQueryFacade.find(callback);
                if (null != spiDTO && SpiStatusEnum.ENABLED.equals(spiDTO.getBasic().getStatus())) {
                    spis.add(spiDTO);
                    spiDTO.getRequest().getRequestBody().getContents().forEach((k, v) -> {
                        try {
                            JsonNode jsonNode = objectMapper.readTree(v.getSchema());
                            String modelName = jsonNode.get("$ref").asText().replace("#/components/schemas/", "");
                            rootModelNames.computeIfAbsent(spiDTO.getBasic().getApiGroup(), p -> new HashSet<>()).add(modelName);
                        } catch (IOException e) {
                            throw new RuntimeException(e);
                        }
                    });
                }
            });
        }
        Map<String, List<String>> modelMap = new HashMap<>();
        rootModelNames.forEach((k, v) -> modelMap.put(k, new ArrayList<>(v)));
        List<ModelDTO> models = modelQueryFacade.findModelsRecursively(modelMap);
        Map<String, String> modelParamMap = new HashMap<>();
        if (CollectionUtils.isNotEmpty(models)) {
            models.forEach(model -> {
                try {
                    modelParamMap.put(model.getApiGroup() + "." + model.getName(), objectMapper.writeValueAsString(SwaggerUtil.parseModelSchema(model)));
                } catch (Exception e) {
                    e.printStackTrace();
                }
            });
        }
        List<ErrorCodeDTO> errorCodeDTOS = apiErrcodeFacade.findByApiId(apiDTO.getApiId());
        List<Map<String, String>> apiErrcodeList = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(errorCodeDTOS)) {
            errorCodeDTOS.stream()
                    .filter(apiErrcode -> StringUtils.isNotEmpty(apiErrcode.getSubErrorMsg()))
                    .forEach(apiErrcode -> {
                        Map<String, String> map = new HashMap<>();
                        map.put("subErrorCode", apiErrcode.getSubErrorCode());
                        map.put("subErrorMsg", apiErrcode.getSubErrorMsg());
                        map.put("innerSolution", apiErrcode.getInnerSolution());
                        map.put("outerSolution", apiErrcode.getOuterSolution());
                        apiErrcodeList.add(map);
                    });
        }

        List<Map<String, String>> callbacks = new ArrayList<>();
        spis.forEach(spi -> {
            Map<String, String> map = new HashMap<>();
            map.put("name", spi.getBasic().getName());
            map.put("title", spi.getBasic().getTitle());
            map.put("description", spi.getBasic().getDescription());
            map.put("referenceUrl", "https://open.yeepay.com/docs-v3/notify/" + spi.getBasic().getName() + ".md");
            callbacks.add(map);
        });
        System.out.println(new ApiMdUtils(apiDefinition, modelParamMap, apiErrcodeList, callbacks, null).convertToMarkdown());
        assert null != apiDefinition;

    }

    private Map<String, String> generateSampleCode(ApiGroupDTO apiGroup, ApiDefineDTO apiDefine) {
        Map<String, String> sampleCode = Maps.newTreeMap(SAMPLE_CODE_COMPARATOR);
        try {
            final ApiDefineSampleCodeGenerateRequest sampleCodeRequest = new ApiDefineSampleCodeGenerateRequest();
            sampleCodeRequest.setApiGroup(apiGroup);
            sampleCodeRequest.setApiDefines(Collections.singletonList(apiDefine));
            final Map<String, ApiSampleCodes> sampleGenerated = sampleCodeGenerateFacade.generate(sampleCodeRequest);
            if (MapUtils.isNotEmpty(sampleGenerated)) {
                final ApiSampleCodes apiSampleCodes = sampleGenerated.get(apiDefine.getApiUri());
                if (null != apiSampleCodes) {
                    final EnumMap<LangEnum, String> sampleCodesMap = apiSampleCodes.getSampleCodes();
                    final Map<String, String> sampleCodesPlus = apiSampleCodes.getSampleCodesPlus();
                    if (MapUtils.isNotEmpty(sampleCodesMap)) {
                        sampleCodesMap.forEach((k, v) -> sampleCode.put(k.name(), v));
                    }
                    if (MapUtils.isNotEmpty(sampleCodesPlus)) {
                        sampleCode.putAll(sampleCodesPlus);
                    }
                }
            }
        } catch (Exception e) {
            LOGGER.error("fail to generateSampleCode, apiUri:" + apiDefine.getApiUri() + "ex:", e);
        }
        return sampleCode;
    }

    @Test
    public void testV2() throws JsonProcessingException {
        OpenAPI openAPI = SwaggerUtil.getJsonMapper().readValue("{\"openapi\":\"3.0.1\",\"info\":{\"title\":\"金融业务组\",\"version\":\"1.0\"},\"security\":[{\"YOP-SM2-SM3_financial\":[]},{\"YOP-RSA2048-SHA256_financial\":[]}],\"tags\":[{\"name\":\"financial\",\"description\":\"分组\"}],\"paths\":{\"/rest/v1.0/financial/order/pay/query\":{\"get\":{\"tags\":[\"financial\"],\"summary\":\"协议支付2.0-支付订单查询\",\"description\":\"查询支付订单信息\",\"operationId\":\"order_pay_query_v1\",\"parameters\":[{\"name\":\"orderId\",\"in\":\"query\",\"description\":\"<p>商户请求号</p>\",\"required\":true,\"schema\":{\"exclusiveMaximum\":true,\"exclusiveMinimum\":true,\"maxLength\":64,\"minLength\":1,\"type\":\"string\",\"example\":\"210107104428249784269\"}},{\"name\":\"merchantNo\",\"in\":\"query\",\"description\":\"<p>收款商编</p>\",\"required\":true,\"schema\":{\"exclusiveMaximum\":true,\"exclusiveMinimum\":true,\"maxLength\":16,\"minLength\":1,\"type\":\"string\",\"example\":\"***********\"}},{\"name\":\"parentMerchantNo\",\"in\":\"query\",\"description\":\"<p>发起方商编</p>\",\"required\":true,\"schema\":{\"exclusiveMaximum\":true,\"exclusiveMinimum\":true,\"maxLength\":16,\"minLength\":1,\"type\":\"string\"}},{\"name\":\"commandType\",\"in\":\"query\",\"description\":\"<p>命令类型</p>\\n<p>DOUBLE_MERGE：合单</p>\",\"schema\":{\"exclusiveMaximum\":true,\"exclusiveMinimum\":true,\"maxLength\":32,\"minLength\":1,\"type\":\"string\",\"example\":\"DOUBLE_MERGE\"}}],\"responses\":{\"200\":{\"description\":\"返回\",\"content\":{\"application/json\":{\"schema\":{\"$ref\":\"#/components/schemas/financial.PayResponseDTO\"}}}}},\"x-yop-apigateway-api-parameter-handling\":\"PASSTHROUGH\",\"x-yop-apigateway-api-type\":\"COMMON\",\"x-yop-apigateway-auth-inherited\":true,\"x-yop-apigateway-request-encrypt\":null,\"x-yop-apigateway-response-encrypt\":null}}},\"components\":{\"schemas\":{\"financial.PayResponseDTO\":{\"required\":[\"code\",\"merchantNo\",\"message\",\"orderId\",\"payerInfo\",\"status\"],\"type\":\"object\",\"properties\":{\"code\":{\"exclusiveMaximum\":true,\"exclusiveMinimum\":true,\"type\":\"string\",\"description\":\"<p>返回码<br />详见业务错误码</p>\",\"example\":\"成功:00000\"},\"message\":{\"exclusiveMaximum\":true,\"exclusiveMinimum\":true,\"type\":\"string\",\"description\":\"<p>返回信息</p>\",\"example\":\"成功\"},\"merchantNo\":{\"exclusiveMaximum\":true,\"exclusiveMinimum\":true,\"type\":\"string\",\"description\":\"<p>收单商户编号</p>\",\"example\":\"***********\"},\"orderId\":{\"exclusiveMaximum\":true,\"exclusiveMinimum\":true,\"type\":\"string\",\"description\":\"<p>商户请求号</p>\",\"example\":\"210107104428249784269\"},\"uniqueOrderNo\":{\"exclusiveMaximum\":true,\"exclusiveMinimum\":true,\"type\":\"string\",\"description\":\"<p>易宝订单号</p>\",\"example\":\"1013202101070000001990888983\"},\"bankOrderId\":{\"exclusiveMaximum\":true,\"exclusiveMinimum\":true,\"type\":\"string\",\"description\":\"<p>银行订单号</p>\",\"example\":\"B68868695207\"},\"payerInfo\":{\"$ref\":\"#/components/schemas/financial.BankCardInfoDTO\"},\"status\":{\"exclusiveMaximum\":true,\"exclusiveMinimum\":true,\"type\":\"string\",\"description\":\"<div class=\\\"api-desc-wrap\\\">订单状态<br />可选项如下:</div>\\n<div class=\\\"api-desc-wrap\\\">ACCEPT:已接收<br />PROCESSING:处理中<br />WAIT_CONFIRM:待确认<br />FAIL:失败<br />SUCCESS:成功</div>\",\"example\":\"SUCCESS\"},\"paySuccessDate\":{\"exclusiveMaximum\":true,\"exclusiveMinimum\":true,\"type\":\"string\",\"description\":\"<p>支付成功时间</p>\",\"example\":\"2021-01-01 12:13:14\"}},\"description\":\"支付响应\"},\"financial.BankCardInfoDTO\":{\"type\":\"object\",\"properties\":{\"bankId\":{\"exclusiveMaximum\":true,\"exclusiveMinimum\":true,\"type\":\"string\",\"description\":\"<p>银行编码</p>\",\"example\":\"ICBC\"},\"cardType\":{\"exclusiveMaximum\":true,\"exclusiveMinimum\":true,\"type\":\"string\",\"description\":\"<p>卡类型<br />可选项如下:<br />DEBIT:借记卡</p>\",\"example\":\"DEBIT\"},\"bankCardNo\":{\"exclusiveMaximum\":true,\"exclusiveMinimum\":true,\"type\":\"string\",\"description\":\"<p>银行卡号(掩码返回)</p>\",\"example\":\"621483*********8888\"},\"mobilePhoneNo\":{\"exclusiveMaximum\":true,\"exclusiveMinimum\":true,\"type\":\"string\",\"description\":\"<p>手机号(掩码返回)</p>\",\"example\":\"135****1234\"},\"bindId\":{\"exclusiveMaximum\":true,\"exclusiveMinimum\":true,\"type\":\"string\",\"description\":\"<p>绑卡ID</p>\",\"example\":\"12345\"},\"bankCardNoFirst6\":{\"exclusiveMaximum\":true,\"exclusiveMinimum\":true,\"type\":\"string\",\"description\":\"<p>银行卡前6位</p>\",\"example\":\"621483\"},\"bankCardNoLast4\":{\"exclusiveMaximum\":true,\"exclusiveMinimum\":true,\"type\":\"string\",\"description\":\"<p>银行卡后4位</p>\",\"example\":\"8888\"},\"cardName\":{\"exclusiveMaximum\":true,\"exclusiveMinimum\":true,\"type\":\"string\",\"description\":\"<p>持卡人姓名(掩码返回)</p>\",\"example\":\"易*宝\"}},\"description\":\"持卡人信息\"}},\"securitySchemes\":{\"YOP-RSA2048-SHA256_financial\":{\"type\":\"apiKey\",\"name\":\"Authorization\",\"in\":\"header\",\"x-yop-apigateway-auth-type\":\"YOP-RSA2048-SHA256\"},\"YOP-SM2-SM3_financial\":{\"type\":\"apiKey\",\"name\":\"Authorization\",\"in\":\"header\",\"x-yop-apigateway-auth-type\":\"YOP-SM2-SM3\"}}},\"x-yop-apigateway-group\":\"financial\"}", OpenAPI.class);
        ApiDocItem api = new ApiDocItem();
        api.setApiGroup("financial");
        api.setSpiCount(0);
        api.setApiId("77b3708aa6244888b331cbb28ebe49a8");
        api.setDesc("");
        api.setMethod("GET");
        api.setPath("/rest/v1.0/financial/order/pay/query");
        api.setApiVersion("V2");
        api.setLastModifiedDate(new Date());
        api.setTitle("");
//        api.setApiType("");
        ApiDefinition apiDefinition = ApiDefinition.fromV2Api(api, openAPI, null, null, "");
        assert null != apiDefinition;
    }

    @Test
    public void testV2Remote() throws JsonProcessingException {
//        // /rest/v1.0/aggpay/pre-pay 聚合支付统一下单，POST FORM
//        String apiGroup = "aggpay";
//        String apiId = "59c31fc3295046118fab5bc605c65b8d";

        String apiGroup = "trade"; //查询订单 GET FORM
        String apiId = "1fe6aa427dc941cc8228fe9700a74eab";

        // /rest/v1.0/aggpay/pre-pay 聚合支付统一下单，POST JSON
//        String apiGroup = "kj";
//        String apiId = "8c619eb57da847ae87f7bf7c453b0260";
        Map<String, List<ApiDTO>> newApis = findNewApis(Arrays.asList(apiId));
        ApiDTO apiDTO = newApis.get(apiGroup).get(0);

        ApiDocItem api = new ApiDocItem();
        api.setApiGroup(apiDTO.getBasic().getApiGroup());
        api.setSpiCount(0);
        api.setApiId(apiDTO.getApiId());
        api.setDesc(apiDTO.getBasic().getDescription());
        api.setMethod(apiDTO.getRequest().getHttpMethod());
        api.setPath(apiDTO.getRequest().getPath());
        api.setApiVersion("V2");
        api.setLastModifiedDate(apiDTO.getLastModifiedDate());
        api.setTitle(apiDTO.getBasic().getTitle());
        api.setApiType(apiDTO.getBasic().getApiType().getValue());

        ApiGroupDTO apiGroupDTO = apiGroupFacade.findByApiGroupCode(apiDTO.getBasic().getApiGroup());
        Map<String, Set<String>> rootModelNames = new HashMap<>();
        List<String> apiDirectedRefModels = apiDTO.getDirectedRefModels();
        if (CollectionUtils.isNotEmpty(apiDirectedRefModels)) {
            rootModelNames.computeIfAbsent(apiDTO.getBasic().getApiGroup(), k -> new HashSet<>()).addAll(apiDirectedRefModels);
        }
        List<SpiDTO> spis = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(apiDTO.getCallbacks())) {
            apiDTO.getCallbacks().forEach(callback -> {
                SpiDTO spiDTO = spiQueryFacade.find(callback);
                if (null != spiDTO && SpiStatusEnum.ENABLED.equals(spiDTO.getBasic().getStatus())) {
                    spis.add(spiDTO);
                    spiDTO.getRequest().getRequestBody().getContents().forEach((k, v) -> {
                        try {
                            JsonNode jsonNode = objectMapper.readTree(v.getSchema());
                            String modelName = jsonNode.get("$ref").asText().replace("#/components/schemas/", "");
                            rootModelNames.computeIfAbsent(spiDTO.getBasic().getApiGroup(), p -> new HashSet<>()).add(modelName);
                        } catch (IOException e) {
                            throw new RuntimeException(e);
                        }
                    });
                }
            });
        }
        Map<String, List<String>> modelMap = new HashMap<>();
        rootModelNames.forEach((k, v) -> modelMap.put(k, new ArrayList<>(v)));
        List<ModelDTO> models = modelQueryFacade.findModelsRecursively(modelMap);

        final OpenAPI openAPI = SwaggerExporter.export(apiGroupDTO, Collections.singletonList(apiDTO), spis, null,
                models, new ApiFoxSwaggerOptions(apiDTO.getBasic().getApiGroup()));

        List<SecurityReqDTO> securities = apiDTO.getSecurity();
        if (CollectionUtils.isEmpty(securities)) {
            securities = apiGroupDTO.getSecurity();
        }

        ApiDefinition apiDefinition = ApiDefinition.fromV2Api(api, openAPI, securities,
                generateSampleCode(api, apiGroupDTO, Json.mapper().writeValueAsString(openAPI)), "");
        Map<String, String> modelParamMap = new HashMap<>();
        if (CollectionUtils.isNotEmpty(models)) {
            models.forEach(model -> {
                try {
                    modelParamMap.put(model.getApiGroup() + "." + model.getName(), objectMapper.writeValueAsString(SwaggerUtil.parseModelSchema(model)));
                } catch (Exception e) {
                    e.printStackTrace();
                }
            });
        }
        List<ErrorCodeDTO> errorCodeDTOS = apiErrcodeFacade.findByApiId(apiId);
        List<Map<String, String>> apiErrcodeList = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(errorCodeDTOS)) {
            errorCodeDTOS.stream()
                    .filter(apiErrcode -> StringUtils.isNotEmpty(apiErrcode.getSubErrorMsg()))
                    .forEach(apiErrcode -> {
                        Map<String, String> map = new HashMap<>();
                        map.put("subErrorCode", apiErrcode.getSubErrorCode());
                        map.put("subErrorMsg", apiErrcode.getSubErrorMsg());
                        map.put("innerSolution", apiErrcode.getInnerSolution());
                        map.put("outerSolution", apiErrcode.getOuterSolution());
                        apiErrcodeList.add(map);
                    });
        }

        List<Map<String, String>> callbacks = new ArrayList<>();
        spis.forEach(spi -> {
            Map<String, String> map = new HashMap<>();
            map.put("name", spi.getBasic().getName());
            map.put("title", spi.getBasic().getTitle());
            map.put("description", spi.getBasic().getDescription());
            map.put("referenceUrl", "https://open.yeepay.com/docs-v3/notify/" + spi.getBasic().getName() + ".md");
            callbacks.add(map);
        });


        System.out.println(new ApiMdUtils(apiDefinition, modelParamMap, apiErrcodeList, callbacks, null).convertToMarkdown());
        assert null != apiDefinition;
    }


    private Map<String, List<ApiDTO>> findNewApis(List<String> apiIds) {
        if (CollectionUtils.isEmpty(apiIds)) {
            return Collections.emptyMap();
        }
        Map<String, List<ApiDTO>> apisMap = apiPublishQueryFacade.batchFindLatestPublishedApi(apiIds);
        if (MapUtils.isNotEmpty(apisMap)) {
            List<ApiDTO> allApis = apisMap.values().stream()
                    .flatMap(List::stream)
                    .collect(Collectors.toList());
            fillApiSecurities(allApis);
        }
        return apisMap;
    }

    private void fillApiSecurities(List<ApiDTO> apis) {
        if (CollectionUtils.isNotEmpty(apis)) {
            Map<String, List<SecurityReqDTO>> apiSecurityMap = securityReqQueryFacade.findByValues(apis.stream()
                    .map(ApiDTO::getApiId).collect(Collectors.toList()));
            apis.forEach(api -> api.setSecurity(apiSecurityMap.get(api.getApiId())));
        }
    }

    private boolean isApiEnabled(String status) {
        return ApiStatusEnum.ACTIVE.name().equals(status) || "ONLINE".equals(status);
    }

    private static final Map<String, Integer> SAMPLE_CODE_ORDER;
    private static final Comparator<String> SAMPLE_CODE_COMPARATOR;

    static {
        SAMPLE_CODE_ORDER = Maps.newHashMapWithExpectedSize(LangEnum.values().length + SampleCodePlusEnum.values().length);
        int i = 0;
        for (LangEnum value : LangEnum.values()) {
            SAMPLE_CODE_ORDER.put(value.name(), i++);
        }
        for (SampleCodePlusEnum value : SampleCodePlusEnum.values()) {
            SAMPLE_CODE_ORDER.put(value.name(), i++);
        }
        SAMPLE_CODE_COMPARATOR = Comparator.comparingInt(o -> SAMPLE_CODE_ORDER.getOrDefault(o, Integer.MAX_VALUE));
    }

    private Map<String, String> generateSampleCode(ApiDocItem api, ApiGroupDTO apiGroup, String swagger) {
        Map<String, String> sampleCode = Maps.newTreeMap(SAMPLE_CODE_COMPARATOR);
        try {
            final ApiSampleCodeGenerateRequest request = new ApiSampleCodeGenerateRequest();
            request.setApiGroup(apiGroup);
            request.setOpenApiContent(swagger);
            final Map<String, ApiSampleCodes> sampleGenerated = sampleCodeGenerateFacade.generate(request);
            if (MapUtils.isNotEmpty(sampleGenerated)) {
                final ApiSampleCodes apiSampleCodes = sampleGenerated.get(api.getPath() + ":" + api.getMethod());
                if (null != apiSampleCodes) {
                    final EnumMap<LangEnum, String> sampleCodesMap = apiSampleCodes.getSampleCodes();
                    final Map<String, String> sampleCodesPlus = apiSampleCodes.getSampleCodesPlus();
                    if (MapUtils.isNotEmpty(sampleCodesMap)) {
                        sampleCodesMap.forEach((k, v) -> sampleCode.put(k.name(), v));
                    }
                    if (MapUtils.isNotEmpty(sampleCodesPlus)) {
                        sampleCode.putAll(sampleCodesPlus);
                    }
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        return sampleCode;
    }

}
