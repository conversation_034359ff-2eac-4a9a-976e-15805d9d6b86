package com.yeepay.g3.core.yop.doc.service;

import com.yeepay.boot.components.config.utils.ConfigUtils;
import com.yeepay.g3.core.yop.doc.service.impl.DocUrlServiceImpl;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.core.ValueOperations;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.mockito.Mockito.*;

public class DocUrlServiceTest {

    static {
        try {
            ConfigUtils.init();
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }

    @Mock
    private DocPageService docPageService;
    @Mock
    private RedisTemplate<String, Object> redisTemplate;
    @Mock
    private ValueOperations<String, Object> valueOperations;
    @InjectMocks
    private DocUrlServiceImpl docUrlService = new DocUrlServiceImpl();

    @BeforeEach
    void setUp() {
        MockitoAnnotations.openMocks(this);
        docUrlService.setDocPageService(docPageService);
        docUrlService.setRedisTemplate(redisTemplate);
        when(redisTemplate.opsForValue()).thenReturn(valueOperations);
        when(valueOperations.get(anyString())).thenReturn(null);
    }

    @Test
    public void testConvMdUrl_docs_products_hosts() {
        // /docs/products/{docNo}/{页面路径}
        String url = "https://open.yeepay.com/docs/products/fwssfk/others/5f59fc1720289f001ba82528/659cf26b4cf2f6004e026727";
        when(docPageService.findByPageNoPath(eq("fwssfk"), eq("others"), eq("5f59fc1720289f001ba82528"), eq("659cf26b4cf2f6004e026727")))
                .thenReturn(MockDocPageDTO.withId(123L));
        when(docPageService.findById(123L, false)).thenReturn(MockDocPageDTO.withId(123L));
        String result = docUrlService.convMdUrl(url);
        assertEquals("https://open.yeepay.com/docs-v3/product/fwssfk/123.md", result);
    }

    @Test
    public void testConvMdUrl_docs_products() {
        // /docs/products/{docNo}/{页面路径}
        String url = "/docs/products/fwssfk/others/5f59fc1720289f001ba82528/659cf26b4cf2f6004e026727";
        when(docPageService.findByPageNoPath(eq("fwssfk"), eq("others"), eq("5f59fc1720289f001ba82528"), eq("659cf26b4cf2f6004e026727")))
                .thenReturn(MockDocPageDTO.withId(123L));
        String result = docUrlService.convMdUrl(url);
        assertEquals("/docs-v3/product/fwssfk/123.md", result);
    }

    @Test
    public void testConvMdUrl_docs_v3_product() {
        String url = "https://open.yeepay.com/docs-v3/product/wechat-offiaccount/1334.md";
        String result = docUrlService.convMdUrl(url);
        assertEquals(url, result);
    }

    @Test
    public void testConvMdUrl_attachments() {
        String url = "https://open.yeepay.com/attachments/access?fileId=7IMyZnvPJQ";
        String result = docUrlService.convMdUrl(url);
        assertEquals(url, result);
    }

    @Test
    public void testConvMdUrl_docs_apis() {
        String url = "/docs/apis/newsqkk/options__rest__v1.0__frontcashier__bindcard__queryorderinfo";
        String result = docUrlService.convMdUrl(url);
        assertEquals("/docs-v3/api/options_rest_v1.0_frontcashier_bindcard_queryorderinfo.md", result);
    }

    @Test
    public void testConvMdUrl_docs_apis_industry() {
        String url = "/docs/apis/INDUSTRY_SOLUTION/GENERAL/ptssfk/ruwang/options__yos__v1.0__sys__merchant__qual__upload";
        String result = docUrlService.convMdUrl(url);
        assertEquals("/docs-v3/api/options_yos_v1.0_sys_merchant_qual_upload.md", result);
    }

    @Test
    public void testConvMdUrl_docs_v2_apis() {
        String url = "/docs-v2/apis/account-recharge/options__rest__v1.0__account__recharge__onlinebank__order";
        String result = docUrlService.convMdUrl(url);
        assertEquals("/docs-v3/api/options_rest_v1.0_account_recharge_onlinebank_order.md", result);
    }

    @Test
    public void testConvMdUrl_docs_v2_products_index() {
        String url = "/docs/v2/products/opr/others/5e952691a8e9ea001ac6d0ef/5e9527e8a8e9ea001ac6d0f1/index.html";
        when(docPageService.findByPageNoPath(eq("opr"), eq("others"), eq("5e952691a8e9ea001ac6d0ef"), eq("5e9527e8a8e9ea001ac6d0f1")))
                .thenReturn(MockDocPageDTO.withId(789L));
        String result = docUrlService.convMdUrl(url);
        assertEquals("/docs-v3/product/opr/789.md", result);
    }

    @Test
    public void testConvMdUrl_docs_v2_products_apis_index() {
        String url = "/docs/v2/products/opr/apis/options__rest__v1.0__sys__merchant__hmackeyquery/index.html";
        String result = docUrlService.convMdUrl(url);
        assertEquals("/docs-v3/api/options_rest_v1.0_sys_merchant_hmackeyquery.md", result);
    }

    // Mock DocPageDTO
    static class MockDocPageDTO extends com.yeepay.g3.facade.yop.doc.dto.v2.DocPageDTO {
        static MockDocPageDTO withRefId(Long refId) {
            MockDocPageDTO dto = new MockDocPageDTO();
            dto.setRefId(refId);
            return dto;
        }

        static MockDocPageDTO withId(Long id) {
            MockDocPageDTO dto = new MockDocPageDTO();
            dto.setId(id);
            return dto;
        }
    }
}
