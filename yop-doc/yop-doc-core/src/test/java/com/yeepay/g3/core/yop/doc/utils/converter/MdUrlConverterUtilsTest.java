package com.yeepay.g3.core.yop.doc.utils.converter;

import org.junit.Assert;
import org.junit.Test;

public class MdUrlConverterUtilsTest {

    static class TestUrlConverter implements UrlConverter {
        @Override
        public String convert(String originalUrl) {
            return "https://converted.com?src=" + originalUrl;
        }
    }

    @Test
    public void testMarkdownLinks() {
        String input = "这是一个[链接](http://example.com)和另一个[测试](https://test.com/path)";
        String expected = "这是一个[链接](https://converted.com?src=http://example.com)和另一个[测试](https://converted.com?src=https://test.com/path)";
        String actual = MdUrlConverterUtils.parseUrlsAndConvert(input, new TestUrlConverter());
        Assert.assertEquals(expected, actual);
    }

    @Test
    public void testHtmlLinks() {
        String input = "请访问<a href=\"http://example.com\">示例</a>和<a href=\"https://test.com/path\">测试</a>";
        String expected = "请访问<a href=\"https://converted.com?src=http://example.com\">示例</a>和<a href=\"https://converted.com?src=https://test.com/path\">测试</a>";
        String actual = MdUrlConverterUtils.parseUrlsAndConvert(input, new TestUrlConverter());
        Assert.assertEquals(expected, actual);
    }

    @Test
    public void testMixedLinks() {
        String input = "[md](http://a.com)和<a href=\"http://b.com\">html</a>";
        String expected = "[md](https://converted.com?src=http://a.com)和<a href=\"https://converted.com?src=http://b.com\">html</a>";
        String actual = MdUrlConverterUtils.parseUrlsAndConvert(input, new TestUrlConverter());
        Assert.assertEquals(expected, actual);
    }

    @Test
    public void testNoLinks() {
        String input = "没有链接的文本";
        String expected = input;
        String actual = MdUrlConverterUtils.parseUrlsAndConvert(input, new TestUrlConverter());
        Assert.assertEquals(expected, actual);
    }

    @Test
    public void testEdgeCases() {
        String input = "[空]() <a href=\"\">空</a> [正常](http://ok.com)";
        String expected = "[空]() <a href=\"\">空</a> [正常](https://converted.com?src=http://ok.com)";
        String actual = MdUrlConverterUtils.parseUrlsAndConvert(input, new TestUrlConverter());
        Assert.assertEquals(expected, actual);
    }
}

