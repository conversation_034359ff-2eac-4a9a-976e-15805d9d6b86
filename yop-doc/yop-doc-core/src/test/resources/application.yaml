spring:
  profiles:
    active: test
  application:
    name: yop-doc-hessian
  main:
    allow-bean-definition-overriding: true
    allow-circular-references: true
  servlet:
    multipart:
      max-file-size: 100MB
      max-request-size: 100MB
  data:
    redis:
      repositories:
        enabled: false
server:
  port: 30032
  tomcat:
    connection-timeout: 10000
    threads:
      max: 2000
  compression:
    enabled: true
    min-response-size: 2048
    mime-types: text/html,text/xml,text/javascript,application/javascript,text/css,text/plain
  servlet:
    context-parameters:
      soa_app_name: yop-doc-hessian
logging:
  config: classpath:log4j2.xml
yeepay:
  event:
    distribute:
      exchange: exchange.topic.yop.event.source
  rmi:
    package-name: com.yeepay.g3.facade.yop.doc.facade
    soa-mapping: /yop-doc-hessian/soa/*

