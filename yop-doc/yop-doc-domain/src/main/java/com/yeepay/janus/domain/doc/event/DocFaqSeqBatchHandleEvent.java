/*
 * Copyright: Copyright (c)2011
 * Company: 易宝支付(YeePay)
 */
package com.yeepay.janus.domain.doc.event;

import java.io.Serializable;

/**
 * title: 常见问题发布事件<br>
 * description: 描述<br>
 * Copyright: Copyright (c)2014<br>
 * Company: 易宝支付(YeePay)<br>
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2022/1/4
 */
public class DocFaqSeqBatchHandleEvent implements Serializable, PlainEvent {

    private static final long serialVersionUID = -1L;

    public static final DocFaqSeqBatchHandleEvent INSTANCE = new DocFaqSeqBatchHandleEvent();
}
