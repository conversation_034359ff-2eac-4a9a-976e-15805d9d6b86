/*
 * Copyright: Copyright (c)2011
 * Company: 易宝支付(YeePay)
 */
package com.yeepay.janus.domain.utils.config;

/**
 * title: 统一配置规范接口<br/>
 * description: <br/>
 * Copyright: Copyright (c) 2018<br/>
 * Company: 易宝支付(YeePay)<br/>
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2021/12/27 22:39
 */
public interface ConfigKey {

    /**
     * 获取 Key
     *
     * @return Key
     */
    String getKey();

    /**
     * 获取默认值
     *
     * @return 默认值
     */
    Object getDefaultValue();

}
