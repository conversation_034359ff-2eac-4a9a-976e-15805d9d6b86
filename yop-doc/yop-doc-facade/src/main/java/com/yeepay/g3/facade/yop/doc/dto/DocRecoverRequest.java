package com.yeepay.g3.facade.yop.doc.dto;

/**
 * title: <br>
 * description: 描述<br>
 * Copyright: Copyright (c)2014<br>
 * Company: 易宝支付(YeePay)<br>
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2019-10-19 11:02
 */
public class DocRecoverRequest extends BaseDTO {

    private static final long serialVersionUID = -1L;

    /**
     * 文档发布记录id
     */
    private Long id;

    /**
     * 发布/回滚人
     */
    private String operator;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getOperator() {
        return operator;
    }

    public void setOperator(String operator) {
        this.operator = operator;
    }
}
