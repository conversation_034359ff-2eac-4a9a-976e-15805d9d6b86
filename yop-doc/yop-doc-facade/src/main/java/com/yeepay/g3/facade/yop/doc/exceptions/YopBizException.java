/**
 * Copyright: Copyright (c)2011
 * Company: 易宝支付(YeePay)
 */
package com.yeepay.g3.facade.yop.doc.exceptions;

import com.yeepay.g3.common.exception.CoreBizException;

/**
 * Description: <br>
 * Copyright: Copyright (c)2011<br>
 * Company: 易宝支付(YeePay)<br>
 *
 * <AUTHOR>
 * @version 0.1, 14-11-29 9:22
 */
public final class YopBizException extends CoreBizException {

    // 00xx 系统内部异常

    public static final YopBizException GENERAL_EXCEPTION = new YopBizException("0000");

    /**
     * 静态资源相关
     */
    public static final YopBizException STATICS_RESOURCE_UPLOAD_ERROR_STATUS = new YopBizException("0001");

    /**
     * 附件上传相关
     */
    public static final YopBizException ATTACHMENT_EXCEPTION = new YopBizException("0100");

    /**
     * 页面负责人相关异常
     */
    public static final YopBizException PAGE_OWNER_EXCEPTION = new YopBizException("0200");


    private static final long serialVersionUID = 1L;

    private YopBizException(String defineCode) {
        super(defineCode);
    }

    public YopBizException newInstance(String message, Object... args) {
        YopBizException e = new YopBizException(this.defineCode);
        e.setStackTrace(getCoreStackTrace());
        e.setMessage(message, args);
        return e;
    }
}
