package com.yeepay.g3.facade.yop.doc.util;

import com.yeepay.g3.facade.yop.doc.dto.v2.DocContentBlock;
import com.yeepay.g3.facade.yop.doc.dto.v2.DocDTO;
import com.yeepay.g3.facade.yop.doc.dto.v2.DocPageDTO;
import com.yeepay.g3.facade.yop.doc.enums.v2.DocPageMetaTypeEnum;
import com.yeepay.g3.facade.yop.doc.enums.v2.DocPageTypeEnum;
import com.yeepay.g3.facade.yop.doc.enums.v2.DocTypeEnum;
import com.yeepay.g3.facade.yop.doc.enums.v2.DocVisableEnum;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.ArrayUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.*;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

/**
 * title: <br>
 * description: 描述<br>
 * Copyright: Copyright (c)2014<br>
 * Company: 易宝支付(YeePay)<br>
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2019-11-16 08:30
 */
public class DocUtils {

    private static final Logger LOGGER = LoggerFactory.getLogger(DocUtils.class);

    public static final String DEFAULT_HTTP_METHOD = "options";
    public static final int SEQ_STEP = 1 << 10;
    public static final Pattern YOP_DOC_PAGE_NO_PATTERN = Pattern.compile("^[0-9a-zA-Z_][0-9a-zA-Z_.-]*[0-9a-zA-Z]$");

    public static final Long YOP_DOC_ROOT_PAGE_ID = -1L;
    public static final String YOP_DOC_ROOT_PAGE_NO = "root";
    public static final String YOP_DOC_ROOT_PAGE_TITLE = "根页面";

    public static final String YOP_DOC_PRODUCT_PROFILE_PAGE_NO = "profile";
    public static final String YOP_DOC_PRODUCT_PROFILE_TITLE = "产品介绍";
    public static final int YOP_DOC_PRODUCT_PROFILE_PAGE_CONTROL = Integer.valueOf("0000000010", 2);//2

    public static final String YOP_DOC_PRODUCT_QUICK_START_PAGE_NO = "quick-start";
    public static final String YOP_DOC_PRODUCT_QUICK_START_TITLE = "接入指引";
    public static final int YOP_DOC_PRODUCT_QUICK_START_PAGE_CONTROL = Integer.valueOf("0000000110", 2);//6

    public static final String YOP_DOC_PRODUCT_SCENE_PAGE_NO_PREFIX = "scene";
    public static final String YOP_DOC_PRODUCT_SCENE_TITLE_FORMAT = "场景%s接入指南";
    public static final int YOP_DOC_PRODUCT_SCENE_PAGE_CONTROL = Integer.valueOf("0000000010", 2);//2

    public static final String YOP_DOC_PRODUCT_SDK_DEMO_PAGE_NO = "sdk-demo";
    public static final String YOP_DOC_PRODUCT_SDK_DEMO_TITLE = "SDK&产品DEMO";
    public static final int YOP_DOC_PRODUCT_SDK_DEMO_PAGE_CONTROL = Integer.valueOf("0000000010", 2);//2

    public static final String YOP_DOC_PRODUCT_API_PARENT_PAGENO = "apis";
    public static final String YOP_DOC_PRODUCT_API_PARENT_TITLE = "API列表";
    public static final int YOP_DOC_PRODUCT_API_PARENT_PAGE_CONTROL = Integer.valueOf("0000000000", 2);//0
    public static final int YOP_DOC_PRODUCT_API_PAGE_CONTROL = Integer.valueOf("1100001011", 2);//779

    public static final String YOP_DOC_PRODUCT_SPI_PARENT_PAGENO = "spis";
    public static final String YOP_DOC_PRODUCT_SPI_PARENT_TITLE = "商户通知";
    public static final int YOP_DOC_PRODUCT_SPI_PARENT_PAGE_CONTROL = Integer.valueOf("0000000100", 2);
    public static final int YOP_DOC_PRODUCT_SPI_PAGE_CONTROL = Integer.valueOf("1100001001", 2);//777

    public static final String YOP_DOC_PRODUCT_OTHERS_PARENT_PAGENO = "others";
    public static final String YOP_DOC_PRODUCT_OTHERS_PARENT_TITLE = "其他文档";
    public static final int YOP_DOC_PRODUCT_OTHERS_PARENT_PAGE_CONTROL = Integer.valueOf("0000000100", 2);//4

    public static final int YOP_DOC_API_CATEGORY_PAGE_CONTROL = Integer.valueOf("0011101100", 2);//236

    public static final int YOP_DOC_COMMON_PAGE_CONTROL = Integer.valueOf("0011111110", 2);//254
    public static final String YOP_DOC_EXAMPLE_DOC_NO = "example";
    public static final String YOP_DOC_EXAMPLE_PAGE_NO = "example";
    public static final String YOP_DOC_EXAMPLE_PAGE_TITLE = "示例页面";
    public static final String YOP_DOC_EXAMPLE_PAGE_CONTENT = "## 示例内容";
    public static final String PATH_SEPERATOR = "/";
    public static final String YOP_PLATFORM_DOC_NO = "platform";
    public static final String YOP_SM_PLATFORM_DOC_NO = "platform-doc";
    public static final String YOP_PRODUCTS_DOC_PREFIX = "products";
    public static final String YOP_DOC_PAGE_ACCESS_PATTERN = "/v2/%s/%s/index.html";
    public static final String YOP_DOC_WIKI_PAGE_STORE_PATTERN = "docs/%s/%s.html";
    public static final String YOP_DOC_PAGE_LOCATION_PATTERN = "/%s/%s";

    public static final String YOP_DOC_APIS_CONTENT_URI_PATTERN = "/docs/products/%s/apiCategories.json";
    public static final String YOP_DOC_SOLUTION_APIS_CONTENT_URI_PATTERN = "/docs/solutions/%s/apiCategories.json";
    public static final String YOP_DOC_PAGE_REF_CONTENT_URI_PATTERN = "/commons/doc/page/ref/content?refId=%d";
    public static final String YOP_DOC_PAGE_CONTENT_URI_PATTERN = "/%s/%s/%s/%s.html";

    public static boolean validatePageNo(String pageNo) {
        return !YOP_DOC_ROOT_PAGE_NO.equals(pageNo) && YOP_DOC_PAGE_NO_PATTERN.matcher(pageNo).matches();
    }

    public static final String getApiStorePath(String apiId) {
        return "docs/apis/" + apiId;
    }

    public static final String getSpiStorePath(String spiPageNo) {
        return "docs_spi/" + spiPageNo + ".html";
    }

    public static String generateApiPageNo(String httpMethod, String apiUri) {
        final String uriPath = apiUri.replace("/", "__");
        return StringUtils.isBlank(httpMethod) ? DEFAULT_HTTP_METHOD + uriPath : httpMethod.toLowerCase() + uriPath;
    }

    public static String[] fromApiPageNo(String apiPageNo) {
        String httpMethod = StringUtils.substringBefore(apiPageNo, "__"),
                apiUri = StringUtils.substringAfter(apiPageNo, httpMethod).replace("__", "/");
        return new String[]{httpMethod.toUpperCase(), apiUri};
    }

    public static String generateSpiPageNo(String spiName) {
        return spiName.replace(".", "__");
    }

    public static String fromSpiPageNo(String spiPageNo) {
        return spiPageNo.replace("__", ".");
    }

    public static boolean isOldApi(String pageNo) {
        return StringUtils.isNotBlank(pageNo) && StringUtils.startsWith(pageNo, DEFAULT_HTTP_METHOD);
    }

    public static boolean isApi(String pageNo) {
        boolean isApiFlag = false;
        String a = "__rest__v\\d+\\.\\d+__";
        Pattern pattern = Pattern.compile(a);
        Matcher matcher = pattern.matcher(pageNo);
        if (matcher.find()) {
            isApiFlag = true;
        }
        return isApiFlag;
    }

    /**
     * 获取页面访问路径
     * @param docNo
     * @param pagePath
     * @return
     */
    public static String pageAccessPath(String docNo, String pagePath) {
        docNo = StringUtils.trim(docNo);
        boolean isPlatform = YOP_PLATFORM_DOC_NO.equals(docNo);
        return String.format(YOP_DOC_PAGE_ACCESS_PATTERN,
                (isPlatform ? docNo : YOP_PRODUCTS_DOC_PREFIX + PATH_SEPERATOR + docNo),
                cleanPath(pagePath));
    }

    /**
     * 获取wiki页面存储路径(不带桶名)
     * @param docNo
     * @param pagePath
     * @return
     */
    public static String wikiPageStorePath(String docNo, String docVersion, String pagePath) {
        docNo = StringUtils.trim(docNo);
        boolean isPlatform = YOP_PLATFORM_DOC_NO.equals(docNo);
        return String.format(YOP_DOC_WIKI_PAGE_STORE_PATTERN,
                (isPlatform ? docNo : YOP_PRODUCTS_DOC_PREFIX + PATH_SEPERATOR + docNo) + PATH_SEPERATOR + docVersion,
                cleanPath(pagePath));
    }

    /**
     * 获取页面路径id(展示用)
     * @param docNo
     * @param pagePath
     * @return
     */
    public static String pageLocation(String docNo, String pagePath) {
        docNo = StringUtils.trim(docNo);
        return String.format(YOP_DOC_PAGE_LOCATION_PATTERN, docNo, cleanPath(pagePath));
    }

    /**
     * 去除空格、多余'/'
     * @param path
     * @return
     */
    public static String cleanPath(String path) {
        if (StringUtils.isNotEmpty(path)) {
            return Arrays.stream(path.split(PATH_SEPERATOR)).filter(StringUtils::isNotBlank).map(e -> e.replaceAll("\\s*", "")).collect(Collectors.joining(PATH_SEPERATOR));
        }
        return path;
    }


    /**
     * 解析pageNo
     * @param pagePath
     * @return [grand,parent,son]
     */
    public static String[] toPageNos(String pagePath) {
        final String[] result = new String[3];
        if (StringUtils.isNotBlank(pagePath)) {
            pagePath = cleanPath(pagePath);
            final String[] split = pagePath.split(PATH_SEPERATOR);
            if (split.length == 3) {
                return split;
            } else {
                if (split.length == 1) {
                    result[2] = split[0];
                } else {
                    result[1] = split[0];
                    result[2] = split[1];
                }
            }
        }
        return result;
    }

    /**
     * 校验页面是否是有模板
     * @param wikiPageTemplate
     * @param curPage
     * @return
     */
    public static boolean isWikiTemplatePage(Map<String, DocPageDTO> wikiPageTemplate, DocPageDTO curPage) {
        return !curPage.getDocNo().equals(YOP_PLATFORM_DOC_NO) && YOP_DOC_ROOT_PAGE_ID.equals(curPage.getPid()) && wikiPageTemplate.containsKey(curPage.getPageNo());
    }

    /**
     * 为模板页面的每个block，生成title标识
     * @param pageType
     * @param pageNo
     * @param blockTitle
     * @return
     */
    public static String blockTitleKey(DocPageTypeEnum pageType, String pageNo, String blockTitle) {
        if (pageType == DocPageTypeEnum.WIKI) {
            return pageNo + blockTitle;
        } else if (pageType == DocPageTypeEnum.API) {
            return "api" + blockTitle;
        }
        return blockTitle;
    }

    public static DocPageDTO getDocRootPage(DocDTO doc) {
        final DocPageDTO rootPage = new DocPageDTO();
        rootPage.setId(YOP_DOC_ROOT_PAGE_ID);
        rootPage.setDocNo(doc.getDocNo());
        rootPage.setPageNo(YOP_DOC_ROOT_PAGE_NO);
        rootPage.setTitle(YOP_DOC_ROOT_PAGE_TITLE);
        rootPage.setPageVisible(doc.getVisible());
        rootPage.setDisplay(doc.getDisplay());
        if (!DocUtils.isTemplateDoc(doc.getType())) {
            rootPage.setPageControl(YOP_DOC_COMMON_PAGE_CONTROL);
        } else {
            rootPage.setPageControl(0);
        }
        rootPage.setDepth(0);
        rootPage.setSeq(0);
        rootPage.setPid(0L);
        rootPage.setPageType(DocPageTypeEnum.WIKI);
        return rootPage;
    }

    public static DocPageDTO getRootExamplePage(String docNo) {
        DocPageDTO page = new DocPageDTO();
        page.setDocNo(docNo);
        page.setPageNo(YOP_DOC_EXAMPLE_PAGE_NO);
        page.setPageType(DocPageTypeEnum.WIKI);
        page.setPageVisible(DocVisableEnum.PUBLIC);
        page.setDisplay(true);
        page.setPageControl(YOP_DOC_COMMON_PAGE_CONTROL);
        page.setPid(YOP_DOC_ROOT_PAGE_ID);
        page.setSeq(1 + SEQ_STEP);
        page.setDepth(1);
        page.setTitle(YOP_DOC_EXAMPLE_PAGE_TITLE);
        page.setBlocks(Collections.singletonList(new DocContentBlock("root_example", YOP_DOC_EXAMPLE_PAGE_TITLE, YOP_DOC_EXAMPLE_PAGE_CONTENT)));
        page.setVersion(0L);
        return page;
    }



    public static DocPageDTO getProductApiParentPage(String docNo) {
        DocPageDTO apiParentPage = new DocPageDTO();
        apiParentPage.setDocNo(docNo);
        apiParentPage.setPageNo(YOP_DOC_PRODUCT_API_PARENT_PAGENO);
        apiParentPage.setTitle(YOP_DOC_PRODUCT_API_PARENT_TITLE);
        apiParentPage.setPageType(DocPageTypeEnum.API);
        apiParentPage.setPageControl(YOP_DOC_PRODUCT_API_PARENT_PAGE_CONTROL);
        apiParentPage.setPid(YOP_DOC_ROOT_PAGE_ID);
        apiParentPage.setSeq(1 + 3 * SEQ_STEP);
        apiParentPage.setPageVisible(DocVisableEnum.PUBLIC);
        apiParentPage.setDisplay(true);
        apiParentPage.setDepth(1);
        return apiParentPage;
    }

    public static DocPageDTO getProductSpiParentPage(String docNo) {
        DocPageDTO spiParentPage = new DocPageDTO();
        spiParentPage.setDocNo(docNo);
        spiParentPage.setPageNo(YOP_DOC_PRODUCT_SPI_PARENT_PAGENO);
        spiParentPage.setPageType(DocPageTypeEnum.SPI);
        spiParentPage.setPageControl(YOP_DOC_PRODUCT_SPI_PARENT_PAGE_CONTROL);
        spiParentPage.setTitle(YOP_DOC_PRODUCT_SPI_PARENT_TITLE);
        spiParentPage.setPid(YOP_DOC_ROOT_PAGE_ID);
        spiParentPage.setSeq(1 + 5 * SEQ_STEP);
        spiParentPage.setPageVisible(DocVisableEnum.PROTECTED);
        spiParentPage.setDisplay(true);
        spiParentPage.setDepth(1);
        return spiParentPage;
    }

    public static DocPageDTO getProductOtherParentPage(String docNo) throws Exception {
        DocPageDTO page = new DocPageDTO();
        page.setDocNo(docNo);
        page.setPageNo(YOP_DOC_PRODUCT_OTHERS_PARENT_PAGENO);
        page.setTitle(YOP_DOC_PRODUCT_OTHERS_PARENT_TITLE);
        page.setPageControl(YOP_DOC_PRODUCT_OTHERS_PARENT_PAGE_CONTROL);
        page.setPid(YOP_DOC_ROOT_PAGE_ID);
        page.setSeq(1 + 5 * SEQ_STEP);
        page.setDepth(1);
        page.setPageType(DocPageTypeEnum.WIKI);
        page.setPageVisible(DocVisableEnum.PUBLIC);
        page.setDisplay(true);
        page.setVersion(0L);
        return page;
    }

    public static DocPageDTO getDocApiCategoryPage(String docNo, Long id, Long pid, String title, String desc, Integer seq) {
        DocPageDTO page = new DocPageDTO();
        page.setId(id);
        page.setPid(pid);
        page.setDocNo(docNo);
        page.setPageNo("");
        page.setTitle(title);
        page.setDesc(desc);
        page.setPageControl(YOP_DOC_API_CATEGORY_PAGE_CONTROL);
        page.setSeq(seq);
        page.setDepth(2);
        page.setPageType(DocPageTypeEnum.WIKI);
        page.setPageVisible(DocVisableEnum.PUBLIC);
        page.setDisplay(true);
        page.setMetaType(DocPageMetaTypeEnum.CATEGORY);
        page.setChildren(new LinkedList<>());
        return page;
    }

    /**
     * 获取文档访问前缀（同ceph存储前缀）
     * @param docType 文档类型
     * @return
     */
    public static String docPrefix(DocTypeEnum docType) {
        switch (docType) {
            case PRODUCT:
                return "docs/products";
            case OPEN:
                return "docs/open";
            case SOLUTION:
                return "docs/solutions";
            default:
                return "docs";
        }
    }

    /**
     * 是否为模板文档
     *
     * @param docType
     * @return
     */
    public static boolean isTemplateDoc(DocTypeEnum docType) {
        return DocTypeEnum.PRODUCT.equals(docType) || DocTypeEnum.SOLUTION.equals(docType);
    }

    /**
     * 获取文档隐藏页面
     *
     * @param docNo 文档编码
     * @param docExcludeConfig 文档配置
     * @return 页面id，List<Long>
     */
    public static Set<Long> getExcludePages(String docNo, Map<String, String> docExcludeConfig) {
        String idsStr = null;
        String[] idsArray = null;
        if (MapUtils.isEmpty(docExcludeConfig)
                || StringUtils.isBlank(idsStr = docExcludeConfig.get(docNo))
                || ArrayUtils.isEmpty(idsArray = idsStr.split(","))) {
            return Collections.emptySet();
        }
        Set<Long> result = new HashSet<>(idsArray.length);
        for (String s : idsArray) {
            try {
                result.add(Long.valueOf(s));
            } catch (Exception e) {
                LOGGER.warn("illegal docExcludeConfig, value:{}, ex:", docExcludeConfig, e);
            }
        }
        return result;
    }

    public static String toApiAccessUri(String method, String path) {
        return method.toLowerCase() + path.replace("/", "_");
    }

    public static boolean isPlatformDoc(String docNo) {
        return null != docNo && StringUtils.equalsAny(docNo, YOP_PLATFORM_DOC_NO, YOP_SM_PLATFORM_DOC_NO);
    }
}
