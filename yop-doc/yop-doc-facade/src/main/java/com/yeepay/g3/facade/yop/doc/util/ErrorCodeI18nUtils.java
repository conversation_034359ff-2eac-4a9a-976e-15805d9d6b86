package com.yeepay.g3.facade.yop.doc.util;

/**
 * title: <br>
 * description: 描述<br>
 * Copyright: Copyright (c)2014<br>
 * Company: 易宝支付(YeePay)<br>
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2020-08-04 11:05
 */
public class ErrorCodeI18nUtils {
    public static final String PROVIDER = "YOP";
    public static final String BIZ_CODE = "yop-sys.errcodes";
    public static final String BIZ_NAME = "YOP错误码";


    public static final String SEPERATOR = ".";
    public static final String DESC_SUFFIX = "desc";
    public static final String INNER_SOLUTION_SUFFIX = "innerSolution";
    public static final String OUTER_SOLUTION_SUFFIX = "outerSolution";
    public static final String SUB_ERRCODES_SUFFIX = "subErrcodes";

    public static final String PLATFORM_ERRCODE_RESOURCE_KEY_PREFIX = "errcodes.platform";
    public static final String GROUP_ERRCODE_RESOURCE_KEY_PREFIX = "errcodes.groups";
    public static final String API_ERRCODE_RESOURCE_KEY_PREFIX = "errcodes.apis";

    public static String platformErrcodeDescResourceKey(String errcode) {
        return PLATFORM_ERRCODE_RESOURCE_KEY_PREFIX + SEPERATOR + errcode + SEPERATOR + DESC_SUFFIX;
    }

    public static String platformSubErrcodesResourceKey(String errcode) {
        return PLATFORM_ERRCODE_RESOURCE_KEY_PREFIX + SEPERATOR + errcode + SEPERATOR + SUB_ERRCODES_SUFFIX;
    }

    public static String groupSubErrcodesResourceKey(String apiGroupCode) {
        return GROUP_ERRCODE_RESOURCE_KEY_PREFIX + SEPERATOR + apiGroupCode + SEPERATOR + SUB_ERRCODES_SUFFIX;
    }

    public static String apiSubErrcodesResourceKey(String apiUri) {
        return API_ERRCODE_RESOURCE_KEY_PREFIX + SEPERATOR + apiUri + SEPERATOR + SUB_ERRCODES_SUFFIX;
    }

    public static String subErrcodeDescResourceKey(String subErrcode) {
        return subErrcode + SEPERATOR + DESC_SUFFIX;
    }

    public static String subErrcodeInnerSolutionResourceKey(String subErrcode) {
        return subErrcode + SEPERATOR + INNER_SOLUTION_SUFFIX;
    }

    public static String subErrcodeOuterSolutionResourceKey(String subErrcode) {
        return subErrcode + SEPERATOR + OUTER_SOLUTION_SUFFIX;
    }

}
