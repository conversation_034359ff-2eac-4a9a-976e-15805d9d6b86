/*
 * Copyright: Copyright (c)2011
 * Company: 易宝支付(YeePay)
 */
package com.yeepay.janus.facade.doc.dto.data;

import lombok.Data;

import java.io.Serializable;

/**
 * title: 常见问题关联文章的基本信息<br>
 * description: 描述<br>
 * Copyright: Copyright (c)2014<br>
 * Company: 易宝支付(YeePay)<br>
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2021/12/21
 */
@Data
public class DocFaqRelateItemDTO implements Serializable {

    private static final long serialVersionUID = -1L;

    /**
     * 关联文章的id
     */
    private String id;

    /**
     * 关联文章的类型
     */
    private String type;

    /**
     * 关联文章的标题
     */
    private String title;
}
