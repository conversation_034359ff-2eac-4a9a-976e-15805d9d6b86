<?xml version="1.0" encoding="UTF-8"?>
<Configuration
        packages="com.yeepay.g3.utils"><!--packages参数告诉log4j2还需要额外加载哪个包下的Log4j plugin，其中YeepayMessagePatternConverter即为定制的plugin,负责输出的日志带GUID -->

    <Appenders>
        <!--        <Console name="STDOUT" target="SYSTEM_OUT">-->
            <!-- <PatternLayout pattern="%d %-5p %c:%L [%t] - %m%n" /> -->
            <!--<PatternLayout pattern="%d %-5p %c [%t] - %Y%n"/>-->
        <!--            <PatternLayout pattern="%d{yyyy-MM-dd HH:mm:ss,SSS} - %C{3} -%-4r [%t] %-5p %x - %msg%n%throwable"/>-->
        <!--        </Console>-->
    </Appenders>

    <Loggers>
        <Logger name="com.yeepay" level="DEBUG"/>
        <Logger name="org.springframework.boot.autoconfigure.logging" level="INFO"/>
        <Logger name="com.alibaba.dubbo" level="WARN"/>
        <Logger name="com.yeepay.g3.utils.soa.registry.zookeeper.ZookeeperRegistry" level="WARN"/>
        <Logger name="com.alibaba.druid.pool.DruidDataSourceStatLoggerImpl" level="WARN"/>
        <Logger name="com.alibaba.dubbo.common.utils.ConfigUtils" level="ERROR"/>
        <Root level="INFO">
            <!--            <AppenderRef ref="STDOUT"/>-->
        </Root>
    </Loggers>
</Configuration>