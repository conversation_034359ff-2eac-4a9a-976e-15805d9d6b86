/*
 * Copyright: Copyright (c)2011
 * Company: 易宝支付(YeePay)
 */
package com.yeepay.g3.i18n.support;

import com.yeepay.g3.i18n.dto.I18nMaterialItemDTO;

import java.util.List;

/**
 * title: <br>
 * description: 描述<br>
 * Copyright: Copyright (c)2014<br>
 * Company: 易宝支付(YeePay)<br>
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2020-07-17 11:12
 */
public interface I18nSpliter {

    /**
     * 素材拆分
     * @return
     */
    List<I18nMaterialItemDTO> split();
}
