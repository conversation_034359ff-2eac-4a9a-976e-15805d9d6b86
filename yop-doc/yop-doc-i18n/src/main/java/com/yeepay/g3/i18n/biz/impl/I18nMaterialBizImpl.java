/*
 * Copyright: Copyright (c)2011
 * Company: 易宝支付(YeePay)
 */
package com.yeepay.g3.i18n.biz.impl;

import com.baomidou.dynamic.datasource.annotation.DSTransactional;
import com.yeepay.g3.i18n.biz.I18nMaterialBiz;
import com.yeepay.g3.i18n.domain.*;
import com.yeepay.g3.i18n.dto.I18nMaterialItemDTO;
import com.yeepay.g3.i18n.dto.I18nMaterialsPushDTO;
import com.yeepay.g3.i18n.enums.I18nContentTypeEnum;
import com.yeepay.g3.i18n.enums.I18nTaskStatus;
import com.yeepay.g3.i18n.service.*;
import com.yeepay.g3.i18n.utils.JsonUtils;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;

import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * title: <br>
 * description: 描述<br>
 * Copyright: Copyright (c)2014<br>
 * Company: 易宝支付(YeePay)<br>
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2020/7/14 下午4:58
 */
@Component
public class I18nMaterialBizImpl implements I18nMaterialBiz {

    private static final Logger LOGGER = LoggerFactory.getLogger(I18nMaterialBizImpl.class);

    @Autowired
    private I18nProjectService i18nProjectService;

    @Autowired
    private I18nMaterialService i18nMaterialService;

    @Autowired
    private I18nMaterialContentHistoryService i18nMaterialContentHistoryService;

    @Autowired
    private I18nResourceService i18nResourceService;

    @Autowired
    private I18nPlanService i18nPlanService;

    @Autowired
    private I18nTaskService i18nTaskService;

    @Override
    @DSTransactional
    @Async
    public void pushMaterials(I18nMaterialsPushDTO materials) {
        String provider = null;
        String bizCode = null;
        Long i18nProjectId = null;
        List<I18nMaterialItemDTO> i18nMaterialItemDTOS = null;
        List<I18nPlan> i18nPlans = null;
        try {
            provider = materials.getProvider();
            bizCode = materials.getBizCode();
            I18nProject i18nProject = i18nProjectService.findByBizCode(provider, bizCode);
            if (null == i18nProject) {
                i18nProject = new I18nProject();
                i18nProject.setProvider(provider);
                i18nProject.setBizCode(bizCode);
                i18nProject.setBizName(materials.getBizName());
                i18nProjectService.create(i18nProject);
            }
            i18nProjectId = i18nProject.getId();
            i18nMaterialItemDTOS = materials.getMaterials();
            if (CollectionUtils.isEmpty(i18nMaterialItemDTOS)) {
                return;
            }
            i18nPlans = i18nPlanService.findByProjectId(i18nProjectId);
        } catch (Exception e) {
            throw new RuntimeException(e);
        }

        for (I18nMaterialItemDTO i18nMaterialItemDTO : i18nMaterialItemDTOS) {
            try {
                String i18nKey = i18nMaterialItemDTO.getI18nKey();
                String content = i18nMaterialItemDTO.getContent();
                String contentType = i18nMaterialItemDTO.getContentType().name();
                I18nMaterial i18nMaterial = i18nMaterialService.findByI18nKey(i18nProjectId, i18nKey);
                if (null == i18nMaterial) {
                    i18nMaterial = new I18nMaterial();
                    i18nMaterial.setProjectId(i18nProjectId);
                    i18nMaterial.setContentType(contentType);
                    i18nMaterial.setI18nKey(i18nKey);
                    i18nMaterial.setVersion(0L);
                    i18nMaterialService.create(i18nMaterial);
                }
                Long i18nMaterialId = i18nMaterial.getId();
                Long i18nMaterialVersion = i18nMaterial.getVersion();
                I18nMaterialContentHistory i18nMaterialContentHistory = i18nMaterialContentHistoryService.findByMaterial(i18nMaterialId, i18nMaterialVersion);
                boolean contentChanged = false;
                if (null == i18nMaterialContentHistory || (contentChanged = contentChanged(i18nMaterialItemDTO.getContentType(),
                        i18nMaterialContentHistory.getOriginContent(), content))) {
                    long version = null == i18nMaterialContentHistory ? 0 : i18nMaterialService.updateVersion(i18nMaterialId);
                    i18nMaterialContentHistory = new I18nMaterialContentHistory();
                    i18nMaterialContentHistory.setMaterialId(i18nMaterialId);
                    i18nMaterialContentHistory.setMaterialVersion(version);
                    i18nMaterialContentHistory.setLength(content.length());
                    i18nMaterialContentHistory.setOriginContent(content);
                    i18nMaterialContentHistoryService.create(i18nMaterialContentHistory);
                }
                if (!CollectionUtils.isEmpty(i18nPlans)) {
                    for (I18nPlan i18nPlan : i18nPlans) {
                        Long i18nPlanId = i18nPlan.getId();
                        String lang = i18nPlan.getLang();
                        I18nTask i18nTask = i18nTaskService.findByPlanAndMaterial(i18nPlanId, i18nMaterialId);
                        if (null == i18nTask) {
                            i18nTask = new I18nTask();
                            i18nTask.setPlanId(i18nPlanId);
                            i18nTask.setMaterialId(i18nMaterialId);
                            i18nTask.setStatus(I18nTaskStatus.INIT);
                            i18nTask.setMaterialVersion(i18nMaterialContentHistory.getMaterialVersion());
                            i18nTaskService.create(i18nTask);
                        } else if (i18nTask.getStatus() == I18nTaskStatus.INIT) {
                            i18nTask.setMaterialVersion(i18nMaterialContentHistory.getMaterialVersion());
                            i18nTask.setLastModifiedDatetime(new Date());
                            i18nTaskService.update(i18nTask);
                        } else if (contentChanged) {
                            i18nTask.setStatus(I18nTaskStatus.UPDATED);
                            i18nTask.setMaterialVersion(i18nMaterialContentHistory.getMaterialVersion());
                            i18nTask.setLastModifiedDatetime(new Date());
                            i18nTaskService.updateStatus(i18nTask);
                        }
                        I18nResource i18nResource = i18nResourceService.findByMaterialAndLang(i18nMaterialId, lang);
                        if (null == i18nResource) {
                            i18nResource = new I18nResource();
                            i18nResource.setProvider(provider);
                            i18nResource.setBizCode(bizCode);
                            i18nResource.setMaterialId(i18nMaterialId);
                            i18nResource.setLang(lang);
                            i18nResource.setI18nKey(i18nKey);
                            i18nResource.setHandledContent(content);
                            i18nResource.setTranslated(Boolean.FALSE);
                            i18nResourceService.create(i18nResource);
                        } else if (Boolean.FALSE.equals(i18nResource.getTranslated())) {
                            i18nResource.setHandledContent(content);
                            i18nResourceService.untranslated(i18nResource);
                        }
                    }
                }
            } catch (Exception e) {
                LOGGER.error("error when push material, item:" + i18nMaterialItemDTO + "ex:", e);
            }
        }
    }

    private boolean contentChanged(I18nContentTypeEnum contentType, String originContent, String changeContent) {
        if (StringUtils.equals(originContent, changeContent)) {
            return false;
        }
        if (I18nContentTypeEnum.JSON.equals(contentType)) {
            final Map<?,?> map1 = JsonUtils.fromJson(originContent, Map.class);
            final Map<?,?> map2 = JsonUtils.fromJson(changeContent, Map.class);
            if (null != map1 && null != map2) {
                return !(map1.size() == map2.size() &&
                        map1.entrySet().stream().allMatch(entry -> entry.getValue().equals(map2.get(entry.getKey()))));
            }
        }
        return true;
    }

    @Override
    public void cleanMaterial(List<Long> materialIds) {
        for (Long materialId : materialIds) {
            try {
                i18nMaterialService.deleteById(materialId);
                i18nMaterialContentHistoryService.deleteByMaterial(materialId);
                i18nTaskService.deleteByMaterial(materialId);
                i18nResourceService.deleteByMaterial(materialId);
            } catch (Exception e) {
                LOGGER.error("error when cleanMaterial, materialId:" + materialId, e);
            }

        }
    }

    @Override
    public void fixProviderInfo(List<String> i18nKeyPrefix, String provider, String bizCode) {
        i18nResourceService.fixProviderInfo(i18nKeyPrefix, provider, bizCode);
    }

    @Override
    public void fixI18nMaterialVersion() {
        i18nMaterialService.fixI18nMaterialVersion();
    }

}
