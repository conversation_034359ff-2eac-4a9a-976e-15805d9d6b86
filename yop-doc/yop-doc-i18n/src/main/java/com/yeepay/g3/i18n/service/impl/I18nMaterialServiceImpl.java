/*
 * Copyright: Copyright (c)2011
 * Company: 易宝支付(YeePay)
 */
package com.yeepay.g3.i18n.service.impl;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.yeepay.g3.i18n.domain.I18nMaterial;
import com.yeepay.g3.i18n.repository.I18nMaterialRepository;
import com.yeepay.g3.i18n.service.I18nMaterialService;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * title: <br>
 * description: 描述<br>
 * Copyright: Copyright (c)2014<br>
 * Company: 易宝支付(YeePay)<br>
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2020/7/15 下午5:43
 */
@Component
public class I18nMaterialServiceImpl implements I18nMaterialService {

    @Autowired
    private I18nMaterialRepository i18nMaterialRepository;

    @Override
    public void create(I18nMaterial i18nMaterial) {
        i18nMaterialRepository.insert(i18nMaterial);
    }

    @Override
    public long updateVersion(long id) {
        I18nMaterial target = new I18nMaterial();
        target.setId(id);
        boolean lock;
        do {
            I18nMaterial i18nMaterial = i18nMaterialRepository.selectById(id);
            target.setVersion(i18nMaterial.getVersion());
            lock = 1 != i18nMaterialRepository.updateById(target);
        } while (lock);
        return target.getVersion();
    }

    @Override
    public I18nMaterial findByI18nKey(long projectId, String i18nKey) {
        I18nMaterial condition = new I18nMaterial();
        condition.setProjectId(projectId);
        condition.setI18nKey(i18nKey);
        // 理论上1条，但有脏数据, 取最新的(id最大的)
        final List<I18nMaterial> i18nMaterials = i18nMaterialRepository.selectList(Wrappers.query(condition).orderByDesc("id"));
        return CollectionUtils.isEmpty(i18nMaterials) ? null : i18nMaterials.get(0);
    }

    @Override
    public List<I18nMaterial> findByProjectId(long projectId) {
        I18nMaterial condition = new I18nMaterial();
        condition.setProjectId(projectId);
        return i18nMaterialRepository.selectList(Wrappers.query(condition));
    }

    @Override
    public I18nMaterial findById(Long materialId) {
        return i18nMaterialRepository.selectById(materialId);
    }

    @Override
    public void deleteById(Long materialId) {
        i18nMaterialRepository.deleteById(materialId);
    }

    @Override
    public void fixI18nMaterialVersion() {
        i18nMaterialRepository.fixI18nMaterialVersion();
    }
}
