<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/maven-v4_0_0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>com.yeepay.janus</groupId>
        <artifactId>janus-parent</artifactId>
        <version>1.1-SNAPSHOT</version>
        <relativePath>../../pom.xml</relativePath>
    </parent>

    <artifactId>yop-codegen-generator</artifactId>
    <packaging>jar</packaging>

    <dependencies>
        <!--yop-->
        <dependency>
            <groupId>com.yeepay.janus</groupId>
            <artifactId>yop-doc-codegen-facade</artifactId>
        </dependency>
        <dependency>
            <groupId>com.yeepay.g3.yop</groupId>
            <artifactId>yop-sys-facade</artifactId>
        </dependency>

        <!--swagger-->
        <dependency>
            <groupId>io.swagger.codegen.v3</groupId>
            <artifactId>swagger-codegen-generators</artifactId>
        </dependency>
        <dependency>
            <groupId>io.swagger.codegen.v3</groupId>
            <artifactId>swagger-codegen</artifactId>
        </dependency>
        <dependency>
            <groupId>com.samskivert</groupId>
            <artifactId>jmustache</artifactId>
        </dependency>
        <dependency>
            <groupId>org.apache.commons</groupId>
            <artifactId>commons-collections4</artifactId>
        </dependency>

        <!--test-->
        <dependency>
            <groupId>org.junit.jupiter</groupId>
            <artifactId>junit-jupiter</artifactId>
            <scope>test</scope>
        </dependency>
    </dependencies>

</project>