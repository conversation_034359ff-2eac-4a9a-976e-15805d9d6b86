{{#operations}}
{{#operation}}

### {{path}} {{httpMethod}}


package {{invokerPackage}};

{{#hasOauth2}}
import {{invokerPackage}}.auth.credentials.YopOauth2Credentials;
{{/hasOauth2}}
import {{invokerPackage}}.exception.YopClientException;
{{#hasOauth2}}
import {{invokerPackage}}.model.YopRequestConfig;
{{/hasOauth2}}
{{#hasDownload}}
import {{invokerPackage}}.model.yos.YosDownloadInputStream;
import {{invokerPackage}}.model.yos.YosDownloadResponse;
{{/hasDownload}}
import {{package}}.{{classname}}Client;
import {{package}}.{{classname}}ClientBuilder;
import {{package}}.request.*;
{{#hasOperationConflict}}
import {{package}}.request.old.*;
{{/hasOperationConflict}}
{{^hasOnlyDownload}}
import {{package}}.response.*;
{{#hasOperationConflict}}
import {{package}}.response.old.*;
{{/hasOperationConflict}}
{{/hasOnlyDownload}}
{{#sampleImports}}
import {{.}};
{{/sampleImports}}
import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
{{#hasDownload}}
import java.io.IOException;
import org.apache.commons.io.IOUtils;
{{/hasDownload}}

public class Example {

    private static final Logger LOGGER = LoggerFactory.getLogger(Example.class);

    // 该Client线程安全，请使用单例模式，多次请求共用
    private static final {{classname}}Client api = {{classname}}ClientBuilder.builder().build();

    public static void main(String[] args) {
        {{{requestType}}} request = new {{{requestType}}}();
        {{#isOauth2}}
        YopOauth2Credentials yopOauth2Credentials = new YopOauth2Credentials("appKey_example", "token_sample");
        request.getRequestConfig().setCredentials(yopOauth2Credentials);
        {{/isOauth2}}
        {{#allParams}}
        {{#isEnum}}
        {{#isListContainer}}
        request.{{setter}}(Arrays.asList({{{requestType}}}.{{{enumName}}}.fromValue("{{{allowableValues.values.0}}}")));
        {{/isListContainer}}
        {{#isMapContainer}}
        request.{{setter}}(new HashMap());
        {{/isMapContainer}}
        {{^isContainer}}
        request.{{setter}}({{{requestType}}}.{{{enumName}}}.fromValue("{{{allowableValues.values.0}}}"));
        {{/isContainer}}
        {{/isEnum}}
        {{^isEnum}}
        request.{{setter}}({{{example}}});
        {{/isEnum}}
        {{/allParams}}
        try {
            {{#isYosDownload}}YosDownloadResponse{{/isYosDownload}}{{^isYosDownload}}{{responseType}}{{/isYosDownload}} response = api.{{operationId}}(request);
            {{#returnType}}
            {{#isYosDownload}}
            // 及时关闭文件流，避免连接泄漏
            YosDownloadInputStream result = null;
            try {
                result = response.getResult();
                saveFile(result);
            } catch (IOException e) {
                LOGGER.error("error when download, ex:", e);
            }  finally {
                if (null != result) {
                    try {
                        result.close();
                    } catch (IOException e) {
                        LOGGER.error("error when close YosDownloadInputStream, ex:", e);
                    }
                }
            }
            {{/isYosDownload}}
            {{^isYosDownload}}
            LOGGER.info("result:{}", response.getResult());
            {{/isYosDownload}}
            {{/returnType}}
        } catch (YopClientException e) {
            LOGGER.error("Exception when calling {{{classname}}}Client#{{{operationId}}}, ex:", e);
        }
    }

    {{#isYosDownload}}
    private static void saveFile(YosDownloadInputStream in) throws java.io.IOException {
        //todo save 可参考：https://open.yeepay.com/docs/open/platform-doc/sdk_guide-sm/yos-api-stream-handle实现
    }
    {{/isYosDownload}}
}
{{/operation}}
{{/operations}}
