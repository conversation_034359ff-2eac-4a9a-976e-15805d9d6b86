package com.yeepay.g3.core.yop.codegen;

import com.yeepay.g3.core.yop.codegen.generator.DefaultGenerator;
import com.yeepay.g3.core.yop.codegen.generator.YopCodegenConstants;
import com.yeepay.g3.core.yop.codegen.generator.YopCodegenOperation;
import com.yeepay.g3.core.yop.codegen.generator.YopCodegenParameter;
import com.yeepay.g3.core.yop.codegen.generator.langs.java.YopJavaClientCodegen;
import com.yeepay.g3.core.yop.codegen.generator.swagger.JsonUtils;
import io.swagger.codegen.v3.*;
import io.swagger.v3.oas.models.OpenAPI;
import org.junit.After;
import org.junit.Before;
import org.junit.Test;
import org.junit.rules.TemporaryFolder;
import org.springframework.core.io.ClassPathResource;

import java.io.IOException;
import java.util.Arrays;

/**
 * Tests for DefaultGenerator logic
 */
public class DefaultGeneratorTest {

    public TemporaryFolder folder = new TemporaryFolder();

    @Before
    public void setUp() throws Exception {
        folder.create();
    }

    @After
    public void tearDown() {
        folder.delete();
    }

    @Test
    public void generate() throws IOException {
        CodegenModelFactory.setTypeMapping(CodegenModelType.OPERATION, YopCodegenOperation.class);
        CodegenModelFactory.setTypeMapping(CodegenModelType.PARAMETER, YopCodegenParameter.class);

        Generator generator = new DefaultGenerator();

        YopJavaClientCodegen codegenConfig = new YopJavaClientCodegen();
        codegenConfig.additionalProperties().put(YopCodegenConstants.YOP_API_TRIM_PARAM_TYPE_FORMAT_CONFIG, Arrays.asList("string:merchant-no"));
        // post json
        OpenAPI openAPI = JsonUtils.deserialize(new ClassPathResource("api/auth_dev.json").getInputStream());
        // post form
//        OpenAPI openAPI = JsonUtils.deserialize(new ClassPathResource("api/post_form_qa.json").getInputStream());
        // get form
//        OpenAPI openAPI = JsonUtils.deserialize(new ClassPathResource("api/get_form_qa.json").getInputStream());

        ClientOptInput clientOptInput = new ClientOptInput().config(codegenConfig).openAPI(openAPI).opts(new ClientOpts());
        generator.opts(clientOptInput);

        generator.generate();
    }

}
