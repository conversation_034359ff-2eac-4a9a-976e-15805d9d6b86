/*
 * Copyright: Copyright (c)2011
 * Company: 易宝支付(YeePay)
 */
package com.yeepay.g3.core.yop.mustache.java;

import com.samskivert.mustache.Mustache;
import com.samskivert.mustache.Template;

import java.io.*;
import java.util.Map;
import java.util.Scanner;

/**
 * title: <br>
 * description: 描述<br>
 * Copyright: Copyright (c)2014<br>
 * Company: 易宝支付(YeePay)<br>
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 6/8/21
 */
public abstract class AbstractRenderTest {

    abstract String getTemplateFile();


    protected String renderToString(Map<String, Object> templateData) throws IOException {
        String templateFile = getTemplateFile();
        String template = readTemplate(templateFile);
        Mustache.Compiler compiler = Mustache.compiler();
//        compiler = config.processCompiler(compiler);// 无处理
        Template tmpl = compiler
                .withLoader(new Mustache.TemplateLoader() {
                    @Override
                    public Reader getTemplate(String name) {
                        return getTemplateReader(templateFile.substring(0, templateFile.lastIndexOf("/") + 1) + name + ".mustache");
                    }
                })
                .defaultValue("")
                .compile(template);

        return tmpl.execute(templateData);
    }

    public String readTemplate(String classpathTemplateFile) {
        try {
            Reader reader = this.getTemplateReader(classpathTemplateFile);
            if (reader == null) {
                throw new RuntimeException("no file found");
            } else {
                Scanner s = (new Scanner(reader)).useDelimiter("\\A");
                return s.hasNext() ? s.next() : "";
            }
        } catch (Exception var4) {
            throw new RuntimeException("can't load template " + classpathTemplateFile);
        }
    }

    public Reader getTemplateReader(String classpathTemplateFile) {
        try {
            InputStream is = this.getClass().getClassLoader().getResourceAsStream(classpathTemplateFile);
            if (is == null) {
                is = new FileInputStream(new File(classpathTemplateFile));
            }

            return new InputStreamReader((InputStream)is, "UTF-8");
        } catch (Exception var3) {
            throw new RuntimeException("can't load template " + classpathTemplateFile);
        }
    }
}
