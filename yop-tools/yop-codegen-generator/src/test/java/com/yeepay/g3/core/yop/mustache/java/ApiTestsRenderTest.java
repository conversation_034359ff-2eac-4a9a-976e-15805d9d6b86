/*
 * Copyright: Copyright (c)2011
 * Company: 易宝支付(YeePay)
 */
package com.yeepay.g3.core.yop.mustache.java;

import org.junit.Test;

import java.io.IOException;

/**
 * title: <br>
 * description: 描述<br>
 * Copyright: Copyright (c)2014<br>
 * Company: 易宝支付(YeePay)<br>
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 6/8/21
 */
public class ApiTestsRenderTest extends BaseApiRenderTest {

    private static final String templateFile = "mustache/Java/api_test.mustache";

    @Test
    public void testRenderApiTest() throws IOException {
        testRenderApi();
    }

    @Override
    String getTemplateFile() {
        return templateFile;
    }
}
