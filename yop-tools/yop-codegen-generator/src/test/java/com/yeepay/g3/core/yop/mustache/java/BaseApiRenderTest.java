/*
 * Copyright: Copyright (c)2011
 * Company: 易宝支付(YeePay)
 */
package com.yeepay.g3.core.yop.mustache.java;

import com.yeepay.g3.core.yop.codegen.generator.YopCodegenConstants;
import com.yeepay.g3.core.yop.codegen.generator.YopCodegenOperation;
import org.apache.commons.lang3.StringUtils;

import java.io.IOException;
import java.util.Collections;
import java.util.HashMap;
import java.util.Map;

/**
 * title: <br>
 * description: 描述<br>
 * Copyright: Copyright (c)2014<br>
 * Company: 易宝支付(YeePay)<br>
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 6/8/21
 */
public abstract class BaseApiRenderTest extends AbstractRenderTest {

    public void testRenderApi() throws IOException {
        Map<String, Object> operations = mockOperations();
        String rendered = renderToString(operations);
        assert StringUtils.isNotBlank(rendered);
        System.out.println(rendered);
    }

    private Map<String, Object> mockOperations() {
        Map<String, Object> operations = new HashMap<String, Object>();
        Map<String, Object> objs = new HashMap<String, Object>();
        objs.put("classname", "TestYosDownload");
        objs.put("pathPrefix", "Test");

        YopCodegenOperation yopCodegenOperation = new YopCodegenOperation();
        yopCodegenOperation.getVendorExtensions().put(YopCodegenConstants.OPERATION_IS_YOS_DOWNLOAD, true);
        yopCodegenOperation.returnType = "YosDownloadResult";
        yopCodegenOperation.operationId = "testOperationId";

        objs.put("operation", Collections.singletonList(yopCodegenOperation));

        operations.put("operations", objs);
        operations.put("package", "com.yop.sdk.test");
        return operations;
    }
}
