/*
 * Copyright: Copyright (c)2014
 * Company: 易宝支付(YeePay)
 */
package com.yeepay.g3.core.yop.operation;

import com.google.common.base.CaseFormat;
import io.swagger.codegen.v3.generators.DefaultCodegenConfig;
import org.apache.commons.lang3.StringUtils;
import org.junit.Test;

import java.util.regex.Pattern;

import static com.yeepay.g3.core.yop.codegen.generator.YopCodegenConstants.API_NAME_CONFLICT_WITH_URI;

/**
 * title: <br>
 * description: 描述<br>
 * Copyright: Copyright (c)2014<br>
 * Company: 易宝支付(YeePay)<br>
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2024/8/28
 */
public class LocalOperationIdTest {

    @Test
    public void testApiNameApiUri() {
        String apiUri = "/rest/v1.1/lx-test/case/cconversion";
        String apiName = "case_cconVersion";
        final String operationId = toOperationId(apiUri);
        System.out.println(operationId);
        System.out.println(StringUtils.replace(apiName, "_", "").equalsIgnoreCase(operationId));
        System.out.println(apiName);
        System.out.println(CaseFormat.LOWER_UNDERSCORE.to(CaseFormat.UPPER_CAMEL, apiName));
        System.out.println(Character.toUpperCase(operationId.charAt(0)) + operationId.substring(1));
        System.out.println(DefaultCodegenConfig.camelize(apiName));

        if (StringUtils.replace(apiName, "_", "").equalsIgnoreCase(operationId) &&
                !CaseFormat.LOWER_UNDERSCORE.to(CaseFormat.UPPER_CAMEL, apiName)
                        .equals(Character.toUpperCase(operationId.charAt(0)) + operationId.substring(1))) {
            // 生成的Request、Response、Marshaller会文件冲突
            System.out.println("true");
        }
    }

    private static final Pattern API_VERSION_PATTERN = Pattern.compile("v\\d+.\\d+");
    private static String toOperationId(String apiUri) {
        String[] parts = StringUtils.split(StringUtils.substring(apiUri, StringUtils.ordinalIndexOf(apiUri, "/", 4)),
                "/");
        StringBuilder operationId = new StringBuilder();
        operationId.append(CaseFormat.LOWER_HYPHEN.to(CaseFormat.LOWER_CAMEL, parts[0]));
        if (parts.length > 1) {
            for (int index = 1; index < parts.length; index++) {
                operationId.append(CaseFormat.LOWER_HYPHEN.to(CaseFormat.UPPER_CAMEL, parts[index]));
            }
        }
        final String[] split = StringUtils.split(apiUri, "/");
        if (split.length > 2 && API_VERSION_PATTERN.matcher(split[1]).matches() && !"v1.0".equalsIgnoreCase(split[1])) {
            String apiVersion = split[1];
            apiVersion = apiVersion.toUpperCase().replace(".", "_").replace("_0", "");
            operationId.append(apiVersion);
        }
        return operationId.toString();
    }
}
