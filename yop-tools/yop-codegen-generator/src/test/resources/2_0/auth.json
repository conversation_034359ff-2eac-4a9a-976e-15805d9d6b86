{"swagger": "2.0", "info": {"title": "认证服务", "version": "3.0", "description": "身份证银行卡相关认证", "termsOfService": "https://open.yeepay.com/", "contact": {"name": "创新应用部基础应用组", "email": "<EMAIL>"}, "license": {"name": "Apache 2.0", "url": "http://www.apache.org/licenses/LICENSE-2.0.html"}}, "host": "open.yeepay.com", "basePath": "/yop-center", "schemes": ["https"], "x-api-group": "test", "x-service-group": ["test"], "paths": {"/rest/v1.0/test/auth-idcard-with-json": {"post": {"tags": ["test"], "summary": "个人身份证认证", "description": "个人身份证认证", "operationId": "idCardAuth", "consumes": ["application/json"], "produces": ["application/json"], "parameters": [{"name": "idCardAuthParam", "in": "body", "description": "身份证认证参数", "required": true, "schema": {"$ref": "#/definitions/IdCardAuthParam"}}], "responses": {"200": {"description": "Success", "schema": {"$ref": "#/definitions/IdCardAuthResult"}}, "400": {"description": "Status 400"}, "405": {"description": "Invalid input"}}, "security": [{"YOP-RSA2048-SHA256": []}]}}}, "securityDefinitions": {"YOP-RSA2048-SHA256": {"type": "<PERSON><PERSON><PERSON><PERSON>", "name": "YOP-RSA2048-SHA256", "in": "header"}}, "definitions": {"IdCardAuthParam": {"type": "object", "properties": {"id_card_number": {"description": "身份证号码", "required": true, "type": "string"}, "name": {"description": "姓名", "required": false, "type": "string"}, "request_system": {"description": "请求系统", "required": false, "type": "string"}, "request_flow_id": {"description": "请求流水号", "required": false, "type": "string"}}}, "IdCardAuthResult": {"type": "object", "properties": {"status": {"type": "string", "description": "状态"}}}}}