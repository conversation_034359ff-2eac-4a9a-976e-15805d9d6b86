{"swagger": "2.0", "info": {"version": "2.0", "title": "蜂鸟登录认证", "contact": {"name": "创新应用部基础应用组", "url": "http://www.yeepay.com", "email": "<EMAIL>"}, "license": {"name": "Apache 2.0", "url": "http://www.apache.org/licenses/LICENSE-2.0.html"}}, "host": "open.yeepay.com", "basePath": "/yop-center", "schemes": ["https"], "consumes": ["application/json"], "produces": ["application/json"], "paths": {"/rest/v2.0/hbird/oauth2/token": {"post": {"tags": ["hbird"], "parameters": [{"name": "corp_id", "in": "query", "description": "corp_id", "required": true, "type": "string"}, {"name": "user_name", "in": "query", "description": "user_name", "required": true, "type": "string"}, {"name": "password", "in": "query", "description": "password", "required": true, "type": "string"}, {"name": "need_corp_info", "in": "query", "description": "need_corp_info", "required": true, "type": "string"}, {"name": "need_token", "in": "query", "description": "need_token", "required": true, "type": "string"}, {"name": "verified", "in": "query", "description": "verified", "required": true, "type": "string"}], "security": [{"YOP-HMAC-AES128": []}]}}, "/rest/v2.0/hbird/corp/query": {"post": {"tags": ["hbird"], "parameters": [{"name": "corp_id", "in": "query", "description": "corp_id", "required": true, "type": "string"}, {"name": "depth", "in": "query", "description": "depth", "required": true, "type": "string"}, {"name": "with_user", "in": "query", "description": "with_user", "required": true, "type": "string"}, {"name": "page_num", "in": "query", "description": "page_num", "required": true, "type": "string"}, {"name": "page_size", "in": "query", "description": "page_size", "required": true, "type": "string"}], "security": [{"YOP-OAUTH2": []}]}}}, "securityDefinitions": {"YOP-HMAC-AES128": {"type": "YOP-HMAC", "name": "YOP-HMAC-AES128"}, "YOP-OAUTH2": {"type": "YOP-OAUTH2", "name": "YOP-OAUTH2"}}}