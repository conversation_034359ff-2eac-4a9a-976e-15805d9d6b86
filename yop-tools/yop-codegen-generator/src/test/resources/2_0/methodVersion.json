{"swagger": "2.0", "info": {"version": "3.0", "title": "测试修改", "termsOfService": "https://open.yeepay.com/", "contact": {"name": "金融科技部", "url": "http://www.yeepay.com", "email": "<EMAIL>"}, "license": {"name": "Apache 2.0", "url": "http://www.apache.org/licenses/LICENSE-2.0"}}, "host": "open.yeepay.com", "basePath": "/yop-center", "schemes": ["https"], "security": [{"YOP-RSA2048-SHA256": []}], "paths": {"/rest/v1.0/test/testfileupload": {"post": {"tags": ["test"], "summary": "测试服务上传", "description": "测试服务上传", "operationId": "testfileuploadV1", "consumes": ["multipart/form-data"], "produces": ["application/json"], "parameters": [{"name": "name", "in": "formData", "required": false, "type": "file"}], "responses": {"200": {"schema": {"$ref": "#/definitions/AuthIdCardResultDTO"}}}, "security": [{"YOP-RSA2048-SHA256": []}], "x-operation-type": "MULTI_FILE_UPLOAD"}}, "/rest/v1.0/test/test-file-upload": {"post": {"tags": ["test"], "summary": "测试服务上传", "description": "测试服务上传", "operationId": "testFileUploadV1", "consumes": ["multipart/form-data"], "produces": ["application/json"], "parameters": [{"name": "name", "in": "formData", "required": false, "type": "file"}], "responses": {"200": {"schema": {"$ref": "#/definitions/AuthIdCardResultDTO"}}}, "security": [{"YOP-RSA2048-SHA256": []}], "x-operation-type": "MULTI_FILE_UPLOAD"}}, "/rest/v2.0/test/test-file-upload": {"post": {"tags": ["test"], "summary": "测试服务上传", "description": "测试服务上传", "operationId": "testFileUploadV2", "consumes": ["multipart/form-data"], "produces": ["application/json"], "parameters": [{"name": "name", "in": "formData", "required": false, "type": "file"}], "responses": {"200": {"schema": {"$ref": "#/definitions/AuthIdCardResultDTO"}}}, "security": [{"YOP-RSA2048-SHA256": []}], "x-operation-type": "MULTI_FILE_UPLOAD"}}, "/rest/v2.1/test/test-file-upload": {"post": {"tags": ["test"], "summary": "测试服务上传", "description": "测试服务上传", "operationId": "testFileUploadV2_1", "consumes": ["multipart/form-data"], "produces": ["application/json"], "parameters": [{"name": "name", "in": "formData", "required": false, "type": "file"}], "responses": {"200": {"schema": {"$ref": "#/definitions/AuthIdCardResultDTO"}}}, "security": [{"YOP-RSA2048-SHA256": []}], "x-operation-type": "MULTI_FILE_UPLOAD"}}}, "securityDefinitions": {"YOP-RSA2048-SHA256": {"type": "<PERSON><PERSON><PERSON><PERSON>", "name": "YOP-RSA2048-SHA256", "in": "header"}}, "definitions": {"AuthIdCardResultDTO": {"type": "object", "properties": {"orderId": {"type": "string", "title": "未命名"}, "requestFlowId": {"type": "string", "title": "未命名"}, "authType": {"type": "string", "title": "未命名"}, "authInterfaceType": {"type": "string", "title": "未命名"}, "authInterfaceId": {"type": "integer", "format": "int64", "title": "未命名"}, "bottomInterface": {"type": "string", "title": "未命名"}, "status": {"type": "string", "title": "未命名"}, "fee": {"type": "number", "format": "double", "title": "未命名"}, "cost": {"type": "number", "format": "double", "title": "未命名"}, "externalOrderId": {"type": "string", "title": "未命名"}, "remark": {"type": "string", "title": "未命名"}, "channelReturnCode": {"type": "string", "title": "未命名"}, "channelReturnMsg": {"type": "string", "title": "未命名"}, "encryptMsg": {"type": "string", "title": "未命名"}, "invokeRecords": {"type": "array", "title": "未命名", "items": {"title": "未命名", "$ref": "#/definitions/InvokeRecord"}}, "name": {"type": "string", "title": "未命名"}, "idCardNumber": {"type": "string", "title": "未命名"}, "address": {"type": "string", "title": "未命名"}, "photo": {"type": "string", "title": "未命名"}}}, "InvokeRecord": {"type": "object", "properties": {"orderId": {"type": "string", "title": "未命名"}, "externalOrderId": {"type": "string", "title": "未命名"}, "authType": {"type": "string", "title": "未命名"}, "authInterfaceId": {"type": "integer", "format": "int64", "title": "未命名"}, "bottomInterface": {"type": "string", "title": "未命名"}, "authStatus": {"type": "string", "title": "未命名"}, "fee": {"type": "number", "format": "double", "title": "未命名"}, "cost": {"type": "number", "format": "double", "title": "未命名"}, "elapsedTime": {"type": "integer", "format": "int64", "title": "未命名"}, "authDate": {"type": "string", "title": "未命名"}, "requestContent": {"type": "string", "title": "未命名"}, "requestDate": {"type": "string", "title": "未命名"}, "responseContent": {"type": "string", "title": "未命名"}, "responseDate": {"type": "string", "title": "未命名"}, "returnCode": {"type": "string", "title": "未命名"}, "returnMsg": {"type": "string", "title": "未命名"}, "remark": {"type": "string", "title": "未命名"}}}}, "x-api-group": "test"}