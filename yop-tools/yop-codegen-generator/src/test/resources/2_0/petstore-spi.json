{"swagger": "2.0", "info": {"description": "识别服务之个人身份证OCR服务", "version": "1.0.0", "title": "个人身份证OCR服务-识别服务", "termsOfService": "https://open.yeepay.com/yop-mbr/api/", "contact": {"name": "创新应用部基础应用组", "email": "<EMAIL>"}, "license": {"name": "Apache 2.0", "url": "http://www.apache.org/licenses/LICENSE-2.0.html"}}, "host": "open.yeepay.com", "basePath": "yop-center/rest/v1.0", "schemes": ["https"], "paths": {"/ocr/idcard": {"post": {"tags": ["idcardOcr"], "summary": "个人身份证OCR服务", "description": "个人身份证OCR服务", "operationId": "idcard_ocr", "consumes": ["application/json"], "produces": ["application/json"], "parameters": [{"in": "query", "name": "requestFlowId", "description": "请求流水标识", "required": true, "type": "string"}, {"in": "query", "name": "requestIP", "description": "请求IP", "required": false, "type": "string", "defaultValue": "127.0.0.1"}, {"in": "query", "name": "requestIdentification", "description": "请求者标示", "required": false, "type": "string"}, {"in": "query", "name": "bankCardImageBase64", "description": "银行卡图片(base64格式)", "required": false, "type": "string"}, {"in": "query", "name": "bankCardImage", "description": "银行卡图片(二进制格式)", "required": false, "type": "string"}], "responses": {"405": {"description": "Invalid input"}}, "security": [{"yop_app_key": [], "petstore_auth": ["write:pets", "read:pets"]}]}}}, "securityDefinitions": {"api_key": {"type": "<PERSON><PERSON><PERSON><PERSON>", "name": "api_key", "in": "header"}, "petstore_auth": {"type": "oauth2", "authorizationUrl": "http://open.yeepay.com/yop-center/oauth/token", "flow": "implicit", "scopes": {"write:pets": "modify pets in your account", "read:pets": "read your pets"}}}}