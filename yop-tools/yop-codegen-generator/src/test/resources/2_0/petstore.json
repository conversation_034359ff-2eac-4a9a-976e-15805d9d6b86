{"swagger": "2.0", "info": {"title": "个人身份证OCR服务-识别服务", "version": "3.0", "description": "识别服务之个人身份证OCR服务", "termsOfService": "https://open.yeepay.com/", "contact": {"name": "创新应用部基础应用组", "email": "<EMAIL>"}, "license": {"name": "Apache 2.0", "url": "http://www.apache.org/licenses/LICENSE-2.0.html"}}, "host": "open.yeepay.com", "basePath": "/yop-center/rest/v3.0", "schemes": ["https"], "x-api-group": "ocr", "x-service-group": ["ocr"], "security": [{"YOP-OAUTH2 ": ["write:pets", "read:pets"]}], "paths": {"/ocr/idcard": {"post": {"tags": ["OCR"], "summary": "个人身份证OCR服务", "description": "个人身份证OCR服务", "operationId": "ocr_idcard", "consumes": ["application/json"], "produces": ["application/json"], "parameters": [{"in": "query", "name": "requestFlowId", "description": "请求流水标识", "required": true, "type": "string"}, {"in": "query", "name": "requestIP", "description": "请求IP", "required": false, "type": "string"}, {"in": "query", "name": "requestIdentification", "description": "请求者标示", "required": false, "type": "string"}, {"in": "query", "name": "bankCardImageBase64", "description": "银行卡图片(base64格式)", "required": false, "type": "string"}, {"in": "query", "name": "bankCardImage", "description": "银行卡图片(二进制格式)", "required": false, "type": "string"}], "responses": {"200": {"description": "Success", "schema": {"type": "array", "items": {"$ref": "#/definitions/IdcardOCRResult"}}, "examples": {"application/json": "11"}}, "400": {"description": "Status 400"}, "405": {"description": "Invalid input"}}, "security": [{"YOP-HMAC-SHA256": []}]}}}, "securityDefinitions": {"YOP-HMAC-SHA256": {"type": "YOP-HMAC-SHA", "name": "YOP-HMAC-SHA256", "in": "query"}, "YOP-RSA2048-SHA256": {"type": "YOP-RSA-SHA", "name": "YOP-RSA2048-SHA256", "in": "header"}, "YOP-OAUTH2": {"type": "oauth2", "authorizationUrl": "http://open.yeepay.com/yop-center/oauth/token", "flow": "implicit", "scopes": {"write:pets": "modify pets in your account", "read:pets": "read your pets"}}}, "definitions": {"IdcardOCRResult": {"type": "object", "properties": {"idCardNumber": {"type": "string", "description": "身份证号码"}, "name": {"type": "string", "description": "姓名"}, "nation": {"type": "string", "description": "民族"}, "birthDate": {"type": "date", "description": "出生年月"}, "address": {"type": "string", "description": "住址"}, "issueDate": {"type": "date", "description": "签发日期"}, "validDate": {"type": "date", "description": "User Status"}, "issueAuthority": {"type": "string"}, "headPortrait": {"type": "string"}, "croppedImage": {"type": "string"}}}}}