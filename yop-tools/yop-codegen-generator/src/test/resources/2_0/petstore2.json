{"swagger": "2.0", "info": {"title": "个人身份证OCR服务-识别服务", "version": "3.0", "description": "识别服务之个人身份证OCR服务", "termsOfService": "https://open.yeepay.com/", "contact": {"name": "创新应用部基础应用组", "email": "<EMAIL>"}, "license": {"name": "Apache 2.0", "url": "http://www.apache.org/licenses/LICENSE-2.0.html"}}, "host": "open.yeepay.com", "basePath": "/yop-center", "schemes": ["https"], "x-api-group": "ocr", "x-service-group": ["ocr"], "paths": {"/rest/v3.0/ocr/idcard": {"post": {"tags": ["OCR"], "summary": "个人身份证OCR服务", "description": "个人身份证OCR服务", "operationId": "ocr_idcard", "consumes": ["application/json"], "produces": ["application/json"], "parameters": [{"name": "idCardOcrParams", "in": "body", "description": "idCardOcrParams", "required": true, "schema": {"$ref": "#/definitions/IdCardOcrParams"}}], "responses": {"200": {"description": "Success", "schema": {"$ref": "#/definitions/IdCardOcrResult"}}, "400": {"description": "Status 400"}, "405": {"description": "Invalid input"}}, "security": [{"YOP-RSA2048-SHA256": []}]}}, "/rest/v3.0/ocr/bankcard": {"post": {"tags": ["OCR"], "summary": "个人身份证OCR服务", "description": "个人身份证OCR服务", "operationId": "ocr_bankcard", "consumes": ["application/json"], "produces": ["application/json"], "parameters": [{"name": "bankCardOcrParams", "in": "body", "description": "bankCardOcrParams", "required": true, "schema": {"$ref": "#/definitions/BankCardOcrParams"}}], "responses": {"200": {"description": "Success", "schema": {"type": "array", "items": {"$ref": "#/definitions/BankCardOcrResult"}}}, "400": {"description": "Status 400"}, "405": {"description": "Invalid input"}}, "security": [{"YOP-RSA2048-SHA256": []}]}}}, "securityDefinitions": {"YOP-RSA2048-SHA256": {"type": "<PERSON><PERSON><PERSON><PERSON>", "name": "YOP-RSA2048-SHA256", "in": "header"}}, "definitions": {"IdCardOcrParams": {"type": "object", "properties": {"requestFlowId": {"description": "请求流水标识", "required": true, "type": "string"}, "requestIP": {"description": "请求IP", "required": false, "type": "array", "items": {"type": "string"}}, "requestIdentification": {"description": "请求者标示", "required": false, "type": "string"}, "idCardImageBase64": {"description": "身份证图片(base64格式)", "required": false, "type": "string"}}}, "IdCardOcrResult": {"type": "object", "properties": {"idCardNumber": {"type": "string", "description": "身份证号码"}, "name": {"type": "string", "description": "姓名"}, "nation": {"type": "string", "description": "民族"}, "birthDate": {"type": "date", "description": "出生年月"}, "address": {"type": "string", "description": "住址"}, "issueDate": {"type": "date", "description": "签发日期"}, "validDate": {"type": "date", "description": "User Status"}, "issueAuthority": {"type": "string"}, "headPortrait": {"type": "string"}, "croppedImage": {"type": "string"}, "testRef": {"$ref": "#/definitions/BankCardOcrResult"}}}, "BankCardOcrParams": {"type": "object", "properties": {"requestFlowId": {"description": "请求流水标识", "required": true, "type": "string"}, "requestIP": {"description": "请求IP", "required": false, "type": "string"}, "requestIdentification": {"description": "请求者标示", "required": false, "type": "string"}, "idCardImageBase64": {"description": "身份证图片(base64格式)", "required": false, "type": "string"}}}, "BankCardOcrResult": {"type": "object", "properties": {"idCardNumber": {"type": "string", "description": "身份证号码"}, "name": {"type": "string", "description": "姓名"}, "nation": {"type": "string", "description": "民族"}, "birthDate": {"type": "date", "description": "出生年月"}, "address": {"type": "string", "description": "住址"}, "issueDate": {"type": "date", "description": "签发日期"}, "validDate": {"type": "date", "description": "User Status"}, "issueAuthority": {"type": "string"}, "headPortrait": {"type": "string"}, "croppedImage": {"type": "string"}}}}}