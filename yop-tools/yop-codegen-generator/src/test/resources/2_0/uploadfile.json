{"swagger": "2.0", "info": {"description": "识别服务之个人身份证OCR服务", "version": "1.0.0", "title": "个人身份证OCR服务-识别服务", "termsOfService": "https://open.yeepay.com/yop-mbr/api/", "contact": {"name": "创新应用部基础应用组", "email": "<EMAIL>"}, "license": {"name": "Apache 2.0", "url": "http://www.apache.org/licenses/LICENSE-2.0.html"}}, "host": "open.yeepay.com", "basePath": "/rest/v1.0", "schemes": ["https"], "paths": {"/pet/{petId}/uploadImage": {"post": {"tags": ["pet"], "summary": "uploads an image", "description": "", "operationId": "uploadFile", "consumes": ["multipart/form-data"], "produces": ["application/json", "application/xml"], "parameters": [{"name": "petId", "in": "path", "description": "ID of pet to update", "required": true, "type": "integer", "format": "int64"}, {"name": "additionalMetadata", "in": "formData", "description": "Additional data to pass to server", "required": false, "type": "string"}, {"name": "file", "in": "formData", "description": "file to upload", "required": false, "type": "file"}], "responses": {"default": {"description": "successful operation"}}, "security": [{"petstore_auth": ["write:pets", "read:pets"]}]}}}}