{"openapi": "3.0.1", "info": {"title": "认证", "description": ""}, "security": [{"YOP-RSA2048-SHA256": []}], "tags": [{"name": "auth", "description": "分组"}], "paths": {"/rest/v2.0/auth/idcard": {"post": {"tags": ["auth"], "summary": "身份证实名认证服务", "description": "", "operationId": "idcard", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/RequestIdCardAuthDTO"}}}, "required": true}, "responses": {"200": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/AuthIdCardResultDTO"}}}}}, "security": [{"YOP-RSA2048-SHA256": []}], "x-yop-apigateway-api-type": "COMMON"}}}, "components": {"schemas": {"AuthIdCardResultDTO": {"type": "object", "properties": {"orderId": {"type": "string"}, "requestFlowId": {"type": "string"}, "authType": {"type": "string"}, "authInterfaceType": {"type": "string"}, "authInterfaceId": {"type": "integer", "format": "int32"}, "bottomInterface": {"type": "string"}, "status": {"type": "string"}, "fee": {"type": "number"}, "cost": {"type": "number"}, "externalOrderId": {"type": "string"}, "remark": {"type": "string"}, "channelReturnCode": {"type": "string"}, "channelReturnMsg": {"type": "string"}, "encryptMsg": {"type": "string"}, "invokeRecords": {"type": "array", "items": {"$ref": "#/components/schemas/InvokeRecord"}}, "name": {"type": "string"}, "idCardNumber": {"type": "string"}, "address": {"type": "string"}, "photo": {"type": "string"}}}, "InvokeRecord": {"type": "object", "properties": {"orderId": {"type": "string"}, "externalOrderId": {"type": "string"}, "authType": {"type": "string"}, "authInterfaceId": {"type": "integer", "format": "int32"}, "bottomInterface": {"type": "string"}, "authStatus": {"type": "string"}, "fee": {"type": "number"}, "cost": {"type": "number"}, "elapsedTime": {"type": "integer", "format": "int32"}, "authDate": {"type": "string"}, "requestContent": {"type": "string"}, "requestDate": {"type": "string"}, "responseContent": {"type": "string"}, "responseDate": {"type": "string"}, "returnCode": {"type": "string"}, "returnMsg": {"type": "string"}, "remark": {"type": "string"}}}, "RequestIdCardAuthDTO": {"type": "object", "properties": {"requestSystem": {"type": "string", "format": "merchant-no"}, "requestCustomerId": {"type": "string"}, "requestFlowId": {"type": "string"}, "requestIP": {"type": "string"}, "requestIdentification": {"type": "string"}, "authType": {"type": "string"}, "authMethod": {"type": "string"}, "channelName": {"type": "string"}, "repositoryAvailableDays": {"type": "integer", "format": "int32"}, "encryptMsg": {"type": "string"}, "requestSystemId": {"type": "integer", "format": "int32"}, "idCardNumber": {"type": "string"}, "name": {"type": "string"}, "excludePhoto": {"type": "boolean"}}, "description": "方法签名第0个参数，请自行修改arg0等参数的名字"}}, "securitySchemes": {"YOP-RSA2048-SHA256": {"type": "<PERSON><PERSON><PERSON><PERSON>", "name": "Authorization", "in": "header", "x-yop-apigateway-auth-type": "YOP-RSA2048-SHA256"}}}}