{"openapi": "3.0.1", "info": {"title": "航旅自动出票（百亿扶摇)", "description": "<ul>\n<li>百亿扶摇是航旅针对出票做的自动出票项目，其打通了OTA派单、航司出票。</li>\n<li>air-auto-ticket-hessian</li>\n</ul>"}, "security": [{"YOP-RSA2048-SHA256_autoticket": []}], "tags": [{"name": "autoticket", "description": "分组"}], "paths": {"/rest/v1.0/autoticket/customer-config/manage-eterm-setting": {"post": {"tags": ["autoticket"], "summary": "配置Eterm信息", "description": "配置Eterm信息，用于PNR小编码转大编码", "operationId": "customerConfigManageEtermSetting", "requestBody": {"content": {"application/x-www-form-urlencoded": {"schema": {"type": "object", "properties": {"username": {"type": "string"}, "password": {"type": "string"}, "workAccount": {"type": "string"}, "workPasword": {"type": "string"}, "serverAddress": {"type": "string"}, "serverPort": {"type": "integer", "format": "int32"}, "officeCode": {"type": "string"}, "instructLimitCou": {"type": "integer", "format": "int32"}}}}}}, "responses": {"200": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/EtermSettingResponseDTO"}}}}}, "x-yop-apigateway-api-type": "COMMON"}}, "/rest/v1.0/autoticket/customer-config/query-eterm-setting": {"get": {"tags": ["autoticket"], "summary": "查询Eterm配置信息", "operationId": "customerConfigQueryEtermSetting", "parameters": [], "responses": {"200": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/QueryEtermSettingResponseDTO"}}}}}, "x-yop-apigateway-api-type": "COMMON"}}, "/rest/v1.0/autoticket/ticket-order/auto-ticket": {"post": {"tags": ["autoticket"], "summary": "单笔订单出票", "description": "单笔订单出票", "operationId": "ticketOrderAutoTicket", "requestBody": {"content": {"application/x-www-form-urlencoded": {"schema": {"type": "object", "properties": {"customerRequestId": {"type": "string"}, "pnr": {"type": "string"}, "flight2Code": {"type": "string"}, "ticketChannel": {"type": "string"}, "ticketPrice": {"type": "number", "format": "double"}, "totalTax": {"type": "number", "format": "double"}, "rebate": {"type": "number", "format": "double"}, "contactMobile": {"type": "string"}, "contactName": {"type": "string"}, "contactEmail": {"type": "string"}, "passengerList": {"type": "array", "items": {"type": "string"}}, "flightList": {"type": "array", "items": {"type": "string"}}}}}}}, "responses": {"200": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/AutoTicketResponseDTO"}}}}}, "x-yop-apigateway-api-type": "COMMON"}}, "/rest/v1.0/autoticket/ticket-order/query-order-detail": {"get": {"tags": ["autoticket"], "summary": "订单详情查询", "operationId": "ticketOrderQueryOrderDetail", "parameters": [{"name": "customerRequestNo", "in": "query", "description": "", "schema": {"type": "string"}}], "responses": {"200": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/QueryAutoTicketOrderResponseDTO"}}}}}, "x-yop-apigateway-api-type": "COMMON"}}}, "components": {"schemas": {"QueryEtermSettingResponseDTO": {"type": "object", "properties": {"retCode": {"type": "string"}, "retMsg": {"type": "string"}, "etermSettingList": {"type": "array", "items": {"$ref": "#/components/schemas/EtermSettingBean"}}}}, "PassengerBean": {"type": "object", "properties": {"name": {"type": "string"}, "type": {"type": "string"}, "cardType": {"type": "string"}, "birthday": {"type": "string"}, "gender": {"type": "string"}, "cardId": {"type": "string"}, "contactPhone": {"type": "string"}, "ticketNo": {"type": "string"}}}, "QueryAutoTicketOrderResponseDTO": {"type": "object", "properties": {"retCode": {"type": "string"}, "retMsg": {"type": "string"}, "orderStatus": {"type": "string"}, "pnr": {"type": "string"}, "customerRequestNo": {"type": "string"}, "payPrice": {"type": "number", "format": "double"}, "flightList": {"type": "array", "items": {"$ref": "#/components/schemas/FlightBean"}}, "passengerList": {"type": "array", "items": {"$ref": "#/components/schemas/PassengerBean"}}}}, "EtermSettingResponseDTO": {"type": "object", "properties": {"retCode": {"type": "string"}, "retMsg": {"type": "string"}}}, "AutoTicketResponseDTO": {"type": "object", "properties": {"retCode": {"type": "string"}, "retMsg": {"type": "string"}, "orderStatus": {"type": "string"}, "pnr": {"type": "string"}, "customerRequestNo": {"type": "string"}, "payPrice": {"type": "number"}, "flightList": {"type": "array", "items": {"$ref": "#/components/schemas/FlightBean"}}, "passengerList": {"type": "array", "items": {"$ref": "#/components/schemas/PassengerBean"}}}}, "FlightBean": {"type": "object", "properties": {"departureCode": {"type": "string"}, "arrivalCode": {"type": "string"}, "departureTime": {"type": "string"}, "arrivalTime": {"type": "string"}, "cabinCls": {"type": "string"}, "flightNo": {"type": "string"}}}, "EtermSettingBean": {"type": "object", "properties": {"id": {"type": "integer", "format": "int32"}, "username": {"type": "string"}, "password": {"type": "string"}, "workAccount": {"type": "string"}, "workPasword": {"type": "string"}, "serverAddress": {"type": "string"}, "serverPort": {"type": "integer", "format": "int32"}, "officeCode": {"type": "string"}, "instructLimitCount": {"type": "integer", "format": "int32"}, "status": {"type": "string"}}}}, "securitySchemes": {"YOP-RSA2048-SHA256_autoticket": {"type": "<PERSON><PERSON><PERSON><PERSON>", "name": "Authorization", "in": "header", "x-yop-apigateway-auth-type": "YOP-RSA2048-SHA256"}}}}