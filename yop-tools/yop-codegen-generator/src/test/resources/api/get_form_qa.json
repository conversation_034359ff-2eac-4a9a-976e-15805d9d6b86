{"openapi": "3.0.1", "info": {"title": "测试大王", "version": "1.0"}, "security": [{"YOP-SM2-SM3_test-wdc": []}, {"YOP-RSA2048-SHA256_test-wdc": []}], "tags": [{"name": "test-wdc", "description": "分组"}], "paths": {"/rest/v1.0/test-wdc/new/api/notconfig": {"get": {"tags": ["test-wdc"], "summary": "新版APIwdc组分组不在统一配置中1", "description": "www.baidu.com", "operationId": "newApiNotconfig", "parameters": [{"name": "string", "in": "query", "description": "<p><a href=\"http://www.baidu.com\">歌词是123</a></p>\n<p><a href=\"http://www.baidu.com\">歌词是</a></p>\n<p>aaa</p>\n<p><a title=\"123\" href=\"123\">123</a></p>", "schema": {"exclusiveMaximum": true, "exclusiveMinimum": true, "type": "string", "format": "date-time"}, "examples": {"zheshisha": {"description": "nonieno", "value": "{\n'string':'hello'\n}"}}}, {"name": "i", "in": "query", "description": "<p>integer</p>", "required": true, "schema": {"maximum": 3434, "exclusiveMaximum": true, "minimum": 1, "exclusiveMinimum": true, "type": "integer", "format": "int64", "default": 323, "x-yop-api-param-condition": {"conditionRequired": false, "type": "PLAIN", "value": ""}}, "examples": {"i": {"description": "integer类型", "value": "{\n'i':22\n}"}}}, {"name": "f", "in": "query", "description": "FLOAT", "schema": {"maximum": 999.9, "exclusiveMaximum": true, "minimum": 1.1, "exclusiveMinimum": true, "type": "number", "format": "float"}}, {"name": "d", "in": "query", "description": "<p>double</p>", "required": true, "schema": {"maximum": 9999.99, "exclusiveMaximum": true, "minimum": 1.0, "exclusiveMinimum": true, "type": "number", "format": "double", "x-yop-api-param-condition": {"conditionRequired": false, "type": "PLAIN", "value": ""}}}, {"name": "testDate", "in": "query", "description": "<p>yyyy-MM-dd HH:mm:ss</p>", "schema": {"exclusiveMaximum": true, "exclusiveMinimum": true, "type": "string", "format": "date-time"}}, {"name": "sP", "in": "query", "schema": {"exclusiveMaximum": true, "exclusiveMinimum": true, "type": "string", "format": "date-time"}}, {"name": "a", "in": "query", "description": "<p>这里时候参数的描述，写两行吧<br />这里时候参数的描述，写两行吧</p>", "schema": {"exclusiveMaximum": true, "exclusiveMinimum": true, "maxLength": 10, "type": "string", "x-yop-api-param-condition": {"conditionRequired": true, "type": "PLAIN", "value": "i不为空时"}}}, {"name": "c", "in": "query", "description": "<p>xx</p>", "schema": {"exclusiveMaximum": true, "exclusiveMinimum": true, "type": "array", "items": {"exclusiveMaximum": true, "exclusiveMinimum": true, "type": "boolean", "format": "boolean", "enum": []}, "x-yop-api-param-condition": {"conditionRequired": true, "type": "PLAIN", "value": "xxx数组时, 一点儿也不好看"}}}], "responses": {"200": {"description": "返回", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/test-wdc.Fanhua"}, "examples": {"这是随意值": {"description": "响应参数", "value": "{\n'TEST':'GWG'\n}"}}}}}}, "x-yop-apigateway-api-name": "NewApiwdcGroupNotConfig", "x-yop-apigateway-api-parameter-handling": "PASSTHROUGH", "x-yop-apigateway-api-type": "COMMON", "x-yop-apigateway-auth-inherited": true, "x-yop-apigateway-api-option-rule": [{"name": "IDEMPOTENT", "config": {"supported": true, "useAllParams": false, "params": ["string"], "hasBizError": false, "bizErrorCode": "001"}}, {"name": "SANDBOX", "config": {"supported": false}}], "x-yop-apigateway-request-encrypt": null, "x-yop-apigateway-response-encrypt": null, "x-yop-apigateway-sensitive-variable": ["$request.query.i"]}}}, "components": {"schemas": {"test-wdc.Fanhua": {"type": "object", "properties": {"string": {"exclusiveMaximum": true, "exclusiveMinimum": true, "maxLength": 111, "minLength": 2, "type": "string", "default": "字符串测试1"}, "i": {"maximum": 11, "exclusiveMaximum": true, "minimum": 1, "exclusiveMinimum": true, "type": "integer", "description": "Integer类型", "format": "int64", "default": 111}, "bo": {"exclusiveMaximum": true, "exclusiveMinimum": true, "type": "boolean", "description": "布尔类型", "default": true}, "l": {"maximum": 121212, "exclusiveMaximum": true, "minimum": 1, "exclusiveMinimum": true, "type": "number", "description": "目测是long"}, "d": {"maximum": 999999.98, "exclusiveMaximum": true, "minimum": 0.21212, "exclusiveMinimum": true, "type": "number", "description": "double", "format": "double"}, "f": {"maximum": 999999.66, "exclusiveMaximum": true, "minimum": 1.11, "exclusiveMinimum": true, "type": "number", "description": "float类型", "format": "float"}, "array": {"exclusiveMaximum": true, "exclusiveMinimum": true, "maxItems": 99, "minItems": 1, "type": "array", "description": "数组", "items": {"exclusiveMaximum": true, "exclusiveMinimum": true, "maxLength": 22222, "minLength": 1, "type": "string", "description": "问问", "format": "string", "default": "default"}}}, "description": "测试API泛化1"}}, "securitySchemes": {"YOP-SM2-SM3_test-wdc": {"type": "<PERSON><PERSON><PERSON><PERSON>", "name": "Authorization", "in": "header", "x-yop-apigateway-auth-type": "YOP-SM2-SM3"}, "YOP-RSA2048-SHA256_test-wdc": {"type": "<PERSON><PERSON><PERSON><PERSON>", "name": "Authorization", "in": "header", "x-yop-apigateway-auth-type": "YOP-RSA2048-SHA256"}}}, "x-yop-apigateway-group": "test-wdc"}