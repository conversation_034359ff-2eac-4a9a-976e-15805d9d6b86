{"openapi": "3.0.1", "info": {"title": "通知", "version": "1.0"}, "paths": {"/rest/v4.0/notifier/sms": {"post": {"summary": "短信通知", "tags": ["notifier"], "description": "短信通知", "operationId": "sms", "requestBody": {"description": "短信通知请求", "content": {"application/x-www-form-urlencoded": {"schema": {"required": ["content", "notifyRule", "recipients"], "type": "object", "properties": {"notifyRule": {"type": "string"}, "recipients": {"type": "array", "items": {"type": "string"}}, "content": {"type": "string"}, "scheduledTime": {"type": "string", "format": "date-time"}, "scheduledDate": {"type": "string", "format": "date"}, "notifyType": {"type": "string", "enum": ["VERIFY_CODE", "AD"]}, "controlInfo": {"type": "array", "items": {"type": "string", "enum": ["one", "two"]}}}}}}}, "responses": {"200": {"description": "返回", "content": {"application/json": {"schema": {"type": "string", "enum": ["SUCCESS", "FAILURE"]}}}}}, "callbacks": {"notify.status.report": {"$ref": "#/components/callbacks/notify.status.report"}}, "security": [{"YOP-RSA2048-SHA256": []}], "x-yop-apigateway-api-parameter-handling": "MAPPING", "x-yop-apigateway-api-type": "COMMON", "x-yop-apigateway-api-idempotent": true, "x-yop-apigateway-endpoint": {"type": "HTTP", "serviceName": "notifier-hessian_http", "systemParameters": [{"systemName": "#context.appKey", "backendName": "requestCustomerId", "location": "HEADER"}, {"systemName": "#header.requestId", "backendName": "flowNo", "location": "HEADER"}], "path": "notify/yop/sms", "method": "POST", "connectionTimeout": 10000, "readTimeout": 20000}}}, "/rest/v2.0/notifier/sms-notify": {"post": {"summary": "短信通知1", "tags": ["notifier"], "description": "短信通知1", "operationId": "sms_notify", "requestBody": {"description": "短信通知请求", "content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/NotifyRequest"}}}}}, "responses": {"200": {"description": "返回", "content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/NotifyResponse"}}}}}}, "callbacks": {"notify.status.report": {"$ref": "#/components/callbacks/notify.status.report"}}, "security": [{"YOP-RSA2048-SHA256_sms_notify": []}], "x-yop-apigateway-api-parameter-handling": "PASSTHROUGH", "x-yop-apigateway-api-type": "COMMON", "x-yop-apigateway-endpoint": {"type": "HTTP", "serviceName": "notifier-hessian_http", "path": "/notify/sms-notify", "method": "POST", "connectionTimeout": 1000, "readTimeout": 2000}}}}, "components": {"schemas": {"MtStatus": {"type": "string", "enum": ["S", "F"]}, "NotifyRequest": {"type": "object", "properties": {"flowNo": {"type": "string"}, "requestCustomerId": {"type": "string"}, "userName": {"type": "string"}, "notifyRuleName": {"type": "string"}, "sign": {"type": "string"}, "recipients": {"type": "array", "items": {"type": "string", "format": "mobile"}}, "messageParams": {"type": "object", "additionalProperties": {"type": "string"}}, "notifyType": {"type": "string", "enum": ["FAST", "SLOW"]}, "options": {"$ref": "#/components/schemas/NotifyOption"}}, "description": "短信通知请求"}, "NotifyOption": {"type": "object", "properties": {"scheduledSendTime": {"type": "string", "format": "date-time"}, "scheduledDate": {"type": "string", "format": "date"}}, "description": "通知选项"}, "MtReport": {"type": "object", "properties": {"orderId": {"type": "string"}, "status": {"$ref": "#/components/schemas/MtStatus"}}, "description": "状态报告"}, "NotifyResponse": {"type": "object", "properties": {"orderId": {"type": "string"}, "status": {"type": "string", "enum": ["SUCCESS", "FAILURE"]}, "remark": {"type": "string"}}, "description": "通知返回"}}, "securitySchemes": {"YOP-RSA2048-SHA256": {"type": "<PERSON><PERSON><PERSON><PERSON>", "name": "Authorization", "in": "header", "x-yop-apigateway-auth-type": "YOP-RSA2048-SHA256"}, "YOP-RSA2048-SHA256_sms_notify": {"type": "<PERSON><PERSON><PERSON><PERSON>", "name": "Authorization", "in": "header", "x-yop-apigateway-auth-force-encrypt-app-date": "2019-07-17", "x-yop-apigateway-auth-type": "YOP-RSA2048-SHA256", "x-yop-apigateway-auth-authority": ["CFCA"], "x-yop-apigateway-need-encrypt": true}}, "callbacks": {"notify.status.report": {"${params.callbackUrl}": {"post": {"summary": "通知状态报告1", "description": "通知状态报告回调1", "requestBody": {"description": "通知状态回调请求体", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/MtReport"}}}}, "responses": {"200": {"description": "返回", "content": {"text/plain": {"schema": {"type": "string"}, "examples": {"success": {"summary": "成功返回", "value": "success"}}}}}}}}}}}}