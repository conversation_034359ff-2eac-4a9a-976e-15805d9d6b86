{"openapi": "3.0.1", "info": {"title": "通知", "version": "1.0"}, "security": [{"YOP-RSA2048-SHA256": []}, {"YOP-HMAC-AES128": []}, {"YOP-OAUTH2_notifier": []}], "tags": [{"name": "notifier", "description": "分组"}], "paths": {"/rest/v1.0/notifier/doc-products": {"post": {"tags": ["notifier"], "summary": "更新测试API", "description": "", "operationId": "doc_products", "requestBody": {"content": {"application/x-www-form-urlencoded": {"schema": {"required": ["notifyRule"], "type": "object", "properties": {"notifyRule": {"type": "string"}, "recipients": {"type": "array", "items": {"exclusiveMaximum": true, "exclusiveMinimum": true, "type": "string", "format": "mobile"}}, "content": {"type": "string"}}}}}}, "responses": {"200": {"description": "返回", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/NotifyResponse"}}}}}, "callbacks": {"notifier.test": {"$ref": "#/components/callbacks/notifier.test"}}, "x-yop-apigateway-api-parameter-handling": "MAPPING", "x-yop-apigateway-api-type": "COMMON", "x-yop-apigateway-auth-inherited": true, "x-yop-apigateway-endpoint": {"type": "HTTP", "serviceName": "notifier-hessian_http", "path": "/notify/yop/sms", "method": "POST", "connectionTimeout": 200, "readTimeout": 100}}}, "/rest/v1.0/notifier/dd": {"post": {"tags": ["notifier"], "summary": "d", "description": "", "operationId": "dd", "requestBody": {"content": {"application/x-www-form-urlencoded": {"schema": {"type": "object", "properties": {"notifyRule": {"type": "string"}, "recipients": {"type": "array", "items": {"exclusiveMaximum": true, "exclusiveMinimum": true, "type": "string", "format": "string"}}, "content": {"type": "string"}}}}}}, "responses": {"200": {"description": "返回", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/NotifyOptionTest"}}}}}, "x-yop-apigateway-api-parameter-handling": "PASSTHROUGH", "x-yop-apigateway-api-type": "COMMON", "x-yop-apigateway-auth-inherited": true, "x-yop-apigateway-endpoint": {"type": "HTTP", "serviceName": "notifier-hessian_http", "path": "/notify/yop/sms", "method": "POST", "connectionTimeout": 2000, "readTimeout": 1000}}}, "/rest/v1.0/notifier/d": {"post": {"tags": ["notifier"], "summary": "测完即删", "description": "", "operationId": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/TestLeDTO"}}}}, "responses": {"200": {"description": "返回", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/NotifyOptionTest"}}}}}, "x-yop-apigateway-api-parameter-handling": "PASSTHROUGH", "x-yop-apigateway-api-type": "COMMON", "x-yop-apigateway-auth-inherited": true, "x-yop-apigateway-endpoint": {"type": "HTTP", "serviceName": "notifier-hessian_http", "path": "/yop/notify/sms", "method": "POST", "connectionTimeout": 200, "readTimeout": 100}}}, "/rest/v1.0/notifier/doc": {"post": {"tags": ["notifier"], "summary": "API变更测试", "description": "", "operationId": "doc", "requestBody": {"content": {"application/x-www-form-urlencoded": {"schema": {"type": "object", "properties": {"arg": {"type": "string"}}}}}}, "responses": {"200": {"description": "返回", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/NotifyOption"}}}}}, "x-yop-apigateway-api-parameter-handling": "PASSTHROUGH", "x-yop-apigateway-api-type": "COMMON", "x-yop-apigateway-auth-inherited": true, "x-yop-apigateway-endpoint": {"type": "HTTP", "serviceName": "notifier-hessian_http", "path": "http://doc/hessian", "method": "POST", "connectionTimeout": 200, "readTimeout": 100}}}, "/rest/v5.0/notifier/sms-notify-tb": {"get": {"tags": ["notifier"], "summary": "短信通知5_xx", "description": "短信通知_xx", "operationId": "sms_notify_xa", "parameters": [{"name": "notifyRule", "in": "query", "schema": {"type": "string", "x-yop-apigateway-test": "test"}, "x-yop-apigateway-backend-location": "QUERY", "x-yop-apigateway-backend-name": "notifyRuleX"}, {"name": "recipient", "in": "query", "schema": {"type": "array", "items": {"type": "string", "format": "mobile"}, "x-yop-apigateway-test": "test"}}, {"name": "content", "in": "query", "schema": {"type": "string", "x-yop-apigateway-test": "test"}}], "responses": {"302": {"description": "返回", "headers": {"location": {"description": "test", "schema": {"type": "string"}}}}}, "callbacks": {"notifier.status.report": {"$ref": "#/components/callbacks/notifier.status.report"}}, "security": [{"YOP-RSA2048-SHA256": []}], "x-yop-apigateway-api-parameter-handling": "MAPPING", "x-yop-apigateway-api-type": "COMMON", "x-yop-apigateway-api-idempotent": true, "x-yop-apigateway-endpoint": {"type": "HTTP", "serviceName": "notifier-hessian_http", "systemParameters": [{"systemName": "#context.appkey#", "backendName": "x-yop-appkey", "location": "HEADER"}, {"systemName": "#header.requestId#", "backendName": "x-yop-request-id", "location": "HEADER"}], "path": "/notify/sms-notify", "method": "POST", "connectionTimeout": 10000, "readTimeout": 20000}}}, "/rest/v5.0/notifier/sms-notify": {"post": {"tags": ["notifier"], "summary": "短信通知6_xx", "description": "短信通知_xx", "operationId": "sms_notify_y", "requestBody": {"description": "短信通知请求5", "content": {"application/x-www-form-urlencoded": {"schema": {"required": ["content", "notifyRule", "recipients"], "type": "object", "properties": {"flowId": {"type": "string", "x-yop-apigateway-test": "test"}, "notifyRule": {"type": "string", "x-yop-apigateway-backend-location": "BODY", "x-yop-apigateway-backend-name": "notifyRule", "x-yop-apigateway-test": "test"}, "recipients": {"type": "array", "items": {"type": "string", "format": "mobile"}, "x-yop-apigateway-test": "test"}, "content": {"type": "string", "x-yop-apigateway-test": "test"}, "textxxx": {"type": "string"}}}}}}, "responses": {"302": {"description": "返回", "headers": {"location": {"description": "test", "schema": {"type": "string"}}}}}, "callbacks": {"notifier.status.report": {"$ref": "#/components/callbacks/notifier.status.report"}}, "security": [{"YOP-RSA2048-SHA256": []}], "x-yop-apigateway-api-parameter-handling": "MAPPING", "x-yop-apigateway-api-type": "COMMON", "x-yop-apigateway-api-idempotent": true, "x-yop-apigateway-endpoint": {"type": "HTTP", "serviceName": "notifier-hessian_http", "path": "/notify/sms-notify", "method": "POST", "connectionTimeout": 10000, "readTimeout": 20000}}}, "/rest/v5.0/notifier/sms-notify-tt": {"get": {"tags": ["notifier"], "summary": "短信通知5", "description": "短信通知", "operationId": "sms_notify_x", "parameters": [{"name": "notifyRule", "in": "query", "schema": {"type": "string"}, "x-yop-apigateway-backend-location": "QUERY", "x-yop-apigateway-backend-name": "notifyRuleX"}, {"name": "recipient", "in": "query", "schema": {"type": "array", "items": {"type": "string", "format": "mobile"}}}, {"name": "content", "in": "query", "schema": {"type": "string"}}], "responses": {"200": {"description": "返回", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/NotifyResponse"}}}}}, "callbacks": {"notifier.status.report": {"$ref": "#/components/callbacks/notifier.status.report"}}, "x-yop-apigateway-api-parameter-handling": "MAPPING", "x-yop-apigateway-api-type": "COMMON", "x-yop-apigateway-api-idempotent": true, "x-yop-apigateway-auth-inherited": true, "x-yop-apigateway-endpoint": {"type": "HTTP", "serviceName": "notifier-hessian_http", "systemParameters": [{"systemName": "#context.appkey#", "backendName": "x-yop-appkey", "location": "HEADER"}, {"systemName": "#header.requestId#", "backendName": "x-yop-request-id", "location": "HEADER"}], "path": "/notify/sms-notify", "method": "POST", "connectionTimeout": 10000, "readTimeout": 20000}}}, "/rest/v4.0/notifier/sms": {"post": {"tags": ["notifier"], "summary": "短信发送", "description": "<p>短信通知</p>", "operationId": "sms_notify", "requestBody": {"description": "短信通知请求", "content": {"application/x-www-form-urlencoded": {"schema": {"required": ["notifyRule"], "type": "object", "properties": {"notifyRule": {"type": "string"}, "recipients": {"type": "array", "items": {"type": "string", "format": "mobile"}}, "content": {"type": "string"}}}}}}, "responses": {"200": {"description": "返回", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/NotifyResponse"}, "examples": {"test": {"description": "s", "value": "ss"}}}}}}, "x-yop-apigateway-api-parameter-handling": "MAPPING", "x-yop-apigateway-api-type": "COMMON", "x-yop-apigateway-api-idempotent": true, "x-yop-apigateway-auth-inherited": true, "x-yop-apigateway-endpoint": {"type": "HTTP", "serviceName": "notifier-hessian_http", "systemParameters": [{"systemName": "#context.appkey#", "backendName": "x-yop-appkey", "location": "HEADER"}, {"systemName": "#header.requestId#", "backendName": "x-yop-request-id", "location": "HEADER"}], "path": "/notify/yop/sms", "method": "POST", "connectionTimeout": 10000, "readTimeout": 20000}}}, "/rest/v3.0/notifier/sms-notify": {"post": {"tags": ["notifier"], "summary": "短信通知3", "description": "", "operationId": "sms_notify_xxx", "requestBody": {"description": "短信通知请求", "content": {"application/x-www-form-urlencoded": {"schema": {"required": ["content", "notifyRule", "recipients"], "type": "object", "properties": {"flowId": {"type": "string"}, "notifyRule": {"type": "string"}, "recipients": {"type": "array", "items": {"type": "string", "format": "mobile"}}, "content": {"type": "string"}}}, "examples": {"12121222": {"description": "2", "value": "12"}}}}}, "responses": {"200": {"description": "返回", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/NotifyResponse"}, "examples": {"2": {"description": "12121", "value": "12"}, "12": {"description": "12", "value": "12"}}}}}}, "callbacks": {"notifier.status.report": {"$ref": "#/components/callbacks/notifier.status.report"}}, "x-yop-apigateway-api-parameter-handling": "PASSTHROUGH", "x-yop-apigateway-api-type": "COMMON", "x-yop-apigateway-auth-inherited": true, "x-yop-apigateway-endpoint": {"type": "HTTP", "serviceName": "notifier-hessian_http", "constantParameters": [], "systemParameters": [], "path": "/notify/sms-notify", "method": "POST", "connectionTimeout": 1000, "readTimeout": 2000}, "x-yop-apigateway-biz-order-variable": "$request.body#flowId", "x-yop-apigateway-sensitive-variable": ["$request.body#recipients"]}}, "/rest/v2.0/notifier/yop/mail": {"post": {"tags": ["notifier"], "summary": "短信通知", "description": "短信通知", "operationId": "mail_notify", "requestBody": {"description": "短信通知请求", "content": {"application/x-www-form-urlencoded": {"schema": {"required": ["content", "recipients"], "type": "object", "properties": {"notifyRuleTest1": {"type": "string", "x-yop-apigateway-backend-name": "notifyRule", "x-yop-apigateway-backend-location": "BODY"}, "recipients": {"type": "array", "items": {"type": "string", "format": "mail"}}, "content": {"type": "string"}}}}}}, "responses": {"200": {"description": "返回", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/NotifyResponse"}}}}}, "callbacks": {"notifier.status.report": {"$ref": "#/components/callbacks/notifier.status.report"}}, "x-yop-apigateway-api-parameter-handling": "MAPPING", "x-yop-apigateway-api-type": "COMMON", "x-yop-apigateway-api-idempotent": true, "x-yop-apigateway-auth-inherited": true, "x-yop-apigateway-endpoint": {"type": "HTTP", "serviceName": "notifier-hessian_http", "path": "/notify/yop/mail", "method": "POST", "connectionTimeout": 20000, "readTimeout": 20000}}}}, "components": {"schemas": {"NotifyOption": {"type": "object", "properties": {"scheduled_sendTime": {"type": "string", "format": "date-time"}}, "description": "通知选项"}, "NotifyOptionTest": {"type": "object", "properties": {"scheduledSendTime": {"type": "string", "format": "date-time"}}, "description": "通知选项"}, "TestLeDTO": {"type": "object", "properties": {"a": {"exclusiveMaximum": true, "exclusiveMinimum": true, "type": "integer", "format": "int32"}, "b": {"$ref": "#/components/schemas/NotifyOptionTest"}, "c": {"exclusiveMaximum": true, "exclusiveMinimum": true, "type": "string"}, "aaa": {"exclusiveMaximum": true, "exclusiveMinimum": true, "type": "string"}}, "description": "测试"}, "MtReport": {"type": "object", "properties": {"orderId": {"type": "string"}, "status": {"type": "string"}}, "description": "状态报告"}, "NotifyResponse": {"type": "object", "properties": {"orderId": {"type": "string", "description": "订单id"}, "status": {"type": "string", "description": "状态"}, "remark": {"type": "string", "description": "备注"}}, "description": "通知返回"}}, "securitySchemes": {"YOP-RSA2048-SHA256": {"type": "<PERSON><PERSON><PERSON><PERSON>", "name": "Authorization", "in": "header", "x-yop-apigateway-auth-type": "YOP-RSA2048-SHA256"}, "YOP-HMAC-AES128": {"type": "<PERSON><PERSON><PERSON><PERSON>", "name": "Authorization", "in": "header", "x-yop-apigateway-auth-type": "YOP-HMAC-AES128"}, "YOP-OAUTH2_notifier": {"type": "<PERSON><PERSON><PERSON><PERSON>", "name": "Authorization", "in": "header", "x-yop-apigateway-auth-type": "YOP-OAUTH2"}}, "callbacks": {"notifier.test": {"#app.callbackUrl": {"post": {"summary": "测试spi", "description": "测试", "requestBody": {"description": "测试", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/NotifyOptionTest"}}}}, "responses": {"200": {"description": "返回", "content": {"text/plain": {"schema": {"type": "string"}, "examples": {"success": {"summary": "成功返回", "value": "success"}}}}}}}}}, "notifier.status.report": {"${params.callbackUrl}": {"post": {"summary": "通知状态报告1", "description": "通知状态报告回调1", "requestBody": {"description": "通知状态回调请求体", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/MtReport"}}}}, "responses": {"200": {"description": "返回", "content": {"text/plain": {"schema": {"type": "string"}, "examples": {"success": {"summary": "成功返回", "value": "success"}}}}}}}}}}}, "x-yop-apigateway-group": "notifier"}