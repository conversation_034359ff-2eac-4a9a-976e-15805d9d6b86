{"openapi": "3.0.1", "info": {"title": "测试大王", "version": "1.0"}, "security": [{"YOP-SM2-SM3_test-wdc": []}, {"YOP-RSA2048-SHA256_test-wdc": []}], "tags": [{"name": "test-wdc", "description": "分组"}], "paths": {"/rest/v1.0/test-wdc/test-pojo-form": {"post": {"tags": ["test-wdc"], "summary": "testPojoForm", "description": "加个描述", "operationId": "testPojoForm", "requestBody": {"content": {"application/x-www-form-urlencoded": {"schema": {"type": "object", "properties": {"bigDecimalNum": {"type": "integer", "format": "int32", "x-yop-api-param-condition": {"conditionRequired": true, "type": "PLAIN", "value": "rwrwrw"}}, "longNum": {"type": "integer", "format": "int64"}, "intNum": {"type": "integer", "format": "int32"}, "doubleAmount": {"type": "number", "format": "double"}, "strList": {"type": "array", "description": "<p>测试array参数</p>", "items": {"exclusiveMaximum": true, "exclusiveMinimum": true, "maxLength": 10, "type": "string", "description": "<p>元素1</p>", "format": "merchant-no"}, "x-yop-api-param-condition": {"conditionRequired": true, "type": "PLAIN", "value": "测试数组条件必填"}}}}}}}, "responses": {"200": {"description": "返回", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/test-wdc.WrapperDTO"}}}}}, "x-yop-apigateway-api-name": "testPojoForm", "x-yop-apigateway-api-parameter-handling": "PASSTHROUGH", "x-yop-apigateway-api-type": "COMMON", "x-yop-apigateway-auth-inherited": true, "x-yop-apigateway-api-option-rule": [{"name": "IDEMPOTENT", "config": {"supported": false, "useAllParams": true, "params": [], "hasBizError": false, "bizErrorCode": ""}}, {"name": "SANDBOX", "config": {"supported": false}}], "x-yop-apigateway-request-encrypt": null, "x-yop-apigateway-response-encrypt": null}}}, "components": {"schemas": {"test-wdc.WrapperDTO": {"type": "object", "properties": {"strList": {"type": "array", "description": "未命名", "items": {"type": "string", "description": "未命名"}}, "doubleAmount": {"type": "number", "description": "未命名", "format": "double"}, "intNum": {"type": "integer", "description": "未命名", "format": "int32"}, "bigDecimalNum": {"type": "number", "description": "未命名"}, "longNum": {"type": "integer", "description": "未命名", "format": "int64"}}, "description": "未命名"}}, "securitySchemes": {"YOP-SM2-SM3_test-wdc": {"type": "<PERSON><PERSON><PERSON><PERSON>", "name": "Authorization", "in": "header", "x-yop-apigateway-auth-type": "YOP-SM2-SM3"}, "YOP-RSA2048-SHA256_test-wdc": {"type": "<PERSON><PERSON><PERSON><PERSON>", "name": "Authorization", "in": "header", "x-yop-apigateway-auth-type": "YOP-RSA2048-SHA256"}}}, "x-yop-apigateway-group": "test-wdc"}