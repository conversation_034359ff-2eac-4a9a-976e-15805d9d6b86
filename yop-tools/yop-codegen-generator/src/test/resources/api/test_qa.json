{"openapi": "3.0.1", "info": {"title": "测试YOP", "description": ""}, "security": [{"YOP-RSA2048-SHA256_test": []}], "tags": [{"name": "test", "description": "分组"}], "paths": {"/yos/v1.0/test/file-info/upload": {"post": {"tags": ["test"], "summary": "yop_yos上传测试", "operationId": "fileInfoUpload", "requestBody": {"content": {"multipart/form-data": {"schema": {"type": "object", "properties": {"notifyRule": {"type": "string"}, "recipients": {"type": "array", "items": {"type": "string"}}, "content": {"type": "string", "format": "binary"}}}}}}, "responses": {"200": {"content": {"application/json": {"schema": {"type": "string"}}}}}, "x-yop-apigateway-api-type": "FILE_UPLOAD"}}}, "components": {"securitySchemes": {"YOP-RSA2048-SHA256_test": {"type": "<PERSON><PERSON><PERSON><PERSON>", "name": "Authorization", "in": "header", "x-yop-apigateway-auth-type": "YOP-RSA2048-SHA256"}}}}