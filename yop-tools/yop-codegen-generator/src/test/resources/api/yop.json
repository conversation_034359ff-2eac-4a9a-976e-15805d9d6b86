{"openapi": "3.0.1", "info": {"title": "yop", "version": "1.0"}, "security": [{"YOP-RSA2048-SHA256": []}, {"YOP-HMAC-AES128": []}, {"YOP-OAUTH2_yop": []}], "tags": [{"name": "yop", "description": "分组"}], "paths": {"/rest/v1.0/yop/user/auth": {"post": {"tags": ["yop"], "summary": "插件端用户登录", "description": "", "operationId": "plugin_user_auth", "requestBody": {"description": "用户", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/PluginUserAuthParam"}}}}, "responses": {"200": {"description": "返回", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/PluginAccessToken"}}}}}, "security": [{"YOP-RSA2048-SHA256_plugin_user_auth": []}], "x-yop-apigateway-api-parameter-handling": "PASSTHROUGH", "x-yop-apigateway-api-type": "COMMON", "x-yop-apigateway-endpoint": {"type": "HTTP", "serviceName": "yop-sys-hessian_http", "systemParameters": [{"systemName": "#context.appkey#", "backendName": "x-yop-appkey", "location": "HEADER"}, {"systemName": "#header.requestIp#", "backendName": "x-yop-request-ip", "location": "HEADER"}], "path": "/plugin/user/auth", "method": "POST", "connectionTimeout": 1000, "readTimeout": 3000}}}, "/rest/v1.0/yop/user/captcha": {"post": {"tags": ["yop"], "summary": "插件端验证码", "description": "<p>插件端验证码</p>", "operationId": "plugin_user_captcha", "requestBody": {"description": "插件端用户验证码参数", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/PluginUserCaptchaParam"}}}}, "responses": {"200": {"description": "返回", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/PluginUserCaptcha"}}}}}, "security": [{"YOP-RSA2048-SHA256_plugin_user_captcha": []}, {"YOP-OAUTH2_plugin_user_captcha": []}], "x-yop-apigateway-api-parameter-handling": "PASSTHROUGH", "x-yop-apigateway-api-type": "COMMON", "x-yop-apigateway-endpoint": {"type": "HTTP", "serviceName": "yop-sys-hessian_http", "systemParameters": [{"systemName": "#context.oauth2.userid#", "backendName": "x-yop-oauth2-userid", "location": "HEADER"}], "path": "/plugin/user/captcha", "method": "POST", "connectionTimeout": 1000, "readTimeout": 3000}}}, "/rest/v1.0/yop/user/unauth": {"post": {"tags": ["yop"], "summary": "插件端用户注销", "description": "", "operationId": "plugin_user_unauth", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/PluginUserUnauthParam"}}}}, "responses": {"200": {"description": "返回", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/PluginRefreshToken"}}}}}, "security": [{"YOP-RSA2048-SHA256_plugin_user_unauth": []}], "x-yop-apigateway-api-parameter-handling": "PASSTHROUGH", "x-yop-apigateway-api-type": "COMMON", "x-yop-apigateway-endpoint": {"type": "HTTP", "serviceName": "yop-sys-hessian_http", "path": "/user/unauth", "method": "POST", "connectionTimeout": 1000, "readTimeout": 3000}}}, "/rest/v1.0/yop/user/refresh": {"post": {"tags": ["yop"], "summary": "插件端用户刷新", "description": "", "operationId": "plugin_user_refresh", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/PluginUserRefreshParam"}}}}, "responses": {"200": {"description": "返回", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/PluginAccessToken"}}}}}, "security": [{"YOP-RSA2048-SHA256_plugin_user_refresh": []}], "x-yop-apigateway-api-parameter-handling": "PASSTHROUGH", "x-yop-apigateway-api-type": "COMMON", "x-yop-apigateway-endpoint": {"type": "HTTP", "serviceName": "yop-sys-hessian_http", "path": "/user/refresh", "method": "POST", "connectionTimeout": 1000, "readTimeout": 3000}}}, "/rest/v1.0/yop/app/list": {"get": {"tags": ["yop"], "summary": "插件端应用列表", "description": "<p>插件端应用列表</p>", "operationId": "plugin_app_list", "parameters": [{"name": "none", "in": "query", "schema": {"exclusiveMaximum": true, "exclusiveMinimum": true, "type": "string"}}], "responses": {"200": {"description": "返回", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/PluginApps"}}}}}, "security": [{"YOP-OAUTH2_plugin_app_list": []}], "x-yop-apigateway-api-parameter-handling": "PASSTHROUGH", "x-yop-apigateway-api-type": "COMMON", "x-yop-apigateway-endpoint": {"type": "HTTP", "serviceName": "yop-sys-hessian_http", "systemParameters": [{"systemName": "#context.oauth2.userid#", "backendName": "x-yop-oauth2-userid", "location": "HEADER"}], "path": "/app/list", "method": "GET", "connectionTimeout": 1000, "readTimeout": 3000}}}, "/rest/v1.0/yop/app/detail": {"get": {"tags": ["yop"], "summary": "插件端应用详情", "description": "<p>插件端应用详情</p>", "operationId": "plugin_app_detail", "parameters": [{"name": "appId", "in": "query", "description": "应用标识", "schema": {"exclusiveMaximum": true, "exclusiveMinimum": true, "type": "string"}}], "responses": {"200": {"description": "返回", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/PluginApp"}}}}}, "security": [{"YOP-OAUTH2_plugin_app_detail": []}], "x-yop-apigateway-api-parameter-handling": "PASSTHROUGH", "x-yop-apigateway-api-type": "COMMON", "x-yop-apigateway-endpoint": {"type": "HTTP", "serviceName": "yop-sys-hessian_http", "systemParameters": [{"systemName": "#context.oauth2.userid#", "backendName": "x-yop-oauth2-userid", "location": "HEADER"}], "path": "/app/detail", "method": "GET", "connectionTimeout": 1000, "readTimeout": 3000}}}}, "components": {"schemas": {"PluginUserRefreshParam": {"required": ["refreshToken"], "type": "object", "properties": {"refreshToken": {"exclusiveMaximum": true, "exclusiveMinimum": true, "type": "string", "description": "刷新令牌"}}, "description": "插件端用户刷新参数"}, "PluginApps": {"type": "object", "properties": {"apps": {"exclusiveMaximum": true, "exclusiveMinimum": true, "type": "array", "description": "应用列表", "items": {"$ref": "#/components/schemas/PluginApp"}}}, "description": "插件端应用列表"}, "PluginUserCaptchaParam": {"type": "object", "properties": {"userType": {"exclusiveMaximum": true, "exclusiveMinimum": true, "type": "string", "description": "用户类型"}, "username": {"exclusiveMaximum": true, "exclusiveMinimum": true, "type": "string", "description": "用户名"}, "identifierType": {"exclusiveMaximum": true, "exclusiveMinimum": true, "type": "string", "description": "标识类型，邮箱或手机号"}}, "description": "插件端用户验证码参数"}, "PluginRefreshToken": {"type": "object", "properties": {"expiration": {"exclusiveMaximum": true, "exclusiveMinimum": true, "type": "string", "description": "过期时间", "format": "date-time"}, "value": {"exclusiveMaximum": true, "exclusiveMinimum": true, "type": "string", "description": "令牌值"}}, "description": "插件端刷新令牌"}, "PluginUserUnauthParam": {"required": ["accessToken"], "type": "object", "properties": {"accessToken": {"exclusiveMaximum": true, "exclusiveMinimum": true, "type": "string", "description": "访问令牌"}}, "description": "插件端用户注销参数"}, "PluginApp": {"type": "object", "properties": {"appId": {"exclusiveMaximum": true, "exclusiveMinimum": true, "type": "string", "description": "应用标识"}, "type": {"exclusiveMaximum": true, "exclusiveMinimum": true, "type": "string", "description": "应用类型"}, "name": {"exclusiveMaximum": true, "exclusiveMinimum": true, "type": "string", "description": "应用名称"}}, "description": "插件端应用"}, "PluginUserAuthParam": {"required": ["<PERSON><PERSON>a", "password", "userType", "username"], "type": "object", "properties": {"username": {"exclusiveMaximum": true, "exclusiveMinimum": true, "type": "string", "description": "用户名"}, "password": {"exclusiveMaximum": true, "exclusiveMinimum": true, "type": "string", "description": "密码", "format": "password"}, "userType": {"exclusiveMaximum": true, "exclusiveMinimum": true, "type": "string", "description": "用户类型"}, "captcha": {"exclusiveMaximum": true, "exclusiveMinimum": true, "type": "string", "description": "验证码", "format": "password"}}, "description": "插件端用户登录参数", "x-yop-apigateway-sensitive-variable": ["password", "<PERSON><PERSON>a"]}, "PluginAccessToken": {"type": "object", "properties": {"expiration": {"exclusiveMaximum": true, "exclusiveMinimum": true, "type": "string", "description": "过期时间", "format": "date-time"}, "value": {"exclusiveMaximum": true, "exclusiveMinimum": true, "type": "string", "description": "令牌值"}, "scope": {"exclusiveMaximum": true, "exclusiveMinimum": true, "type": "array", "description": "授权范围", "items": {"exclusiveMaximum": true, "exclusiveMinimum": true, "type": "string", "description": "授权范围", "format": "string"}}, "refreshToken": {"$ref": "#/components/schemas/PluginRefreshToken"}}, "description": "插件端访问令牌"}, "PluginUserCaptcha": {"type": "object", "properties": {"identifier": {"exclusiveMaximum": true, "exclusiveMinimum": true, "type": "string", "description": "标识"}}, "description": "用户验证码"}}, "securitySchemes": {"YOP-RSA2048-SHA256_plugin_user_auth": {"type": "<PERSON><PERSON><PERSON><PERSON>", "name": "Authorization", "in": "header", "x-yop-apigateway-auth-type": "YOP-RSA2048-SHA256", "x-yop-apigateway-auth-authority": [], "x-yop-apigateway-need-encrypt": false}, "YOP-RSA2048-SHA256_plugin_user_unauth": {"type": "<PERSON><PERSON><PERSON><PERSON>", "name": "Authorization", "in": "header", "x-yop-apigateway-auth-type": "YOP-RSA2048-SHA256", "x-yop-apigateway-auth-authority": [], "x-yop-apigateway-need-encrypt": false}, "YOP-OAUTH2_plugin_app_list": {"type": "<PERSON><PERSON><PERSON><PERSON>", "name": "Authorization", "in": "header", "x-yop-apigateway-auth-type": "YOP-OAUTH2", "x-yop-apigateway-oauth2-scope": ["write:all", "read:all"]}, "YOP-RSA2048-SHA256_plugin_user_captcha": {"type": "<PERSON><PERSON><PERSON><PERSON>", "name": "Authorization", "in": "header", "x-yop-apigateway-auth-type": "YOP-RSA2048-SHA256", "x-yop-apigateway-auth-authority": [], "x-yop-apigateway-need-encrypt": false}, "YOP-RSA2048-SHA256": {"type": "<PERSON><PERSON><PERSON><PERSON>", "name": "Authorization", "in": "header", "x-yop-apigateway-auth-type": "YOP-RSA2048-SHA256"}, "YOP-HMAC-AES128": {"type": "<PERSON><PERSON><PERSON><PERSON>", "name": "Authorization", "in": "header", "x-yop-apigateway-auth-type": "YOP-HMAC-AES128"}, "YOP-RSA2048-SHA256_plugin_user_refresh": {"type": "<PERSON><PERSON><PERSON><PERSON>", "name": "Authorization", "in": "header", "x-yop-apigateway-auth-type": "YOP-RSA2048-SHA256", "x-yop-apigateway-auth-authority": [], "x-yop-apigateway-need-encrypt": false}, "YOP-OAUTH2_plugin_app_detail": {"type": "<PERSON><PERSON><PERSON><PERSON>", "name": "Authorization", "in": "header", "x-yop-apigateway-auth-type": "YOP-OAUTH2", "x-yop-apigateway-oauth2-scope": ["write:all", "read:all"]}, "YOP-OAUTH2_yop": {"type": "<PERSON><PERSON><PERSON><PERSON>", "name": "Authorization", "in": "header", "x-yop-apigateway-auth-type": "YOP-OAUTH2"}, "YOP-OAUTH2_plugin_user_captcha": {"type": "<PERSON><PERSON><PERSON><PERSON>", "name": "Authorization", "in": "header", "x-yop-apigateway-auth-type": "YOP-OAUTH2", "x-yop-apigateway-oauth2-scope": ["write:all", "read:all"]}}}, "x-yop-apigateway-group": "yop"}