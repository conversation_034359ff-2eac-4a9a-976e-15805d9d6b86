{"swagger": "2.0", "info": {"description": "This is a sample server Petstore server.", "version": "1.0.0", "title": "Sample App", "termsOfService": "https://open.yeepay.com/terms/", "contact": {"email": "<EMAIL>"}, "license": {"name": "Apache 2.0", "url": "http://www.apache.org/licenses/LICENSE-2.0.html"}}, "host": "your.host.domain", "basePath": "/v1", "tags": [{"name": "pet", "description": "Everything about your Pets", "externalDocs": {"description": "Find out more", "url": "http://swagger.io"}}, {"name": "store", "description": "Access to Petstore orders"}], "schemes": ["https"], "security": [{"basic": []}], "paths": {"/pet/order/notify": {"get": {"tags": ["pet"], "summary": "Tell Pets order status", "description": "Tell Pets order status.", "operationId": "notifyPetOrderStatus", "consumes": ["application/json", "application/xml"], "produces": ["application/json", "application/xml"], "parameters": [{"name": "order_id", "in": "query", "description": "Order id", "required": true, "type": "string", "maxLength": 100, "minLength": 1, "pattern": "\\\\X", "x-example": 123}, {"name": "price", "in": "query", "description": "Order price", "required": true, "type": "integer", "minimum": 0, "x-example": 12.34}, {"name": "status", "in": "query", "description": "Order status", "required": true, "type": "string", "default": "SUCCESS", "enum": ["SUCCESS", "FAILURE", "UNKNOWN"], "x-example": "SUCCESS"}], "responses": {"200": {"description": "Success", "schema": {"type": "array", "items": {"$ref": "#/definitions/Order"}}, "examples": {"application/json": "[{\"order_id\":\"123\",\"price\":12.34,\"status\":\"SUCCESS\"}, {\"order_id\":\"124\",\"price\":56.78,\"status\":\"SUCCESS\"}]"}}, "400": {"description": "Invalid ID supplied"}, "404": {"description": "Pet Order not found"}, "405": {"description": "Validation exception"}}, "security": [{"api_key": []}, {"petstore_auth": ["write:pets", "read:pets"]}]}}}, "securityDefinitions": {"basic": {"type": "basic"}, "api_key": {"type": "<PERSON><PERSON><PERSON><PERSON>", "name": "api_key", "in": "header"}, "petstore_auth": {"type": "oauth2", "authorizationUrl": "https://petstore.yeepay.com/api/oauth", "flow": "implicit", "scopes": {"write:pets": "modify pets in your account", "read:pets": "read your pets"}}}, "definitions": {"Order": {"type": "object", "properties": {"id": {"type": "integer", "format": "int64"}, "petId": {"type": "integer", "format": "int64"}, "quantity": {"type": "integer", "format": "int32"}, "shipDate": {"type": "string", "format": "date-time"}, "status": {"type": "string", "description": "Order Status", "enum": ["placed", "approved", "delivered"]}, "complete": {"type": "boolean", "default": false}}, "title": "Pet Order", "description": "An order for a pets from the pet store", "xml": {"name": "Order"}}, "Tag": {"type": "object", "properties": {"id": {"type": "integer", "format": "int32"}, "name": {"type": "string"}}}, "Pet": {"type": "object", "required": ["id", "name"], "properties": {"id": {"type": "integer", "format": "int32", "description": "unique identifier for the pet"}, "category": {"$ref": "#/definitions/Category"}, "name": {"type": "string"}, "photoUrls": {"type": "array", "items": {"type": "string"}}, "tags": {"type": "array", "items": {"$ref": "#/definitions/Tag"}}, "status": {"type": "string", "description": "pet status in the store"}}}, "Category": {"type": "object", "properties": {"id": {"type": "integer", "format": "int32"}, "name": {"type": "string"}}}}}